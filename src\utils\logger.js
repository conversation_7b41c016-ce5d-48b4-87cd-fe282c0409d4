const fs = require('fs');
const path = require('path');

// Criar diretório de logs se não existir
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

class Logger {
  constructor() {
    this.logFile = path.join(logsDir, 'app.log');
    this.errorFile = path.join(logsDir, 'error.log');
  }

  formatMessage(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logData = data ? ` | Data: ${JSON.stringify(data)}` : '';
    return `[${timestamp}] [${level}] ${message}${logData}\n`;
  }

  writeToFile(filename, content) {
    try {
      fs.appendFileSync(filename, content);
    } catch (error) {
      console.error('Erro ao escrever no arquivo de log:', error);
    }
  }

  info(message, data = null) {
    const logMessage = this.formatMessage('INFO', message, data);
    console.log(`\x1b[36m${logMessage.trim()}\x1b[0m`); // Cyan
    this.writeToFile(this.logFile, logMessage);
  }

  warn(message, data = null) {
    const logMessage = this.formatMessage('WARN', message, data);
    console.warn(`\x1b[33m${logMessage.trim()}\x1b[0m`); // Yellow
    this.writeToFile(this.logFile, logMessage);
  }

  error(message, data = null) {
    const logMessage = this.formatMessage('ERROR', message, data);
    console.error(`\x1b[31m${logMessage.trim()}\x1b[0m`); // Red
    this.writeToFile(this.logFile, logMessage);
    this.writeToFile(this.errorFile, logMessage);
  }

  success(message, data = null) {
    const logMessage = this.formatMessage('SUCCESS', message, data);
    console.log(`\x1b[32m${logMessage.trim()}\x1b[0m`); // Green
    this.writeToFile(this.logFile, logMessage);
  }

  debug(message, data = null) {
    if (process.env.NODE_ENV === 'development') {
      const logMessage = this.formatMessage('DEBUG', message, data);
      console.log(`\x1b[35m${logMessage.trim()}\x1b[0m`); // Magenta
      this.writeToFile(this.logFile, logMessage);
    }
  }
}

const logger = new Logger();

module.exports = { logger };
