-- Inicialização do banco PostgreSQL para UNIFORMS2
-- <PERSON>ste script é executado automaticamente na criação do container

-- Criar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Configurar timezone
SET timezone = 'UTC';

-- Criar índices para performance
-- (Serão criados automaticamente pelo Prisma, mas deixamos aqui como referência)

-- Função para gerar slugs
CREATE OR REPLACE FUNCTION generate_slug(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(
        regexp_replace(
            regexp_replace(
                unaccent(input_text), 
                '[^a-zA-Z0-9\s]', '', 'g'
            ), 
            '\s+', '-', 'g'
        )
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Função para busca full-text
CREATE OR REPLACE FUNCTION search_text(
    search_term TEXT,
    target_text TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        target_text ILIKE '%' || search_term || '%' OR
        unaccent(target_text) ILIKE '%' || unaccent(search_term) || '%' OR
        similarity(target_text, search_term) > 0.3
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Função para calcular NPS
CREATE OR REPLACE FUNCTION calculate_nps(
    promoters INTEGER,
    detractors INTEGER,
    total INTEGER
) RETURNS INTEGER AS $$
BEGIN
    IF total = 0 THEN
        RETURN 0;
    END IF;
    
    RETURN ROUND(((promoters - detractors)::DECIMAL / total::DECIMAL) * 100);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Função para classificar score NPS
CREATE OR REPLACE FUNCTION classify_nps_score(score INTEGER)
RETURNS TEXT AS $$
BEGIN
    CASE 
        WHEN score <= 6 THEN RETURN 'DETRACTOR';
        WHEN score <= 8 THEN RETURN 'NEUTRAL';
        ELSE RETURN 'PROMOTER';
    END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Função para limpar dados antigos (housekeeping)
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS VOID AS $$
BEGIN
    -- Limpar logs antigos (mais de 90 dias)
    DELETE FROM logs WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Limpar sessões expiradas
    DELETE FROM sessions WHERE expires_at < NOW();
    
    -- Atualizar estatísticas das tabelas
    ANALYZE;
    
    RAISE NOTICE 'Limpeza de dados antigos concluída';
END;
$$ LANGUAGE plpgsql;

-- Criar job para limpeza automática (requer pg_cron extension)
-- SELECT cron.schedule('cleanup-job', '0 2 * * *', 'SELECT cleanup_old_data();');

-- Configurações de performance
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Recarregar configurações
SELECT pg_reload_conf();

-- Criar usuário para monitoramento (opcional)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'monitoring') THEN
        CREATE ROLE monitoring WITH LOGIN PASSWORD 'monitoring_password';
        GRANT CONNECT ON DATABASE uniforms_survey TO monitoring;
        GRANT USAGE ON SCHEMA public TO monitoring;
        GRANT SELECT ON ALL TABLES IN SCHEMA public TO monitoring;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO monitoring;
    END IF;
END
$$;

-- Log de inicialização
INSERT INTO pg_stat_statements_reset();

-- Mensagem de sucesso
DO $$
BEGIN
    RAISE NOTICE '✅ Banco UNIFORMS2 inicializado com sucesso!';
    RAISE NOTICE '📊 Extensões criadas: uuid-ossp, pg_trgm, unaccent';
    RAISE NOTICE '🔧 Funções utilitárias criadas';
    RAISE NOTICE '⚡ Configurações de performance aplicadas';
END
$$;
