const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');
const { validate } = require('../utils/validation');
const Joi = require('joi');
const {
  processChatbotMessage,
  trainChatbot,
  getChatbotStats,
  addCustomIntent,
  processAutomationEvent,
  getScheduledTasks,
  cancelScheduledTask,
  getResponseTemplates,
  createResponseTemplate,
  updateResponseTemplate,
  deleteResponseTemplate,
  getAutomationStats,
  getAutomationLogs,
  testTemplate
} = require('../controllers/automationController');

const router = express.Router();

// Rotas públicas (para webhooks)

// Processar mensagem do chatbot
router.post('/chatbot/message',
  validate(Joi.object({
    userPhone: Joi.string().required(),
    message: Joi.string().required(),
    conversationId: Joi.string().uuid().optional()
  })),
  processChatbotMessage
);

// Processar evento de automação (webhook)
router.post('/event',
  validate(Joi.object({
    eventType: Joi.string().valid(
      'response_received',
      'conversation_resolved',
      'message_received',
      'survey_completed'
    ).required(),
    eventData: Joi.object().required()
  })),
  processAutomationEvent
);

// Rotas autenticadas
router.use(authenticate);

// Chatbot

// Obter estatísticas do chatbot
router.get('/chatbot/stats', getChatbotStats);

// Treinar chatbot
router.post('/chatbot/train',
  validate(Joi.object({
    message: Joi.string().required(),
    correctIntent: Joi.string().required(),
    userFeedback: Joi.string().optional()
  })),
  trainChatbot
);

// Adicionar intent personalizado
router.post('/chatbot/intents',
  authorize('ADMIN'),
  validate(Joi.object({
    name: Joi.string().required(),
    patterns: Joi.array().items(Joi.string()).required(),
    responses: Joi.array().items(Joi.string()).required(),
    context: Joi.string().optional(),
    actions: Joi.array().items(Joi.string()).optional()
  })),
  addCustomIntent
);

// Tarefas Agendadas

// Obter tarefas agendadas
router.get('/tasks',
  validate(Joi.object({
    status: Joi.string().valid('scheduled', 'executed', 'error', 'cancelled').optional(),
    type: Joi.string().valid('followup', 'survey', 'message').optional(),
    limit: Joi.number().integer().min(1).max(200).default(50)
  }).options({ allowUnknown: true })),
  getScheduledTasks
);

// Cancelar tarefa agendada
router.delete('/tasks/:taskId',
  validate(Joi.object({
    taskId: Joi.string().uuid().required()
  })),
  cancelScheduledTask
);

// Templates de Resposta

// Obter templates
router.get('/templates',
  validate(Joi.object({
    type: Joi.string().valid('whatsapp', 'email', 'sms').optional(),
    isActive: Joi.boolean().optional()
  }).options({ allowUnknown: true })),
  getResponseTemplates
);

// Criar template
router.post('/templates',
  authorize('ADMIN'),
  validate(Joi.object({
    name: Joi.string().required(),
    subject: Joi.string().optional(),
    message: Joi.string().required(),
    type: Joi.string().valid('whatsapp', 'email', 'sms').default('whatsapp'),
    variables: Joi.array().items(Joi.string()).default([])
  })),
  createResponseTemplate
);

// Atualizar template
router.put('/templates/:templateId',
  authorize('ADMIN'),
  validate(Joi.object({
    templateId: Joi.string().uuid().required(),
    name: Joi.string().optional(),
    subject: Joi.string().optional(),
    message: Joi.string().optional(),
    type: Joi.string().valid('whatsapp', 'email', 'sms').optional(),
    variables: Joi.array().items(Joi.string()).optional(),
    isActive: Joi.boolean().optional()
  })),
  updateResponseTemplate
);

// Deletar template
router.delete('/templates/:templateId',
  authorize('ADMIN'),
  validate(Joi.object({
    templateId: Joi.string().uuid().required()
  })),
  deleteResponseTemplate
);

// Testar template
router.post('/templates/test',
  validate(Joi.object({
    templateName: Joi.string().required(),
    testData: Joi.object().default({})
  })),
  testTemplate
);

// Estatísticas e Logs

// Obter estatísticas de automação
router.get('/stats',
  validate(Joi.object({
    period: Joi.string().valid('1d', '7d', '30d', '90d').default('7d')
  }).options({ allowUnknown: true })),
  getAutomationStats
);

// Obter logs de automação
router.get('/logs',
  validate(Joi.object({
    ruleName: Joi.string().optional(),
    actionType: Joi.string().optional(),
    limit: Joi.number().integer().min(1).max(500).default(100)
  }).options({ allowUnknown: true })),
  getAutomationLogs
);

// Rotas administrativas

// Configurar regra de automação personalizada
router.post('/rules',
  authorize('ADMIN'),
  validate(Joi.object({
    name: Joi.string().required(),
    trigger: Joi.string().valid(
      'response_received',
      'conversation_resolved',
      'message_received',
      'survey_completed'
    ).required(),
    conditions: Joi.array().items(
      Joi.object({
        field: Joi.string().required(),
        operator: Joi.string().valid('==', '!=', '>', '<', '>=', '<=', 'contains', 'outside_business_hours').required(),
        value: Joi.any().required()
      })
    ).required(),
    actions: Joi.array().items(
      Joi.object({
        type: Joi.string().valid(
          'schedule_followup',
          'schedule_survey',
          'send_message',
          'escalate',
          'notify_team'
        ).required(),
        delay: Joi.string().optional(),
        template: Joi.string().optional(),
        priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').optional(),
        to: Joi.string().optional(),
        team: Joi.string().optional(),
        message: Joi.string().optional()
      })
    ).required(),
    isActive: Joi.boolean().default(true)
  })),
  async (req, res) => {
    try {
      const { name, ...ruleData } = req.body;
      const automationService = require('../services/automationService');
      
      automationService.addCustomRule(name, ruleData);
      
      res.json({
        message: 'Regra de automação criada com sucesso',
        rule: { name, ...ruleData }
      });
    } catch (error) {
      logger.error('Erro ao criar regra de automação:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Obter configuração atual de automação
router.get('/config',
  authorize('ADMIN'),
  async (req, res) => {
    try {
      const automationService = require('../services/automationService');
      const chatbotService = require('../services/chatbotService');
      
      const config = {
        automation: automationService.getStats(),
        chatbot: chatbotService.getStats(),
        businessHours: {
          weekdays: { start: '08:00', end: '18:00' },
          saturday: { start: '08:00', end: '12:00' },
          sunday: 'closed'
        }
      };
      
      res.json(config);
    } catch (error) {
      logger.error('Erro ao obter configuração:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Simular evento para teste
router.post('/simulate',
  authorize('ADMIN'),
  validate(Joi.object({
    eventType: Joi.string().required(),
    eventData: Joi.object().required(),
    dryRun: Joi.boolean().default(true)
  })),
  async (req, res) => {
    try {
      const { eventType, eventData, dryRun } = req.body;
      const automationService = require('../services/automationService');
      
      if (dryRun) {
        // Simular sem executar ações
        res.json({
          message: 'Simulação executada (dry run)',
          eventType,
          eventData,
          note: 'Nenhuma ação foi executada - apenas simulação'
        });
      } else {
        // Executar realmente
        await automationService.processEvent(eventType, eventData);
        res.json({
          message: 'Evento processado com sucesso',
          eventType,
          eventData
        });
      }
    } catch (error) {
      logger.error('Erro na simulação:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Relatório de performance de automação
router.get('/performance',
  authorize('ADMIN'),
  validate(Joi.object({
    period: Joi.string().valid('7d', '30d', '90d').default('30d')
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const { period } = req.query;
      const database = require('../config/database');
      
      // Calcular período
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90 }[period] || 30;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      // Estatísticas de performance
      const [
        taskPerformance,
        rulePerformance,
        responseTimeStats
      ] = await Promise.all([
        database.getClient().scheduledTask.groupBy({
          by: ['type'],
          where: { createdAt: { gte: startDate } },
          _count: { id: true },
          _avg: {
            // Calcular tempo médio entre criação e execução
          }
        }),
        database.getClient().automationLog.groupBy({
          by: ['ruleName'],
          where: { executedAt: { gte: startDate } },
          _count: { id: true }
        }),
        database.getClient().$queryRaw`
          SELECT 
            AVG(EXTRACT(EPOCH FROM (executed_at - created_at))) as avg_execution_time,
            COUNT(*) as total_tasks
          FROM scheduled_tasks 
          WHERE created_at >= ${startDate} 
            AND status = 'executed'
        `
      ]);

      res.json({
        period: { start: startDate, end: now, days },
        performance: {
          tasks: {
            byType: taskPerformance,
            avgExecutionTime: responseTimeStats[0]?.avg_execution_time || 0,
            totalExecuted: responseTimeStats[0]?.total_tasks || 0
          },
          rules: {
            byRule: rulePerformance,
            totalExecutions: rulePerformance.reduce((sum, rule) => sum + rule._count.id, 0)
          }
        }
      });
    } catch (error) {
      logger.error('Erro no relatório de performance:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

module.exports = router;
