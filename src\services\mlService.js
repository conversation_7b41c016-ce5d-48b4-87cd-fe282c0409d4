const database = require('../config/database');
const { logger } = require('../utils/logger');

class MLService {
  constructor() {
    this.models = {
      satisfactionPredictor: null,
      sentimentAnalyzer: null,
      churnPredictor: null
    };
    this.isInitialized = false;
  }

  // Inicializar modelos de ML
  async initialize() {
    try {
      logger.info('Inicializando modelos de Machine Learning...');
      
      // Carregar modelos pré-treinados ou treinar novos
      await this.loadOrTrainModels();
      
      this.isInitialized = true;
      logger.info('✅ Modelos de ML inicializados com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar modelos de ML:', error);
      // Continuar sem ML se houver erro
      this.isInitialized = false;
    }
  }

  // Carregar ou treinar modelos
  async loadOrTrainModels() {
    // Por enquanto, usar algoritmos simples baseados em regras
    // Em produção, integrar com TensorFlow.js ou APIs de ML
    
    this.models.satisfactionPredictor = {
      predict: this.predictSatisfaction.bind(this),
      confidence: 0.75
    };

    this.models.sentimentAnalyzer = {
      analyze: this.analyzeSentiment.bind(this),
      confidence: 0.70
    };

    this.models.churnPredictor = {
      predict: this.predictChurn.bind(this),
      confidence: 0.65
    };
  }

  // Predizer satisfação baseado em dados históricos
  async predictSatisfaction(customerData) {
    try {
      const {
        userPhone,
        previousResponses = [],
        conversationHistory = [],
        demographics = {}
      } = customerData;

      // Buscar histórico do cliente
      const responses = await database.getClient().response.findMany({
        where: { userPhone },
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          survey: {
            select: { title: true }
          }
        }
      });

      if (responses.length === 0) {
        return {
          predictedScore: 7, // Neutro para novos clientes
          confidence: 0.3,
          factors: ['Novo cliente - sem histórico'],
          recommendation: 'Coletar mais dados para melhor predição'
        };
      }

      // Calcular tendência de satisfação
      const scores = responses.map(r => r.score);
      const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
      const trend = this.calculateTrend(scores);
      
      // Analisar padrões temporais
      const temporalPattern = this.analyzeTemporalPattern(responses);
      
      // Analisar feedback textual
      const sentimentScore = await this.analyzeFeedbackSentiment(responses);
      
      // Calcular score predito
      let predictedScore = avgScore;
      
      // Ajustar baseado na tendência
      if (trend > 0.5) {
        predictedScore += 1; // Tendência positiva
      } else if (trend < -0.5) {
        predictedScore -= 1; // Tendência negativa
      }
      
      // Ajustar baseado no sentimento
      predictedScore += sentimentScore * 0.5;
      
      // Normalizar entre 0-10
      predictedScore = Math.max(0, Math.min(10, Math.round(predictedScore)));
      
      // Calcular confiança
      const confidence = this.calculateConfidence(responses.length, trend, sentimentScore);
      
      // Identificar fatores principais
      const factors = this.identifyFactors(responses, trend, sentimentScore, temporalPattern);
      
      // Gerar recomendação
      const recommendation = this.generateRecommendation(predictedScore, factors);

      return {
        predictedScore,
        confidence,
        factors,
        recommendation,
        historicalAverage: avgScore,
        trend,
        sentimentScore,
        temporalPattern
      };
    } catch (error) {
      logger.error('Erro na predição de satisfação:', error);
      return {
        predictedScore: 7,
        confidence: 0.1,
        factors: ['Erro na análise'],
        recommendation: 'Verificar dados do cliente'
      };
    }
  }

  // Analisar sentimento do feedback
  async analyzeFeedbackSentiment(responses) {
    const feedbacks = responses
      .filter(r => r.feedback && r.feedback.trim().length > 0)
      .map(r => r.feedback);

    if (feedbacks.length === 0) return 0;

    let totalSentiment = 0;
    let count = 0;

    for (const feedback of feedbacks) {
      const sentiment = this.analyzeSentiment(feedback);
      totalSentiment += sentiment.score;
      count++;
    }

    return count > 0 ? totalSentiment / count : 0;
  }

  // Analisar sentimento de texto (algoritmo simples)
  analyzeSentiment(text) {
    if (!text || typeof text !== 'string') {
      return { score: 0, confidence: 0, sentiment: 'neutral' };
    }

    const positiveWords = [
      'bom', 'ótimo', 'excelente', 'maravilhoso', 'perfeito', 'satisfeito',
      'feliz', 'contente', 'agradecido', 'recomendo', 'adorei', 'amei',
      'fantástico', 'incrível', 'sensacional', 'top', 'show', 'legal'
    ];

    const negativeWords = [
      'ruim', 'péssimo', 'horrível', 'terrível', 'insatisfeito', 'chateado',
      'irritado', 'decepcionado', 'frustrado', 'raiva', 'ódio', 'detesto',
      'problema', 'erro', 'falha', 'demora', 'lento', 'caro', 'difícil'
    ];

    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) {
        positiveCount++;
      }
      if (negativeWords.some(nw => word.includes(nw))) {
        negativeCount++;
      }
    });

    const totalWords = words.length;
    const sentimentScore = (positiveCount - negativeCount) / Math.max(totalWords, 1);
    
    let sentiment = 'neutral';
    if (sentimentScore > 0.1) sentiment = 'positive';
    else if (sentimentScore < -0.1) sentiment = 'negative';

    return {
      score: sentimentScore,
      confidence: Math.min(0.8, (positiveCount + negativeCount) / totalWords),
      sentiment,
      positiveWords: positiveCount,
      negativeWords: negativeCount
    };
  }

  // Calcular tendência dos scores
  calculateTrend(scores) {
    if (scores.length < 2) return 0;

    let trend = 0;
    for (let i = 1; i < scores.length; i++) {
      trend += scores[i] - scores[i - 1];
    }

    return trend / (scores.length - 1);
  }

  // Analisar padrão temporal
  analyzeTemporalPattern(responses) {
    if (responses.length < 3) return 'insufficient_data';

    const intervals = [];
    for (let i = 1; i < responses.length; i++) {
      const interval = new Date(responses[i - 1].createdAt) - new Date(responses[i].createdAt);
      intervals.push(interval / (1000 * 60 * 60 * 24)); // dias
    }

    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;

    if (avgInterval < 7) return 'frequent'; // Menos de 1 semana
    if (avgInterval < 30) return 'regular'; // Menos de 1 mês
    if (avgInterval < 90) return 'occasional'; // Menos de 3 meses
    return 'rare'; // Mais de 3 meses
  }

  // Calcular confiança da predição
  calculateConfidence(responseCount, trend, sentimentScore) {
    let confidence = 0.3; // Base

    // Mais respostas = maior confiança
    confidence += Math.min(0.4, responseCount * 0.05);

    // Tendência clara = maior confiança
    confidence += Math.min(0.2, Math.abs(trend) * 0.1);

    // Sentimento claro = maior confiança
    confidence += Math.min(0.1, Math.abs(sentimentScore) * 0.2);

    return Math.min(0.95, confidence);
  }

  // Identificar fatores principais
  identifyFactors(responses, trend, sentimentScore, temporalPattern) {
    const factors = [];

    if (responses.length >= 5) {
      factors.push(`Histórico de ${responses.length} respostas`);
    }

    if (trend > 0.5) {
      factors.push('Tendência de melhoria na satisfação');
    } else if (trend < -0.5) {
      factors.push('Tendência de declínio na satisfação');
    }

    if (sentimentScore > 0.2) {
      factors.push('Feedback textual positivo');
    } else if (sentimentScore < -0.2) {
      factors.push('Feedback textual negativo');
    }

    switch (temporalPattern) {
      case 'frequent':
        factors.push('Cliente muito ativo (respostas frequentes)');
        break;
      case 'regular':
        factors.push('Cliente engajado (respostas regulares)');
        break;
      case 'occasional':
        factors.push('Cliente ocasional');
        break;
      case 'rare':
        factors.push('Cliente pouco ativo');
        break;
    }

    const avgScore = responses.reduce((sum, r) => sum + r.score, 0) / responses.length;
    if (avgScore >= 9) {
      factors.push('Histórico de alta satisfação');
    } else if (avgScore <= 6) {
      factors.push('Histórico de baixa satisfação');
    }

    return factors.length > 0 ? factors : ['Análise baseada em dados limitados'];
  }

  // Gerar recomendação
  generateRecommendation(predictedScore, factors) {
    if (predictedScore <= 6) {
      return 'AÇÃO URGENTE: Cliente em risco de insatisfação. Contato proativo recomendado.';
    } else if (predictedScore <= 8) {
      return 'ATENÇÃO: Monitorar cliente e buscar oportunidades de melhoria.';
    } else {
      return 'OPORTUNIDADE: Cliente satisfeito, potencial promotor da marca.';
    }
  }

  // Predizer churn (probabilidade de abandono)
  async predictChurn(customerData) {
    try {
      const { userPhone } = customerData;

      // Buscar dados do cliente
      const [responses, conversations] = await Promise.all([
        database.getClient().response.findMany({
          where: { userPhone },
          orderBy: { createdAt: 'desc' },
          take: 20
        }),
        database.getClient().conversation.findMany({
          where: { userPhone },
          orderBy: { createdAt: 'desc' },
          take: 10
        })
      ]);

      if (responses.length === 0) {
        return {
          churnProbability: 0.5,
          confidence: 0.2,
          factors: ['Novo cliente'],
          recommendation: 'Monitorar engajamento inicial'
        };
      }

      let churnScore = 0;
      const factors = [];

      // Fator 1: Satisfação declinante
      const recentScores = responses.slice(0, 5).map(r => r.score);
      const olderScores = responses.slice(5, 10).map(r => r.score);
      
      if (recentScores.length > 0 && olderScores.length > 0) {
        const recentAvg = recentScores.reduce((a, b) => a + b, 0) / recentScores.length;
        const olderAvg = olderScores.reduce((a, b) => a + b, 0) / olderScores.length;
        
        if (recentAvg < olderAvg - 1) {
          churnScore += 0.3;
          factors.push('Declínio na satisfação');
        }
      }

      // Fator 2: Baixa satisfação recente
      const avgRecentScore = recentScores.reduce((a, b) => a + b, 0) / recentScores.length;
      if (avgRecentScore <= 6) {
        churnScore += 0.4;
        factors.push('Baixa satisfação recente');
      }

      // Fator 3: Redução na frequência de interação
      const now = new Date();
      const lastResponse = new Date(responses[0].createdAt);
      const daysSinceLastResponse = (now - lastResponse) / (1000 * 60 * 60 * 24);
      
      if (daysSinceLastResponse > 90) {
        churnScore += 0.3;
        factors.push('Longo período sem interação');
      }

      // Fator 4: Conversas não resolvidas
      const unresolvedConversations = conversations.filter(c => 
        c.status !== 'RESOLVED' && c.status !== 'CLOSED'
      ).length;
      
      if (unresolvedConversations > 0) {
        churnScore += 0.2;
        factors.push('Conversas não resolvidas');
      }

      // Fator 5: Feedback negativo
      const negativeFeedback = responses.filter(r => 
        r.feedback && this.analyzeSentiment(r.feedback).sentiment === 'negative'
      ).length;
      
      if (negativeFeedback > responses.length * 0.3) {
        churnScore += 0.2;
        factors.push('Feedback frequentemente negativo');
      }

      const churnProbability = Math.min(0.95, churnScore);
      const confidence = Math.min(0.9, responses.length * 0.05 + 0.3);

      let recommendation;
      if (churnProbability >= 0.7) {
        recommendation = 'CRÍTICO: Risco alto de churn. Intervenção imediata necessária.';
      } else if (churnProbability >= 0.4) {
        recommendation = 'ATENÇÃO: Risco moderado de churn. Ações preventivas recomendadas.';
      } else {
        recommendation = 'ESTÁVEL: Baixo risco de churn. Manter qualidade do serviço.';
      }

      return {
        churnProbability,
        confidence,
        factors: factors.length > 0 ? factors : ['Cliente estável'],
        recommendation,
        riskLevel: churnProbability >= 0.7 ? 'HIGH' : churnProbability >= 0.4 ? 'MEDIUM' : 'LOW'
      };
    } catch (error) {
      logger.error('Erro na predição de churn:', error);
      return {
        churnProbability: 0.5,
        confidence: 0.1,
        factors: ['Erro na análise'],
        recommendation: 'Verificar dados do cliente'
      };
    }
  }

  // Analisar padrões de satisfação por segmento
  async analyzeSegmentPatterns(segmentCriteria = {}) {
    try {
      const {
        department,
        timeRange = '30d',
        minResponses = 5
      } = segmentCriteria;

      // Calcular data de início
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90, '365d': 365 }[timeRange] || 30;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      // Buscar respostas do período
      const responses = await database.getClient().response.findMany({
        where: {
          createdAt: { gte: startDate },
          ...(department && {
            survey: {
              // Assumindo que surveys têm departamento
              title: { contains: department }
            }
          })
        },
        include: {
          survey: {
            select: { title: true, id: true }
          }
        }
      });

      // Agrupar por cliente
      const customerGroups = {};
      responses.forEach(response => {
        if (!customerGroups[response.userPhone]) {
          customerGroups[response.userPhone] = [];
        }
        customerGroups[response.userPhone].push(response);
      });

      // Analisar apenas clientes com respostas suficientes
      const qualifiedCustomers = Object.entries(customerGroups)
        .filter(([_, responses]) => responses.length >= minResponses);

      if (qualifiedCustomers.length === 0) {
        return {
          totalCustomers: 0,
          patterns: [],
          insights: ['Dados insuficientes para análise de padrões']
        };
      }

      // Analisar padrões
      const patterns = [];
      let promoters = 0;
      let detractors = 0;
      let improving = 0;
      let declining = 0;

      for (const [userPhone, customerResponses] of qualifiedCustomers) {
        const scores = customerResponses.map(r => r.score);
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
        const trend = this.calculateTrend(scores);

        if (avgScore >= 9) promoters++;
        if (avgScore <= 6) detractors++;
        if (trend > 0.5) improving++;
        if (trend < -0.5) declining++;

        patterns.push({
          userPhone,
          avgScore,
          trend,
          responseCount: customerResponses.length,
          classification: avgScore >= 9 ? 'promoter' : avgScore <= 6 ? 'detractor' : 'neutral'
        });
      }

      // Gerar insights
      const insights = [];
      const totalCustomers = qualifiedCustomers.length;

      insights.push(`${promoters} promotores (${Math.round(promoters/totalCustomers*100)}%)`);
      insights.push(`${detractors} detratores (${Math.round(detractors/totalCustomers*100)}%)`);
      
      if (improving > declining) {
        insights.push(`Tendência positiva: ${improving} clientes melhorando vs ${declining} piorando`);
      } else if (declining > improving) {
        insights.push(`Tendência negativa: ${declining} clientes piorando vs ${improving} melhorando`);
      }

      return {
        totalCustomers,
        patterns,
        insights,
        summary: {
          promoters,
          detractors,
          improving,
          declining,
          promoterRate: promoters / totalCustomers,
          detractorRate: detractors / totalCustomers
        }
      };
    } catch (error) {
      logger.error('Erro na análise de padrões por segmento:', error);
      return {
        totalCustomers: 0,
        patterns: [],
        insights: ['Erro na análise de padrões']
      };
    }
  }

  // Verificar se ML está disponível
  isAvailable() {
    return this.isInitialized;
  }

  // Obter status dos modelos
  getModelsStatus() {
    return {
      initialized: this.isInitialized,
      models: Object.keys(this.models).map(name => ({
        name,
        available: !!this.models[name],
        confidence: this.models[name]?.confidence || 0
      }))
    };
  }
}

module.exports = new MLService();
