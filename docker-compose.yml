# Docker Compose para UNIFORMS2

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: uniforms-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: uniforms_survey
      POSTGRES_USER: uniforms_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-uniforms_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - uniforms-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U uniforms_user -d uniforms_survey"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Redis para cache e filas
  redis:
    image: redis:7-alpine
    container_name: uniforms-redis
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - uniforms-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Evolution API para WhatsApp
  evolution-api:
    image: atendai/evolution-api:latest
    container_name: uniforms-evolution
    restart: unless-stopped
    environment:
      - SERVER_URL=http://localhost:8080
      - AUTHENTICATION_API_KEY=uniforms-evolution-key
      - DATABASE_ENABLED=true
      - DATABASE_CONNECTION_URI=postgresql://uniforms_user:${POSTGRES_PASSWORD:-uniforms_password}@postgres:5432/uniforms_survey
      - DATABASE_PROVIDER=postgresql
      - REDIS_ENABLED=true
      - REDIS_URI=redis://redis:6379
      - WEBHOOK_GLOBAL_URL=http://backend:3000/webhooks/whatsapp
      - WEBHOOK_GLOBAL_ENABLED=true
    ports:
      - "8080:8080"
    networks:
      - uniforms-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - evolution_instances:/evolution/instances

  # Backend Node.js
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
      args:
        - NODE_ENV=${NODE_ENV:-production}
    container_name: uniforms-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=3000
      - DATABASE_URL=postgresql://uniforms_user:${POSTGRES_PASSWORD:-uniforms_password}@postgres:5432/uniforms_survey
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-this}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - WHATSAPP_API_URL=http://evolution-api:8080
      - WHATSAPP_API_KEY=${WHATSAPP_API_KEY:-uniforms-evolution-key}
      - WHATSAPP_INSTANCE_NAME=${WHATSAPP_INSTANCE_NAME:-uniforms-main}
      # - REDIS_URL=redis://redis:6379
      - N8N_WEBHOOK_URL=http://n8n:5678
      - N8N_DETRACTOR_WEBHOOK=/webhook/detractor
      - N8N_DEPARTMENT_WEBHOOK=/webhook/department
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3002}
      - BACKEND_URL=${BACKEND_URL:-http://localhost:3000}
    ports:
      - "${BACKEND_PORT:-3000}:3000"
    networks:
      - uniforms-network
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./docker/backend/wait-for-it.sh:/app/wait-for-it.sh
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Frontend React (opcional, para produção)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.simple
    container_name: uniforms-frontend
    restart: unless-stopped
    ports:
      - "3002:80"
    networks:
      - uniforms-network
    depends_on:
      - backend

  # n8n para automação (opcional)
  n8n:
    image: n8nio/n8n:latest
    container_name: uniforms-n8n
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=uniforms123
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678
      - GENERIC_TIMEZONE=America/Sao_Paulo
    ports:
      - "5678:5678"
    networks:
      - uniforms-network
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres

  # Nginx como proxy reverso (opcional)
  nginx:
    image: nginx:alpine
    container_name: uniforms-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - uniforms-network
    depends_on:
      - backend
      - frontend
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  evolution_instances:
    driver: local
  n8n_data:
    driver: local

networks:
  uniforms-network:
    driver: bridge
