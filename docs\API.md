# Documentação da API - UNIFORMS2 Survey System

## 🔐 Autenticação

Todas as rotas protegidas requerem um token JWT no header:
```
Authorization: Bearer <token>
```

### Endpoints de Autenticação

#### POST /api/auth/login
Realizar login no sistema.

**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "message": "Login realizado com sucesso",
  "user": {
    "id": "uuid",
    "name": "Administrador",
    "email": "<EMAIL>",
    "role": "ADMIN"
  },
  "token": "jwt-token"
}
```

#### POST /api/auth/register
Registrar novo usuário.

**Body:**
```json
{
  "name": "Novo Usuário",
  "email": "<EMAIL>",
  "password": "senha123",
  "role": "USER"
}
```

## 📊 Pesquisas

### GET /api/surveys
Listar pesquisas do usuário.

**Query Parameters:**
- `page` (number): Página (padrão: 1)
- `limit` (number): Itens por página (padrão: 10)
- `search` (string): Buscar por título/descrição
- `isActive` (boolean): Filtrar por status ativo

**Response:**
```json
{
  "surveys": [
    {
      "id": "uuid",
      "title": "Pesquisa de Satisfação",
      "description": "Descrição da pesquisa",
      "questions": [...],
      "isActive": true,
      "responsesCount": 25,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  }
}
```

### POST /api/surveys
Criar nova pesquisa.

**Body:**
```json
{
  "title": "Nova Pesquisa",
  "description": "Descrição opcional",
  "questions": [
    {
      "id": 1,
      "type": "nps",
      "question": "De 0 a 10, o quanto você recomendaria nossos serviços?",
      "required": true
    },
    {
      "id": 2,
      "type": "text",
      "question": "Deixe seu comentário:",
      "required": false
    }
  ],
  "isActive": true
}
```

**Response:**
```json
{
  "message": "Pesquisa criada com sucesso",
  "survey": {...},
  "links": {
    "whatsapp": "https://wa.me/5511999999999?text=IniciarPesquisa_uuid",
    "html": "http://localhost:3000/survey/uuid",
    "qrCodes": {
      "whatsapp": "data:image/png;base64,...",
      "html": "data:image/png;base64,..."
    }
  }
}
```

### GET /api/surveys/:id/stats
Obter estatísticas da pesquisa.

**Response:**
```json
{
  "survey": {
    "id": "uuid",
    "title": "Pesquisa de Satisfação",
    "isActive": true
  },
  "stats": {
    "totalResponses": 100,
    "promoters": 60,
    "neutrals": 25,
    "detractors": 15,
    "npsScore": 45,
    "averageScore": 7.8,
    "responseRate": {
      "promoters": 60,
      "neutrals": 25,
      "detractors": 15
    }
  },
  "recentResponses": [...]
}
```

## 📱 WhatsApp

### GET /api/whatsapp/instances
Listar instâncias WhatsApp.

**Response:**
```json
{
  "instances": [
    {
      "id": "uuid",
      "instanceName": "uniforms-main",
      "apiUrl": "http://localhost:8080",
      "isActive": true,
      "status": "open",
      "connected": true,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### POST /api/whatsapp/instances
Criar nova instância WhatsApp.

**Body:**
```json
{
  "instanceName": "nova-instancia",
  "webhookUrl": "http://localhost:3000/webhooks/whatsapp"
}
```

### POST /api/whatsapp/send-survey
Enviar pesquisa via WhatsApp.

**Body:**
```json
{
  "instanceName": "uniforms-main",
  "number": "5511999999999",
  "surveyId": "uuid-da-pesquisa"
}
```

### POST /api/whatsapp/send-bulk-survey
Enviar pesquisa em lote.

**Body:**
```json
{
  "instanceName": "uniforms-main",
  "numbers": ["5511999999999", "5511888888888"],
  "surveyId": "uuid-da-pesquisa",
  "delay": 2000
}
```

## 💬 Conversas

### GET /api/conversations
Listar conversas.

**Query Parameters:**
- `page`, `limit`: Paginação
- `status`: OPEN, IN_PROGRESS, RESOLVED, CLOSED
- `priority`: LOW, NORMAL, HIGH, URGENT
- `department`: Nome do departamento
- `search`: Buscar por telefone/departamento

**Response:**
```json
{
  "conversations": [
    {
      "id": "uuid",
      "userPhone": "5511999999999",
      "department": "Suporte",
      "priority": "HIGH",
      "status": "OPEN",
      "messagesCount": 5,
      "lastMessage": {
        "text": "Última mensagem...",
        "timestamp": "2024-01-01T00:00:00Z",
        "type": "received"
      },
      "unreadCount": 2,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {...}
}
```

### POST /api/conversations/:id/messages
Enviar mensagem na conversa.

**Body:**
```json
{
  "message": "Olá! Como posso ajudá-lo?"
}
```

### PATCH /api/conversations/:id/status
Atualizar status da conversa.

**Body:**
```json
{
  "status": "RESOLVED"
}
```

## 🏢 Departamentos

### GET /api/departments
Listar departamentos.

**Response:**
```json
{
  "departments": [
    {
      "id": "uuid",
      "name": "Suporte Técnico",
      "keywords": ["suporte", "técnico", "problema"],
      "description": "Departamento de suporte técnico",
      "isActive": true
    }
  ]
}
```

### POST /api/departments/analyze-text
Analisar texto para identificar departamento.

**Body:**
```json
{
  "text": "Estou com problema de falta de água há 3 dias",
  "includeDetails": true
}
```

**Response:**
```json
{
  "analysis": {
    "department": {
      "department": "Infraestrutura",
      "confidence": 85,
      "matchedKeywords": [
        {
          "keyword": "falta de água",
          "score": 10,
          "type": "exact"
        }
      ]
    },
    "sentiment": {
      "sentiment": "negative",
      "confidence": 70,
      "scores": {
        "positive": 0,
        "negative": 2,
        "neutral": 8
      }
    },
    "extractedInfo": {
      "priority": "high"
    }
  }
}
```

## 📈 Analytics

### GET /api/responses/analytics
Obter métricas e analytics.

**Query Parameters:**
- `surveyId`: ID da pesquisa específica
- `startDate`, `endDate`: Período personalizado
- `period`: 1d, 7d, 30d, 90d

**Response:**
```json
{
  "period": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-07T23:59:59Z"
  },
  "metrics": {
    "totalResponses": 150,
    "npsScore": 42,
    "npsTrend": +5,
    "averageScore": 7.2,
    "distribution": {
      "promoters": {
        "count": 75,
        "percentage": 50
      },
      "neutrals": {
        "count": 45,
        "percentage": 30
      },
      "detractors": {
        "count": 30,
        "percentage": 20
      }
    }
  },
  "charts": {
    "responsesByDay": [
      {
        "date": "2024-01-01",
        "responses": 25,
        "averageScore": "7.5"
      }
    ]
  }
}
```

## 🔗 Webhooks

### POST /webhooks/whatsapp
Webhook para receber mensagens do WhatsApp.

**Body (Evolution API):**
```json
{
  "instanceName": "uniforms-main",
  "data": {
    "event": "messages.upsert",
    "data": [
      {
        "key": {
          "remoteJid": "<EMAIL>",
          "fromMe": false
        },
        "message": {
          "conversation": "Olá, preciso de ajuda"
        }
      }
    ]
  }
}
```

### POST /webhooks/n8n/detractor-processed
Webhook para receber notificação de detrator processado.

**Body:**
```json
{
  "responseId": "uuid-da-resposta",
  "status": "processed",
  "agentId": "agent-a-id",
  "notes": "Cliente contatado via telefone",
  "contactAttempts": 1
}
```

### POST /webhooks/n8n/department-assigned
Webhook para notificação de direcionamento.

**Body:**
```json
{
  "conversationId": "uuid-da-conversa",
  "department": "Infraestrutura",
  "reason": "Palavra-chave 'falta de água' identificada",
  "assignedAgent": "agent-infra-id",
  "priority": "HIGH"
}
```

## ❌ Códigos de Erro

- `400` - Bad Request (dados inválidos)
- `401` - Unauthorized (não autenticado)
- `403` - Forbidden (sem permissão)
- `404` - Not Found (recurso não encontrado)
- `409` - Conflict (conflito de dados)
- `500` - Internal Server Error (erro interno)

**Formato de erro:**
```json
{
  "error": "Título do erro",
  "message": "Descrição detalhada do erro",
  "details": [
    {
      "field": "email",
      "message": "Email deve ter um formato válido"
    }
  ]
}
```

## 🔄 Rate Limiting

- **Autenticação**: 5 tentativas por 15 minutos
- **Geral**: 100 requisições por 15 minutos
- **WhatsApp**: Respeitar limites da API do WhatsApp

## 📝 Notas Importantes

1. **Tokens JWT**: Expiram em 7 dias por padrão
2. **Paginação**: Máximo 100 itens por página
3. **Upload**: Máximo 5MB por arquivo
4. **WhatsApp**: Respeitar políticas anti-spam
5. **Webhooks**: Implementar retry logic para falhas

---

Para mais detalhes, consulte o código fonte ou entre em contato com o suporte técnico.
