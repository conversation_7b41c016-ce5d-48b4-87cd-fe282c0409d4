const database = require("../config/database");
const { logger } = require("../utils/logger");
const { asyncHandler } = require("../middleware/errorHandler");
const axios = require("axios");
const gamificationService = require("../services/gamificationService");

// Listar respostas
const getResponses = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    surveyId,
    isDetractor,
    isPromoter,
    isNeutral,
    search,
    startDate,
    endDate,
  } = req.query;

  const skip = (page - 1) * limit;
  const userId = req.user.id;

  // Construir filtros
  const where = {
    survey: { userId }, // Apenas respostas de pesquisas do usuário
    ...(surveyId && { surveyId }),
    ...(isDetractor === "true" && { isDetractor: true }),
    ...(isPromoter === "true" && { isPromoter: true }),
    ...(isNeutral === "true" && { isNeutral: true }),
    ...(search && {
      OR: [
        { feedback: { contains: search, mode: "insensitive" } },
        { userPhone: { contains: search } },
      ],
    }),
    ...(startDate &&
      endDate && {
        createdAt: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      }),
  };

  const [responses, total] = await Promise.all([
    database.getClient().response.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      orderBy: { createdAt: "desc" },
      include: {
        survey: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    }),
    database.getClient().response.count({ where }),
  ]);

  res.json({
    responses,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit),
    },
  });
});

// Obter resposta específica
const getResponse = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  const response = await database.getClient().response.findFirst({
    where: {
      id,
      survey: { userId },
    },
    include: {
      survey: {
        select: {
          id: true,
          title: true,
          questions: true,
        },
      },
      conversations: {
        select: {
          id: true,
          messages: true,
          department: true,
          status: true,
          createdAt: true,
        },
      },
    },
  });

  if (!response) {
    return res.status(404).json({
      error: "Resposta não encontrada",
      message: "A resposta solicitada não foi encontrada",
    });
  }

  res.json(response);
});

// Criar resposta (via API)
const createResponse = asyncHandler(async (req, res) => {
  const { surveyId, userPhone, score, feedback, answers } = req.body;

  // Verificar se a pesquisa existe e está ativa
  const survey = await database.getClient().survey.findUnique({
    where: { id: surveyId, isActive: true },
  });

  if (!survey) {
    return res.status(404).json({
      error: "Pesquisa não encontrada",
      message: "A pesquisa não foi encontrada ou não está ativa",
    });
  }

  // Classificar resposta NPS
  let isDetractor = false;
  let isNeutral = false;
  let isPromoter = false;

  if (score !== null && score !== undefined) {
    if (score <= 6) {
      isDetractor = true;
    } else if (score <= 8) {
      isNeutral = true;
    } else {
      isPromoter = true;
    }
  }

  // Verificar se é primeira resposta do usuário
  const existingResponses = await database.getClient().response.count({
    where: { userPhone: userPhone || "unknown" },
  });
  const isFirstResponse = existingResponses === 0;

  // Criar resposta
  const response = await database.getClient().response.create({
    data: {
      surveyId,
      userPhone: userPhone || "unknown",
      score: score || null,
      feedback: feedback || null,
      isDetractor,
      isNeutral,
      isPromoter,
    },
  });

  // Processar gamificação
  let gamificationResult = null;
  try {
    gamificationResult = await gamificationService.processResponse({
      userPhone: userPhone || "unknown",
      score: score || null,
      feedback: feedback || null,
      surveyId,
      isFirstResponse,
    });
  } catch (error) {
    logger.warn("Erro na gamificação:", error);
  }

  logger.info("Nova resposta criada via API:", {
    responseId: response.id,
    surveyId,
    phone: userPhone,
    score,
    isDetractor,
    gamification: gamificationResult,
  });

  // Se for detrator, enviar para n8n
  if (isDetractor && process.env.N8N_WEBHOOK_URL) {
    try {
      await axios.post(
        `${process.env.N8N_WEBHOOK_URL}${process.env.N8N_DETRACTOR_WEBHOOK}`,
        {
          responseId: response.id,
          surveyId,
          phone: userPhone,
          score,
          feedback,
          source: "api",
        }
      );
      logger.info("Detrator enviado para n8n:", { responseId: response.id });
    } catch (error) {
      logger.error("Erro ao enviar detrator para n8n:", error);
    }
  }

  res.status(201).json({
    message: "Resposta criada com sucesso",
    response,
    gamification: gamificationResult,
  });
});

// Obter métricas e analytics
const getAnalytics = asyncHandler(async (req, res) => {
  const { surveyId, startDate, endDate, period = "7d" } = req.query;
  const userId = req.user.id;

  // Definir período se não especificado
  let dateFilter = {};
  if (startDate && endDate) {
    dateFilter = {
      createdAt: {
        gte: new Date(startDate),
        lte: new Date(endDate),
      },
    };
  } else {
    const now = new Date();
    const periodDays = {
      "1d": 1,
      "7d": 7,
      "30d": 30,
      "90d": 90,
    };
    const days = periodDays[period] || 7;
    dateFilter = {
      createdAt: {
        gte: new Date(now.getTime() - days * 24 * 60 * 60 * 1000),
      },
    };
  }

  // Filtro base
  const baseWhere = {
    survey: { userId },
    ...(surveyId && { surveyId }),
    ...dateFilter,
  };

  // Buscar métricas
  const [
    totalResponses,
    promoters,
    neutrals,
    detractors,
    avgScore,
    responsesByDay,
    topSurveys,
  ] = await Promise.all([
    // Total de respostas
    database.getClient().response.count({ where: baseWhere }),

    // Promotores
    database.getClient().response.count({
      where: { ...baseWhere, isPromoter: true },
    }),

    // Neutros
    database.getClient().response.count({
      where: { ...baseWhere, isNeutral: true },
    }),

    // Detratores
    database.getClient().response.count({
      where: { ...baseWhere, isDetractor: true },
    }),

    // Score médio
    database.getClient().response.aggregate({
      where: { ...baseWhere, score: { not: null } },
      _avg: { score: true },
    }),

    // Respostas por dia (últimos 30 dias)
    database.getClient().$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count,
        AVG(CASE WHEN score IS NOT NULL THEN score END) as avg_score
      FROM responses r
      INNER JOIN surveys s ON r.survey_id = s.id
      WHERE s.user_id = ${userId}
        AND r.created_at >= NOW() - INTERVAL '30 days'
        ${surveyId ? `AND r.survey_id = '${surveyId}'` : ""}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `,

    // Top pesquisas por número de respostas
    database.getClient().survey.findMany({
      where: {
        userId,
        responses: { some: dateFilter },
      },
      select: {
        id: true,
        title: true,
        _count: {
          select: { responses: true },
        },
      },
      orderBy: {
        responses: { _count: "desc" },
      },
      take: 5,
    }),
  ]);

  // Calcular NPS
  const npsScore =
    totalResponses > 0
      ? Math.round(((promoters - detractors) / totalResponses) * 100)
      : 0;

  // Calcular tendência NPS (comparar com período anterior)
  const previousPeriodStart = new Date(dateFilter.createdAt?.gte || new Date());
  previousPeriodStart.setTime(
    previousPeriodStart.getTime() - 7 * 24 * 60 * 60 * 1000
  );

  const [prevPromoters, prevDetractors, prevTotal] = await Promise.all([
    database.getClient().response.count({
      where: {
        ...baseWhere,
        isPromoter: true,
        createdAt: {
          gte: previousPeriodStart,
          lt: dateFilter.createdAt?.gte || new Date(),
        },
      },
    }),
    database.getClient().response.count({
      where: {
        ...baseWhere,
        isDetractor: true,
        createdAt: {
          gte: previousPeriodStart,
          lt: dateFilter.createdAt?.gte || new Date(),
        },
      },
    }),
    database.getClient().response.count({
      where: {
        ...baseWhere,
        createdAt: {
          gte: previousPeriodStart,
          lt: dateFilter.createdAt?.gte || new Date(),
        },
      },
    }),
  ]);

  const prevNpsScore =
    prevTotal > 0
      ? Math.round(((prevPromoters - prevDetractors) / prevTotal) * 100)
      : 0;

  const npsTrend = npsScore - prevNpsScore;

  res.json({
    period: {
      start:
        dateFilter.createdAt?.gte ||
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      end: dateFilter.createdAt?.lte || new Date(),
    },
    metrics: {
      totalResponses,
      npsScore,
      npsTrend,
      averageScore: avgScore._avg.score
        ? Math.round(avgScore._avg.score * 10) / 10
        : null,
      distribution: {
        promoters: {
          count: promoters,
          percentage:
            totalResponses > 0
              ? Math.round((promoters / totalResponses) * 100)
              : 0,
        },
        neutrals: {
          count: neutrals,
          percentage:
            totalResponses > 0
              ? Math.round((neutrals / totalResponses) * 100)
              : 0,
        },
        detractors: {
          count: detractors,
          percentage:
            totalResponses > 0
              ? Math.round((detractors / totalResponses) * 100)
              : 0,
        },
      },
    },
    charts: {
      responsesByDay: responsesByDay.map((row) => ({
        date: row.date,
        responses: parseInt(row.count),
        averageScore: row.avg_score
          ? parseFloat(row.avg_score).toFixed(1)
          : null,
      })),
      topSurveys: topSurveys.map((survey) => ({
        id: survey.id,
        title: survey.title,
        responses: survey._count.responses,
      })),
    },
  });
});

module.exports = {
  getResponses,
  getResponse,
  createResponse,
  getAnalytics,
};
