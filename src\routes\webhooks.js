const express = require("express");
const { handleWebhook } = require("../controllers/whatsappController");
const { logger } = require("../utils/logger");

const router = express.Router();

// Webhook para receber mensagens do WhatsApp
router.post("/whatsapp", handleWebhook);

// Webhook para teste (útil para testar integração com n8n)
router.post("/test", (req, res) => {
  logger.info("Webhook de teste recebido:", req.body);

  res.json({
    message: "Webhook de teste recebido com sucesso",
    timestamp: new Date().toISOString(),
    data: req.body,
  });
});

// Webhook para receber notificações do n8n (detratores processados)
router.post("/n8n/detractor-processed", async (req, res) => {
  try {
    const { responseId, status, agentId, notes, contactAttempts } = req.body;

    logger.info("Detrator processado pelo n8n:", {
      responseId,
      status,
      agentId,
      notes,
      contactAttempts,
    });

    const database = require("../config/database");

    // Buscar a resposta
    const response = await database.getClient().response.findUnique({
      where: { id: responseId },
      include: { survey: true },
    });

    if (response) {
      // Buscar ou criar conversa para este detrator
      let conversation = await database.getClient().conversation.findFirst({
        where: { responseId },
      });

      if (!conversation) {
        // Criar nova conversa para o detrator
        const instance = await database.getClient().whatsAppInstance.findFirst({
          where: { isActive: true },
        });

        if (instance) {
          conversation = await database.getClient().conversation.create({
            data: {
              instanceId: instance.id,
              userPhone: response.userPhone,
              responseId,
              department: "Suporte - Detratores",
              priority: "HIGH",
              status: "IN_PROGRESS",
              messages: [
                {
                  id: Date.now().toString(),
                  type: "system",
                  message: `Detrator identificado (NPS: ${response.score}). Feedback: "${response.feedback}". Atribuído ao agente ${agentId}.`,
                  timestamp: new Date().toISOString(),
                  agentId,
                  notes,
                },
              ],
            },
          });
        }
      } else {
        // Atualizar conversa existente
        const messages = Array.isArray(conversation.messages)
          ? conversation.messages
          : [];
        messages.push({
          id: Date.now().toString(),
          type: "system",
          message: `Status atualizado pelo n8n: ${status}. Notas: ${notes}`,
          timestamp: new Date().toISOString(),
          agentId,
          contactAttempts,
        });

        await database.getClient().conversation.update({
          where: { id: conversation.id },
          data: {
            messages,
            status: status === "resolved" ? "RESOLVED" : "IN_PROGRESS",
            updatedAt: new Date(),
          },
        });
      }

      logger.info("Conversa de detrator atualizada:", {
        conversationId: conversation?.id,
        responseId,
        status,
      });
    }

    res.json({
      message: "Notificação de detrator processado recebida",
      responseId,
      conversationId: conversation?.id,
    });
  } catch (error) {
    logger.error("Erro ao processar notificação de detrator:", error);
    res.status(500).json({
      error: "Erro interno",
      message: "Não foi possível processar a notificação",
    });
  }
});

// Webhook para receber notificações do n8n (direcionamento de departamento)
router.post("/n8n/department-assigned", async (req, res) => {
  try {
    const { conversationId, department, reason, assignedAgent, priority } =
      req.body;

    logger.info("Conversa direcionada pelo n8n:", {
      conversationId,
      department,
      reason,
      assignedAgent,
      priority,
    });

    const database = require("../config/database");

    // Buscar a conversa
    let conversation = null;

    if (conversationId) {
      conversation = await database.getClient().conversation.findUnique({
        where: { id: conversationId },
      });
    }

    if (conversation) {
      // Atualizar conversa existente
      const messages = Array.isArray(conversation.messages)
        ? conversation.messages
        : [];
      messages.push({
        id: Date.now().toString(),
        type: "system",
        message: `Conversa direcionada pelo n8n para ${department}. Motivo: ${reason}`,
        timestamp: new Date().toISOString(),
        assignedAgent,
        reason,
      });

      await database.getClient().conversation.update({
        where: { id: conversationId },
        data: {
          department,
          messages,
          priority: priority || conversation.priority,
          status: "IN_PROGRESS",
          updatedAt: new Date(),
        },
      });

      logger.info("Conversa atualizada pelo n8n:", {
        conversationId,
        department,
        assignedAgent,
      });
    }

    res.json({
      message: "Notificação de direcionamento recebida",
      conversationId,
      department,
      updated: !!conversation,
    });
  } catch (error) {
    logger.error("Erro ao processar direcionamento:", error);
    res.status(500).json({
      error: "Erro interno",
      message: "Não foi possível processar o direcionamento",
    });
  }
});

// Webhook genérico para receber dados do n8n
router.post("/n8n/:action", (req, res) => {
  const { action } = req.params;
  const data = req.body;

  logger.info(`Webhook n8n recebido - Ação: ${action}`, data);

  // Processar diferentes ações do n8n
  switch (action) {
    case "survey-completed":
      // Processar pesquisa completada
      break;

    case "follow-up-scheduled":
      // Processar agendamento de follow-up
      break;

    case "escalation-required":
      // Processar escalação necessária
      break;

    default:
      logger.warn("Ação n8n não reconhecida:", action);
  }

  res.json({
    message: `Webhook n8n processado - Ação: ${action}`,
    timestamp: new Date().toISOString(),
  });
});

// Webhook para receber atualizações de status de instâncias WhatsApp
router.post("/whatsapp/status/:instanceName", (req, res) => {
  const { instanceName } = req.params;
  const { status, event, data } = req.body;

  logger.info("Status da instância WhatsApp atualizado:", {
    instanceName,
    status,
    event,
  });

  // Atualizar status no banco de dados
  const database = require("../config/database");

  database
    .getClient()
    .whatsAppInstance.updateMany({
      where: { instanceName },
      data: {
        isActive: status === "connected" || status === "open",
        updatedAt: new Date(),
      },
    })
    .catch((error) => {
      logger.error("Erro ao atualizar status da instância:", error);
    });

  res.json({
    message: "Status da instância atualizado",
    instanceName,
    status,
  });
});

// Webhook para receber QR Code atualizado
router.post("/whatsapp/qrcode/:instanceName", (req, res) => {
  const { instanceName } = req.params;
  const { qrcode, status } = req.body;

  logger.info("QR Code atualizado:", { instanceName, status });

  // Aqui você pode implementar notificação em tempo real via WebSocket
  // para atualizar o QR Code no frontend

  res.json({
    message: "QR Code recebido",
    instanceName,
  });
});

// Webhook para receber confirmação de mensagem enviada
router.post("/whatsapp/message-sent/:instanceName", (req, res) => {
  const { instanceName } = req.params;
  const { messageId, status, timestamp, to } = req.body;

  logger.info("Confirmação de mensagem enviada:", {
    instanceName,
    messageId,
    status,
    to,
  });

  // Aqui você pode atualizar o status da mensagem no banco de dados

  res.json({
    message: "Confirmação de envio recebida",
    messageId,
  });
});

// Webhook para receber confirmação de mensagem lida
router.post("/whatsapp/message-read/:instanceName", (req, res) => {
  const { instanceName } = req.params;
  const { messageId, readBy, timestamp } = req.body;

  logger.info("Mensagem lida:", {
    instanceName,
    messageId,
    readBy,
    timestamp,
  });

  // Aqui você pode atualizar o status da mensagem como "lida"

  res.json({
    message: "Confirmação de leitura recebida",
    messageId,
  });
});

// Middleware para log de todos os webhooks
router.use("*", (req, res, next) => {
  logger.debug("Webhook recebido:", {
    method: req.method,
    url: req.originalUrl,
    headers: req.headers,
    body: req.body,
  });
  next();
});

// Rota 404 para webhooks não encontrados
router.use("*", (req, res) => {
  logger.warn("Webhook não encontrado:", {
    method: req.method,
    url: req.originalUrl,
  });

  res.status(404).json({
    error: "Webhook não encontrado",
    message: `Endpoint ${req.method} ${req.originalUrl} não existe`,
  });
});

module.exports = router;
