const express = require("express");
const { authenticate, authorize } = require("../middleware/auth");
const { validate } = require("../utils/validation");
const Joi = require("joi");
const {
  getInstances,
  createInstance,
  getQRCode,
  getInstanceStatus,
  sendMessage,
  sendSurvey,
  sendBulkSurvey,
  deleteInstance,
} = require("../controllers/whatsappController");

const router = express.Router();

// Todas as rotas de WhatsApp requerem autenticação
router.use(authenticate);

// Listar instâncias
router.get("/instances", getInstances);

// Criar nova instância
router.post(
  "/instances",
  validate(
    Joi.object({
      instanceName: Joi.string().min(3).max(50).required(),
      webhookUrl: Joi.string().uri().optional(),
    })
  ),
  createInstance
);

// Obter QR Code para conexão
router.get("/instances/:instanceName/qrcode", getQRCode);

// Obter status da instância
router.get("/instances/:instanceName/status", getInstanceStatus);

// Deletar instância
router.delete("/instances/:instanceName", deleteInstance);

// Enviar mensagem de texto
router.post(
  "/send-message",
  validate(
    Joi.object({
      instanceName: Joi.string().required(),
      number: Joi.string()
        .pattern(/^[1-9]\d{1,14}$/)
        .required(),
      message: Joi.string().min(1).max(4096).required(),
    })
  ),
  sendMessage
);

// Enviar pesquisa via WhatsApp
router.post(
  "/send-survey",
  validate(
    Joi.object({
      instanceName: Joi.string().required(),
      number: Joi.string()
        .pattern(/^[1-9]\d{1,14}$/)
        .required(),
      surveyId: Joi.string().uuid().required(),
    })
  ),
  sendSurvey
);

// Enviar pesquisa em lote
router.post(
  "/send-bulk-survey",
  validate(
    Joi.object({
      instanceName: Joi.string().required(),
      numbers: Joi.array()
        .items(Joi.string().pattern(/^[1-9]\d{1,14}$/))
        .min(1)
        .max(100)
        .required(),
      surveyId: Joi.string().uuid().required(),
      delay: Joi.number().min(1000).max(10000).default(2000),
    })
  ),
  sendBulkSurvey
);

// Testar conexão com API do WhatsApp
router.get("/test-connection", async (req, res) => {
  try {
    const whatsappService = require("../services/whatsappService");
    const instances = await whatsappService.listInstances();

    res.json({
      status: "connected",
      message: "Conexão com API do WhatsApp estabelecida",
      apiUrl: whatsappService.apiUrl,
      instancesCount: instances.length || 0,
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Erro ao conectar com API do WhatsApp",
      error: error.message,
    });
  }
});

// Obter informações da API
router.get("/api-info", async (req, res) => {
  try {
    const axios = require("axios");
    const whatsappService = require("../services/whatsappService");

    const response = await axios.get(`${whatsappService.apiUrl}/`, {
      headers: whatsappService.getHeaders(),
    });

    res.json({
      apiUrl: whatsappService.apiUrl,
      version: response.data.version || "unknown",
      status: "online",
    });
  } catch (error) {
    res.status(500).json({
      apiUrl: whatsappService.apiUrl,
      status: "offline",
      error: error.message,
    });
  }
});

// Validar número de telefone
router.post("/validate-number", async (req, res) => {
  try {
    const { instanceName, number } = req.body;

    if (!instanceName || !number) {
      return res.status(400).json({
        error: "Dados incompletos",
        message: "instanceName e number são obrigatórios",
      });
    }

    const axios = require("axios");
    const whatsappService = require("../services/whatsappService");

    const formattedNumber = whatsappService.formatPhoneNumber(number);

    const response = await axios.post(
      `${whatsappService.apiUrl}/chat/whatsappNumbers/${instanceName}`,
      {
        numbers: [formattedNumber],
      },
      {
        headers: whatsappService.getHeaders(instanceName),
      }
    );

    const isValid =
      response.data && response.data.length > 0 && response.data[0].exists;

    res.json({
      number: formattedNumber,
      isValid,
      exists: isValid,
      details: response.data[0] || null,
    });
  } catch (error) {
    res.status(500).json({
      error: "Erro ao validar número",
      message: error.message,
    });
  }
});

// Obter perfil do contato
router.get("/contact/:instanceName/:number", async (req, res) => {
  try {
    const { instanceName, number } = req.params;

    const axios = require("axios");
    const whatsappService = require("../services/whatsappService");

    const formattedNumber = whatsappService.formatPhoneNumber(number);

    const response = await axios.get(
      `${whatsappService.apiUrl}/chat/findContact/${instanceName}`,
      {
        params: { number: formattedNumber },
        headers: whatsappService.getHeaders(instanceName),
      }
    );

    res.json({
      number: formattedNumber,
      profile: response.data,
    });
  } catch (error) {
    res.status(500).json({
      error: "Erro ao obter perfil do contato",
      message: error.message,
    });
  }
});

// Listar chats/conversas
router.get("/chats/:instanceName", async (req, res) => {
  try {
    const { instanceName } = req.params;

    const axios = require("axios");
    const whatsappService = require("../services/whatsappService");

    const response = await axios.get(
      `${whatsappService.apiUrl}/chat/findChats/${instanceName}`,
      {
        headers: whatsappService.getHeaders(instanceName),
      }
    );

    res.json({
      instanceName,
      chats: response.data || [],
    });
  } catch (error) {
    res.status(500).json({
      error: "Erro ao listar chats",
      message: error.message,
    });
  }
});

// Obter histórico de mensagens
router.get("/messages/:instanceName/:number", async (req, res) => {
  try {
    const { instanceName, number } = req.params;
    const { limit = 50 } = req.query;

    const axios = require("axios");
    const whatsappService = require("../services/whatsappService");

    const formattedNumber = whatsappService.formatPhoneNumber(number);

    const response = await axios.get(
      `${whatsappService.apiUrl}/chat/findMessages/${instanceName}`,
      {
        params: {
          number: formattedNumber,
          limit: parseInt(limit),
        },
        headers: whatsappService.getHeaders(instanceName),
      }
    );

    res.json({
      instanceName,
      number: formattedNumber,
      messages: response.data || [],
    });
  } catch (error) {
    res.status(500).json({
      error: "Erro ao obter histórico de mensagens",
      message: error.message,
    });
  }
});

module.exports = router;
