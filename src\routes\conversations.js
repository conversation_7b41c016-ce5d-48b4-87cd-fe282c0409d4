const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');
const { validate } = require('../utils/validation');
const {
  getConversations,
  getConversation,
  sendMessage,
  updateConversationStatus,
  updateConversationPriority,
  assignDepartment,
  analyzeConversation,
  getConversationStats
} = require('../controllers/conversationController');

const router = express.Router();

// Todas as rotas de conversas requerem autenticação
router.use(authenticate);

// Obter estatísticas das conversas
router.get('/stats', getConversationStats);

// Listar conversas
router.get('/', getConversations);

// Obter conversa específica
router.get('/:id', getConversation);

// Enviar mensagem na conversa
router.post('/:id/messages', 
  validate(require('joi').object({
    message: require('joi').string().min(1).max(4096).required()
  })),
  sendMessage
);

// Atualizar status da conversa
router.patch('/:id/status', 
  validate(require('joi').object({
    status: require('joi').string().valid('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED').required()
  })),
  updateConversationStatus
);

// Atualizar prioridade da conversa
router.patch('/:id/priority', 
  validate(require('joi').object({
    priority: require('joi').string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').required()
  })),
  updateConversationPriority
);

// Atribuir departamento à conversa
router.patch('/:id/department', 
  validate(require('joi').object({
    department: require('joi').string().min(2).max(100).required()
  })),
  assignDepartment
);

// Analisar conteúdo da conversa
router.post('/:id/analyze', analyzeConversation);

// Marcar conversa como lida
router.patch('/:id/read', async (req, res) => {
  try {
    const { id } = req.params;
    const database = require('../config/database');
    const { logger } = require('../utils/logger');

    const conversation = await database.getClient().conversation.findUnique({
      where: { id }
    });

    if (!conversation) {
      return res.status(404).json({
        error: 'Conversa não encontrada',
        message: 'A conversa solicitada não foi encontrada'
      });
    }

    // Marcar todas as mensagens como lidas
    const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
    const updatedMessages = messages.map(msg => ({
      ...msg,
      read: msg.type === 'received' ? true : msg.read
    }));

    await database.getClient().conversation.update({
      where: { id },
      data: { messages: updatedMessages }
    });

    logger.info('Conversa marcada como lida:', { conversationId: id });

    res.json({
      message: 'Conversa marcada como lida',
      conversationId: id
    });

  } catch (error) {
    logger.error('Erro ao marcar conversa como lida:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível marcar a conversa como lida'
    });
  }
});

// Adicionar nota à conversa
router.post('/:id/notes', 
  validate(require('joi').object({
    note: require('joi').string().min(1).max(1000).required(),
    isPrivate: require('joi').boolean().default(true)
  })),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { note, isPrivate } = req.body;
      const userId = req.user.id;
      const database = require('../config/database');
      const { logger } = require('../utils/logger');

      const conversation = await database.getClient().conversation.findUnique({
        where: { id }
      });

      if (!conversation) {
        return res.status(404).json({
          error: 'Conversa não encontrada',
          message: 'A conversa solicitada não foi encontrada'
        });
      }

      // Adicionar nota às mensagens
      const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
      const noteMessage = {
        id: Date.now().toString(),
        type: 'note',
        message: note,
        timestamp: new Date().toISOString(),
        userId,
        userName: req.user.name,
        isPrivate
      };

      messages.push(noteMessage);

      await database.getClient().conversation.update({
        where: { id },
        data: { 
          messages,
          updatedAt: new Date()
        }
      });

      logger.info('Nota adicionada à conversa:', { 
        conversationId: id, 
        userId,
        isPrivate
      });

      res.json({
        message: 'Nota adicionada com sucesso',
        note: noteMessage
      });

    } catch (error) {
      logger.error('Erro ao adicionar nota:', error);
      res.status(500).json({
        error: 'Erro interno',
        message: 'Não foi possível adicionar a nota'
      });
    }
  }
);

// Transferir conversa para outro agente/departamento
router.post('/:id/transfer', 
  validate(require('joi').object({
    targetDepartment: require('joi').string().min(2).max(100).optional(),
    targetAgent: require('joi').string().uuid().optional(),
    reason: require('joi').string().min(5).max(500).required()
  })),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { targetDepartment, targetAgent, reason } = req.body;
      const userId = req.user.id;
      const database = require('../config/database');
      const { logger } = require('../utils/logger');

      if (!targetDepartment && !targetAgent) {
        return res.status(400).json({
          error: 'Destino obrigatório',
          message: 'Você deve especificar um departamento ou agente de destino'
        });
      }

      const conversation = await database.getClient().conversation.findUnique({
        where: { id }
      });

      if (!conversation) {
        return res.status(404).json({
          error: 'Conversa não encontrada',
          message: 'A conversa solicitada não foi encontrada'
        });
      }

      // Adicionar mensagem de transferência
      const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
      const transferMessage = {
        id: Date.now().toString(),
        type: 'transfer',
        message: `Conversa transferida. Motivo: ${reason}`,
        timestamp: new Date().toISOString(),
        userId,
        userName: req.user.name,
        transferData: {
          targetDepartment,
          targetAgent,
          reason
        }
      };

      messages.push(transferMessage);

      // Atualizar conversa
      const updateData = {
        messages,
        status: 'OPEN', // Resetar status para OPEN
        updatedAt: new Date()
      };

      if (targetDepartment) {
        updateData.department = targetDepartment;
      }

      await database.getClient().conversation.update({
        where: { id },
        data: updateData
      });

      logger.info('Conversa transferida:', { 
        conversationId: id, 
        fromUser: userId,
        targetDepartment,
        targetAgent,
        reason
      });

      res.json({
        message: 'Conversa transferida com sucesso',
        transfer: transferMessage
      });

    } catch (error) {
      logger.error('Erro ao transferir conversa:', error);
      res.status(500).json({
        error: 'Erro interno',
        message: 'Não foi possível transferir a conversa'
      });
    }
  }
);

// Obter conversas não atribuídas
router.get('/unassigned/list', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    const database = require('../config/database');

    const conversations = await database.getClient().conversation.findMany({
      where: {
        OR: [
          { department: null },
          { department: '' },
          { status: 'OPEN' }
        ]
      },
      take: parseInt(limit),
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' }
      ],
      include: {
        instance: {
          select: {
            instanceName: true
          }
        }
      }
    });

    const processedConversations = conversations.map(conv => {
      const messages = Array.isArray(conv.messages) ? conv.messages : [];
      const lastMessage = messages[messages.length - 1];
      
      return {
        ...conv,
        messagesCount: messages.length,
        lastMessage: lastMessage ? {
          text: lastMessage.message?.substring(0, 100) + (lastMessage.message?.length > 100 ? '...' : ''),
          timestamp: lastMessage.timestamp,
          type: lastMessage.type
        } : null
      };
    });

    res.json({
      conversations: processedConversations,
      count: conversations.length
    });

  } catch (error) {
    logger.error('Erro ao buscar conversas não atribuídas:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível buscar as conversas'
    });
  }
});

// Buscar conversas por número de telefone
router.get('/search/by-phone/:phone', async (req, res) => {
  try {
    const { phone } = req.params;
    const database = require('../config/database');
    const whatsappService = require('../services/whatsappService');

    const formattedPhone = whatsappService.formatPhoneNumber(phone);

    const conversations = await database.getClient().conversation.findMany({
      where: { userPhone: formattedPhone },
      orderBy: { createdAt: 'desc' },
      include: {
        instance: {
          select: {
            instanceName: true
          }
        },
        response: {
          select: {
            id: true,
            score: true,
            isDetractor: true,
            feedback: true
          }
        }
      }
    });

    res.json({
      phone: formattedPhone,
      conversations,
      count: conversations.length
    });

  } catch (error) {
    logger.error('Erro ao buscar conversas por telefone:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível buscar as conversas'
    });
  }
});

module.exports = router;
