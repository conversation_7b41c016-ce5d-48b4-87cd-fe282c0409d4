const whatsappService = require('../services/whatsappService');
const database = require('../config/database');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');

// Listar instâncias WhatsApp
const getInstances = asyncHandler(async (req, res) => {
  const instances = await database.getClient().whatsAppInstance.findMany({
    orderBy: { createdAt: 'desc' }
  });

  // Buscar status de cada instância
  const instancesWithStatus = await Promise.all(
    instances.map(async (instance) => {
      try {
        const status = await whatsappService.getInstanceStatus(instance.instanceName);
        return {
          ...instance,
          status: status.state || 'unknown',
          connected: status.state === 'open'
        };
      } catch (error) {
        return {
          ...instance,
          status: 'error',
          connected: false
        };
      }
    })
  );

  res.json({ instances: instancesWithStatus });
});

// Criar nova instância
const createInstance = asyncHandler(async (req, res) => {
  const { instanceName, webhookUrl } = req.body;

  if (!instanceName) {
    return res.status(400).json({
      error: 'Nome da instância é obrigatório',
      message: 'Você deve fornecer um nome para a instância'
    });
  }

  // Verificar se já existe
  const existingInstance = await database.getClient().whatsAppInstance.findUnique({
    where: { instanceName }
  });

  if (existingInstance) {
    return res.status(409).json({
      error: 'Instância já existe',
      message: 'Uma instância com este nome já existe'
    });
  }

  const result = await whatsappService.createInstance(instanceName, webhookUrl);

  res.status(201).json({
    message: 'Instância criada com sucesso',
    instance: result.instance,
    qrCode: result.data.qrcode
  });
});

// Obter QR Code para conexão
const getQRCode = asyncHandler(async (req, res) => {
  const { instanceName } = req.params;

  const qrData = await whatsappService.getQRCode(instanceName);

  res.json({
    instanceName,
    qrCode: qrData.qrcode,
    status: qrData.state
  });
});

// Obter status da instância
const getInstanceStatus = asyncHandler(async (req, res) => {
  const { instanceName } = req.params;

  const status = await whatsappService.getInstanceStatus(instanceName);

  res.json({
    instanceName,
    status: status.state,
    connected: status.state === 'open',
    details: status
  });
});

// Enviar mensagem de texto
const sendMessage = asyncHandler(async (req, res) => {
  const { instanceName, number, message } = req.body;

  if (!instanceName || !number || !message) {
    return res.status(400).json({
      error: 'Dados incompletos',
      message: 'instanceName, number e message são obrigatórios'
    });
  }

  const result = await whatsappService.sendTextMessage(instanceName, number, message);

  res.json({
    message: 'Mensagem enviada com sucesso',
    messageId: result.key?.id,
    details: result
  });
});

// Enviar pesquisa via WhatsApp
const sendSurvey = asyncHandler(async (req, res) => {
  const { instanceName, number, surveyId } = req.body;

  if (!instanceName || !number || !surveyId) {
    return res.status(400).json({
      error: 'Dados incompletos',
      message: 'instanceName, number e surveyId são obrigatórios'
    });
  }

  // Verificar se a pesquisa existe e pertence ao usuário
  const survey = await database.getClient().survey.findFirst({
    where: { 
      id: surveyId, 
      userId: req.user.id,
      isActive: true 
    }
  });

  if (!survey) {
    return res.status(404).json({
      error: 'Pesquisa não encontrada',
      message: 'A pesquisa não foi encontrada ou não está ativa'
    });
  }

  const result = await whatsappService.sendNPSSurvey(instanceName, number, surveyId);

  res.json({
    message: 'Pesquisa enviada com sucesso',
    surveyId,
    number,
    details: result
  });
});

// Enviar pesquisa em lote
const sendBulkSurvey = asyncHandler(async (req, res) => {
  const { instanceName, numbers, surveyId, delay = 2000 } = req.body;

  if (!instanceName || !numbers || !Array.isArray(numbers) || !surveyId) {
    return res.status(400).json({
      error: 'Dados incompletos',
      message: 'instanceName, numbers (array) e surveyId são obrigatórios'
    });
  }

  // Verificar se a pesquisa existe e pertence ao usuário
  const survey = await database.getClient().survey.findFirst({
    where: { 
      id: surveyId, 
      userId: req.user.id,
      isActive: true 
    }
  });

  if (!survey) {
    return res.status(404).json({
      error: 'Pesquisa não encontrada',
      message: 'A pesquisa não foi encontrada ou não está ativa'
    });
  }

  const results = [];
  const errors = [];

  // Enviar com delay para evitar spam
  for (let i = 0; i < numbers.length; i++) {
    const number = numbers[i];
    
    try {
      await whatsappService.sendNPSSurvey(instanceName, number, surveyId);
      results.push({ number, status: 'sent' });
      
      logger.info('Pesquisa enviada em lote:', { number, surveyId, index: i + 1 });
      
      // Delay entre envios (exceto no último)
      if (i < numbers.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
    } catch (error) {
      logger.error('Erro ao enviar pesquisa em lote:', { number, error: error.message });
      errors.push({ number, error: error.message });
    }
  }

  res.json({
    message: 'Envio em lote concluído',
    summary: {
      total: numbers.length,
      sent: results.length,
      errors: errors.length
    },
    results,
    errors
  });
});

// Deletar instância
const deleteInstance = asyncHandler(async (req, res) => {
  const { instanceName } = req.params;

  await whatsappService.deleteInstance(instanceName);

  res.json({
    message: 'Instância deletada com sucesso',
    instanceName
  });
});

// Webhook para receber mensagens do WhatsApp
const handleWebhook = asyncHandler(async (req, res) => {
  const { instanceName, data } = req.body;

  logger.debug('Webhook WhatsApp recebido:', { instanceName, eventType: data.event });

  try {
    // Processar diferentes tipos de eventos
    switch (data.event) {
      case 'messages.upsert':
        await handleIncomingMessage(instanceName, data);
        break;
        
      case 'connection.update':
        await handleConnectionUpdate(instanceName, data);
        break;
        
      case 'qrcode.updated':
        logger.info('QR Code atualizado:', { instanceName });
        break;
        
      default:
        logger.debug('Evento não processado:', { event: data.event });
    }

    res.status(200).json({ status: 'processed' });

  } catch (error) {
    logger.error('Erro ao processar webhook:', error);
    res.status(500).json({ error: 'Erro ao processar webhook' });
  }
});

// Processar mensagem recebida
async function handleIncomingMessage(instanceName, data) {
  const messages = data.data;
  
  for (const message of messages) {
    if (message.key.fromMe) continue; // Ignorar mensagens enviadas por nós
    
    const number = message.key.remoteJid.replace('@s.whatsapp.net', '');
    const messageText = message.message?.conversation || 
                       message.message?.extendedTextMessage?.text || '';

    logger.info('Mensagem recebida:', { instanceName, number, message: messageText });

    // Verificar se é resposta de pesquisa
    if (messageText.startsWith('IniciarPesquisa_')) {
      const surveyId = messageText.replace('IniciarPesquisa_', '');
      await whatsappService.sendNPSSurvey(instanceName, number, surveyId);
      continue;
    }

    // Verificar se é uma resposta numérica (possível NPS)
    const score = parseInt(messageText);
    if (!isNaN(score) && score >= 0 && score <= 10) {
      // Buscar pesquisa ativa mais recente para este número
      const recentResponse = await database.getClient().response.findFirst({
        where: { 
          userPhone: whatsappService.formatPhoneNumber(number),
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24 horas
          }
        },
        include: { survey: true },
        orderBy: { createdAt: 'desc' }
      });

      if (recentResponse) {
        await whatsappService.processSurveyResponse(
          instanceName, 
          number, 
          messageText, 
          recentResponse.surveyId
        );
        continue;
      }
    }

    // Salvar mensagem na conversa
    await saveConversationMessage(instanceName, number, messageText, 'received');
  }
}

// Processar atualização de conexão
async function handleConnectionUpdate(instanceName, data) {
  const connectionState = data.data.state;
  
  logger.info('Status de conexão atualizado:', { instanceName, state: connectionState });

  // Atualizar status no banco de dados
  await database.getClient().whatsAppInstance.updateMany({
    where: { instanceName },
    data: { 
      isActive: connectionState === 'open',
      updatedAt: new Date()
    }
  });
}

// Salvar mensagem na conversa
async function saveConversationMessage(instanceName, number, message, type) {
  try {
    const instance = await database.getClient().whatsAppInstance.findFirst({
      where: { instanceName }
    });

    if (!instance) return;

    const formattedNumber = whatsappService.formatPhoneNumber(number);
    
    // Buscar ou criar conversa
    let conversation = await database.getClient().conversation.findFirst({
      where: { 
        instanceId: instance.id,
        userPhone: formattedNumber
      }
    });

    const messageData = {
      id: Date.now().toString(),
      type,
      message,
      timestamp: new Date().toISOString()
    };

    if (conversation) {
      // Adicionar mensagem à conversa existente
      const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
      messages.push(messageData);
      
      await database.getClient().conversation.update({
        where: { id: conversation.id },
        data: { 
          messages,
          updatedAt: new Date()
        }
      });
    } else {
      // Criar nova conversa
      await database.getClient().conversation.create({
        data: {
          instanceId: instance.id,
          userPhone: formattedNumber,
          messages: [messageData],
          status: 'OPEN'
        }
      });
    }

    logger.debug('Mensagem salva na conversa:', { instanceName, number, type });

  } catch (error) {
    logger.error('Erro ao salvar mensagem na conversa:', error);
  }
}

module.exports = {
  getInstances,
  createInstance,
  getQRCode,
  getInstanceStatus,
  sendMessage,
  sendSurvey,
  sendBulkSurvey,
  deleteInstance,
  handleWebhook
};
