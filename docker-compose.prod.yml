# Docker Compose para produção
# Use: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # Backend otimizado para produção
  backend:
    build:
      target: production
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Frontend com Nginx otimizado
  frontend:
    build:
      target: production
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.25'

  # PostgreSQL com configurações de produção
  postgres:
    environment:
      - POSTGRES_DB=uniforms_survey
      - POSTGRES_USER=uniforms_user
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
    secrets:
      - postgres_password
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./docker/postgres/postgresql-prod.conf:/etc/postgresql/postgresql.conf
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    command: postgres -c config_file=/etc/postgresql/postgresql.conf

  # Redis com configurações de produção
  redis:
    command: redis-server /usr/local/etc/redis/redis-prod.conf
    volumes:
      - redis_prod_data:/data
      - ./docker/redis/redis-prod.conf:/usr/local/etc/redis/redis-prod.conf
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Evolution API com configurações de produção
  evolution-api:
    environment:
      - LOG_LEVEL=warn
      - AUTHENTICATION_API_KEY_FILE=/run/secrets/evolution_api_key
    secrets:
      - evolution_api_key
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # n8n com configurações de produção
  n8n:
    environment:
      - N8N_LOG_LEVEL=warn
      - N8N_BASIC_AUTH_PASSWORD_FILE=/run/secrets/n8n_password
    secrets:
      - n8n_password
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Nginx como proxy reverso e load balancer
  nginx:
    image: nginx:alpine
    container_name: uniforms-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx-prod.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - uniforms-network
    depends_on:
      - backend
      - frontend
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Prometheus para métricas
  prometheus:
    image: prom/prometheus:latest
    container_name: uniforms-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - uniforms-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana para dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: uniforms-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - uniforms-network
    environment:
      - GF_SECURITY_ADMIN_PASSWORD_FILE=/run/secrets/grafana_password
      - GF_USERS_ALLOW_SIGN_UP=false
    secrets:
      - grafana_password

  # Node Exporter para métricas do sistema
  node-exporter:
    image: prom/node-exporter:latest
    container_name: uniforms-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    networks:
      - uniforms-network
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'

  # Backup automático
  backup:
    image: postgres:15-alpine
    container_name: uniforms-backup
    restart: "no"
    volumes:
      - ./backups:/backups
      - ./docker/backup/backup.sh:/backup.sh
    networks:
      - uniforms-network
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=uniforms_survey
      - POSTGRES_USER=uniforms_user
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
    secrets:
      - postgres_password
    command: /backup.sh
    profiles:
      - backup

secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  evolution_api_key:
    file: ./secrets/evolution_api_key.txt
  n8n_password:
    file: ./secrets/n8n_password.txt
  grafana_password:
    file: ./secrets/grafana_password.txt

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  uniforms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
