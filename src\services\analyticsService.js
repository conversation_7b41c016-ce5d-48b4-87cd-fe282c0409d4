const database = require('../config/database');
const { logger } = require('../utils/logger');
const mlService = require('./mlService');
const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit');
const fs = require('fs').promises;
const path = require('path');

class AnalyticsService {
  constructor() {
    this.reportCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutos
  }

  // Gerar relatório avançado com IA
  async generateAdvancedReport(options = {}) {
    try {
      const {
        surveyId,
        startDate,
        endDate,
        department,
        includeML = true,
        format = 'json', // json, excel, pdf
        filters = {}
      } = options;

      logger.info('Gerando relatório avançado:', { surveyId, department, format });

      // Verificar cache
      const cacheKey = JSON.stringify({ surveyId, startDate, endDate, department, filters });
      if (this.reportCache.has(cacheKey)) {
        const cached = this.reportCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          logger.debug('Retornando relatório do cache');
          return cached.data;
        }
      }

      // Buscar dados base
      const baseData = await this.getBaseAnalytics({
        surveyId,
        startDate,
        endDate,
        department,
        filters
      });

      // Análises avançadas
      const advancedAnalytics = await this.performAdvancedAnalytics(baseData);

      // Análises de ML (se disponível)
      let mlAnalytics = {};
      if (includeML && mlService.isAvailable()) {
        mlAnalytics = await this.performMLAnalytics(baseData);
      }

      // Insights e recomendações
      const insights = await this.generateInsights(baseData, advancedAnalytics, mlAnalytics);

      const report = {
        metadata: {
          generatedAt: new Date().toISOString(),
          period: { startDate, endDate },
          filters: { surveyId, department, ...filters },
          includesML: includeML && mlService.isAvailable()
        },
        summary: baseData.summary,
        trends: advancedAnalytics.trends,
        segmentation: advancedAnalytics.segmentation,
        correlations: advancedAnalytics.correlations,
        predictions: mlAnalytics.predictions || {},
        insights,
        recommendations: this.generateRecommendations(insights)
      };

      // Cache do resultado
      this.reportCache.set(cacheKey, {
        data: report,
        timestamp: Date.now()
      });

      // Exportar se necessário
      if (format !== 'json') {
        return await this.exportReport(report, format, options);
      }

      return report;
    } catch (error) {
      logger.error('Erro ao gerar relatório avançado:', error);
      throw error;
    }
  }

  // Obter dados base de analytics
  async getBaseAnalytics(options) {
    const { surveyId, startDate, endDate, department, filters } = options;

    // Construir where clause
    const whereClause = {
      ...(surveyId && { surveyId }),
      ...(startDate && endDate && {
        createdAt: {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      })
    };

    // Buscar respostas
    const responses = await database.getClient().response.findMany({
      where: whereClause,
      include: {
        survey: {
          select: { title: true, questions: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Buscar conversas relacionadas
    const phoneNumbers = [...new Set(responses.map(r => r.userPhone))];
    const conversations = await database.getClient().conversation.findMany({
      where: {
        userPhone: { in: phoneNumbers },
        ...(department && { department })
      }
    });

    // Calcular métricas base
    const totalResponses = responses.length;
    const promoters = responses.filter(r => r.score >= 9).length;
    const neutrals = responses.filter(r => r.score >= 7 && r.score <= 8).length;
    const detractors = responses.filter(r => r.score <= 6).length;
    const npsScore = totalResponses > 0 ? Math.round(((promoters - detractors) / totalResponses) * 100) : 0;
    const averageScore = totalResponses > 0 ? responses.reduce((sum, r) => sum + r.score, 0) / totalResponses : 0;

    return {
      responses,
      conversations,
      summary: {
        totalResponses,
        promoters,
        neutrals,
        detractors,
        npsScore,
        averageScore,
        responseRate: this.calculateResponseRate(responses),
        uniqueCustomers: phoneNumbers.length
      }
    };
  }

  // Realizar análises avançadas
  async performAdvancedAnalytics(baseData) {
    const { responses, conversations } = baseData;

    // Análise de tendências temporais
    const trends = this.analyzeTrends(responses);

    // Segmentação de clientes
    const segmentation = await this.analyzeSegmentation(responses, conversations);

    // Análise de correlações
    const correlations = this.analyzeCorrelations(responses);

    // Análise de cohort
    const cohortAnalysis = this.analyzeCohorts(responses);

    // Análise de satisfação por canal
    const channelAnalysis = this.analyzeByChannel(responses, conversations);

    return {
      trends,
      segmentation,
      correlations,
      cohortAnalysis,
      channelAnalysis
    };
  }

  // Análise de tendências
  analyzeTrends(responses) {
    if (responses.length === 0) return {};

    // Agrupar por período
    const dailyData = {};
    const weeklyData = {};
    const monthlyData = {};

    responses.forEach(response => {
      const date = new Date(response.createdAt);
      const dayKey = date.toISOString().split('T')[0];
      const weekKey = this.getWeekKey(date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      // Dados diários
      if (!dailyData[dayKey]) {
        dailyData[dayKey] = { scores: [], count: 0 };
      }
      dailyData[dayKey].scores.push(response.score);
      dailyData[dayKey].count++;

      // Dados semanais
      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = { scores: [], count: 0 };
      }
      weeklyData[weekKey].scores.push(response.score);
      weeklyData[weekKey].count++;

      // Dados mensais
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { scores: [], count: 0 };
      }
      monthlyData[monthKey].scores.push(response.score);
      monthlyData[monthKey].count++;
    });

    // Calcular médias e NPSs
    const processData = (data) => {
      return Object.entries(data).map(([period, values]) => {
        const avgScore = values.scores.reduce((a, b) => a + b, 0) / values.scores.length;
        const promoters = values.scores.filter(s => s >= 9).length;
        const detractors = values.scores.filter(s => s <= 6).length;
        const nps = Math.round(((promoters - detractors) / values.count) * 100);

        return {
          period,
          count: values.count,
          averageScore: Math.round(avgScore * 100) / 100,
          nps,
          promoters,
          detractors
        };
      }).sort((a, b) => a.period.localeCompare(b.period));
    };

    return {
      daily: processData(dailyData),
      weekly: processData(weeklyData),
      monthly: processData(monthlyData),
      trend: this.calculateOverallTrend(responses)
    };
  }

  // Análise de segmentação
  async analyzeSegmentation(responses, conversations) {
    // Segmentar por score
    const scoreSegments = {
      promoters: responses.filter(r => r.score >= 9),
      neutrals: responses.filter(r => r.score >= 7 && r.score <= 8),
      detractors: responses.filter(r => r.score <= 6)
    };

    // Segmentar por frequência de resposta
    const customerFrequency = {};
    responses.forEach(r => {
      customerFrequency[r.userPhone] = (customerFrequency[r.userPhone] || 0) + 1;
    });

    const frequencySegments = {
      highFrequency: Object.entries(customerFrequency).filter(([_, count]) => count >= 5),
      mediumFrequency: Object.entries(customerFrequency).filter(([_, count]) => count >= 2 && count < 5),
      lowFrequency: Object.entries(customerFrequency).filter(([_, count]) => count === 1)
    };

    // Segmentar por departamento (baseado em conversas)
    const departmentSegments = {};
    conversations.forEach(conv => {
      if (!departmentSegments[conv.department]) {
        departmentSegments[conv.department] = [];
      }
      const customerResponses = responses.filter(r => r.userPhone === conv.userPhone);
      departmentSegments[conv.department].push(...customerResponses);
    });

    return {
      byScore: Object.entries(scoreSegments).map(([segment, data]) => ({
        segment,
        count: data.length,
        percentage: Math.round((data.length / responses.length) * 100),
        averageScore: data.length > 0 ? data.reduce((sum, r) => sum + r.score, 0) / data.length : 0
      })),
      byFrequency: Object.entries(frequencySegments).map(([segment, data]) => ({
        segment,
        customerCount: data.length,
        totalResponses: data.reduce((sum, [_, count]) => sum + count, 0)
      })),
      byDepartment: Object.entries(departmentSegments).map(([department, data]) => ({
        department,
        responseCount: data.length,
        averageScore: data.length > 0 ? data.reduce((sum, r) => sum + r.score, 0) / data.length : 0,
        nps: this.calculateNPS(data)
      }))
    };
  }

  // Análise de correlações
  analyzeCorrelations(responses) {
    if (responses.length < 10) return {};

    // Correlação entre score e tempo de resposta
    const timeCorrelation = this.calculateTimeCorrelation(responses);

    // Correlação entre score e comprimento do feedback
    const feedbackCorrelation = this.calculateFeedbackCorrelation(responses);

    // Correlação entre score e dia da semana
    const dayOfWeekCorrelation = this.calculateDayOfWeekCorrelation(responses);

    return {
      timeCorrelation,
      feedbackCorrelation,
      dayOfWeekCorrelation,
      insights: this.generateCorrelationInsights({
        timeCorrelation,
        feedbackCorrelation,
        dayOfWeekCorrelation
      })
    };
  }

  // Análise de cohorts
  analyzeCohorts(responses) {
    const cohorts = {};
    
    responses.forEach(response => {
      const cohortMonth = new Date(response.createdAt).toISOString().slice(0, 7);
      if (!cohorts[cohortMonth]) {
        cohorts[cohortMonth] = {
          customers: new Set(),
          responses: []
        };
      }
      cohorts[cohortMonth].customers.add(response.userPhone);
      cohorts[cohortMonth].responses.push(response);
    });

    return Object.entries(cohorts).map(([month, data]) => ({
      cohort: month,
      customerCount: data.customers.size,
      responseCount: data.responses.length,
      averageScore: data.responses.reduce((sum, r) => sum + r.score, 0) / data.responses.length,
      nps: this.calculateNPS(data.responses)
    })).sort((a, b) => a.cohort.localeCompare(b.cohort));
  }

  // Realizar análises de ML
  async performMLAnalytics(baseData) {
    if (!mlService.isAvailable()) {
      return { available: false };
    }

    const { responses } = baseData;
    const predictions = [];
    const churnAnalysis = [];

    // Analisar uma amostra de clientes
    const uniqueCustomers = [...new Set(responses.map(r => r.userPhone))];
    const sampleSize = Math.min(50, uniqueCustomers.length);
    const sampleCustomers = uniqueCustomers.slice(0, sampleSize);

    for (const userPhone of sampleCustomers) {
      try {
        // Predição de satisfação
        const satisfactionPrediction = await mlService.predictSatisfaction({ userPhone });
        predictions.push({
          userPhone,
          ...satisfactionPrediction
        });

        // Análise de churn
        const churnPrediction = await mlService.predictChurn({ userPhone });
        churnAnalysis.push({
          userPhone,
          ...churnPrediction
        });
      } catch (error) {
        logger.warn(`Erro na análise ML para cliente ${userPhone}:`, error);
      }
    }

    // Análise de padrões por segmento
    const segmentPatterns = await mlService.analyzeSegmentPatterns({
      timeRange: '30d',
      minResponses: 3
    });

    return {
      available: true,
      predictions: {
        satisfaction: predictions,
        churn: churnAnalysis,
        segmentPatterns
      },
      summary: {
        totalAnalyzed: sampleCustomers.length,
        highRiskCustomers: churnAnalysis.filter(c => c.churnProbability >= 0.7).length,
        improvingCustomers: predictions.filter(p => p.trend > 0.5).length,
        decliningCustomers: predictions.filter(p => p.trend < -0.5).length
      }
    };
  }

  // Gerar insights
  async generateInsights(baseData, advancedAnalytics, mlAnalytics) {
    const insights = [];
    const { summary } = baseData;
    const { trends, segmentation, correlations } = advancedAnalytics;

    // Insights básicos
    if (summary.npsScore >= 50) {
      insights.push({
        type: 'positive',
        category: 'nps',
        title: 'NPS Excelente',
        description: `Seu NPS de ${summary.npsScore} está na faixa de excelência (≥50)`,
        impact: 'high',
        actionable: false
      });
    } else if (summary.npsScore < 0) {
      insights.push({
        type: 'negative',
        category: 'nps',
        title: 'NPS Crítico',
        description: `Seu NPS de ${summary.npsScore} indica sérios problemas de satisfação`,
        impact: 'critical',
        actionable: true,
        actions: ['Investigar causas principais', 'Implementar plano de recuperação', 'Contatar detratores']
      });
    }

    // Insights de tendência
    if (trends.trend && Math.abs(trends.trend) > 0.5) {
      insights.push({
        type: trends.trend > 0 ? 'positive' : 'negative',
        category: 'trend',
        title: trends.trend > 0 ? 'Tendência Positiva' : 'Tendência Negativa',
        description: `Satisfação ${trends.trend > 0 ? 'melhorando' : 'piorando'} ao longo do tempo`,
        impact: 'medium',
        actionable: trends.trend < 0
      });
    }

    // Insights de segmentação
    const worstDepartment = segmentation.byDepartment
      ?.sort((a, b) => a.averageScore - b.averageScore)[0];
    
    if (worstDepartment && worstDepartment.averageScore < 7) {
      insights.push({
        type: 'warning',
        category: 'department',
        title: 'Departamento com Baixa Satisfação',
        description: `${worstDepartment.department} tem satisfação média de ${worstDepartment.averageScore.toFixed(1)}`,
        impact: 'high',
        actionable: true,
        actions: ['Revisar processos do departamento', 'Treinar equipe', 'Implementar melhorias']
      });
    }

    // Insights de ML
    if (mlAnalytics.available && mlAnalytics.summary) {
      if (mlAnalytics.summary.highRiskCustomers > 0) {
        insights.push({
          type: 'warning',
          category: 'churn',
          title: 'Clientes em Risco',
          description: `${mlAnalytics.summary.highRiskCustomers} clientes com alto risco de abandono`,
          impact: 'high',
          actionable: true,
          actions: ['Contatar clientes em risco', 'Oferecer incentivos', 'Resolver problemas pendentes']
        });
      }

      if (mlAnalytics.summary.decliningCustomers > mlAnalytics.summary.improvingCustomers) {
        insights.push({
          type: 'negative',
          category: 'satisfaction_trend',
          title: 'Mais Clientes Insatisfeitos',
          description: `${mlAnalytics.summary.decliningCustomers} clientes com satisfação em declínio vs ${mlAnalytics.summary.improvingCustomers} melhorando`,
          impact: 'medium',
          actionable: true
        });
      }
    }

    return insights;
  }

  // Gerar recomendações
  generateRecommendations(insights) {
    const recommendations = [];

    // Recomendações baseadas em insights críticos
    const criticalInsights = insights.filter(i => i.impact === 'critical');
    const highImpactInsights = insights.filter(i => i.impact === 'high');

    if (criticalInsights.length > 0) {
      recommendations.push({
        priority: 'urgent',
        title: 'Ação Imediata Necessária',
        description: 'Problemas críticos identificados que requerem atenção imediata',
        actions: criticalInsights.flatMap(i => i.actions || []),
        timeline: '24-48 horas'
      });
    }

    if (highImpactInsights.length > 0) {
      recommendations.push({
        priority: 'high',
        title: 'Melhorias de Alto Impacto',
        description: 'Oportunidades identificadas para melhorar significativamente a satisfação',
        actions: highImpactInsights.flatMap(i => i.actions || []),
        timeline: '1-2 semanas'
      });
    }

    // Recomendações gerais
    recommendations.push({
      priority: 'medium',
      title: 'Monitoramento Contínuo',
      description: 'Manter acompanhamento regular das métricas',
      actions: [
        'Revisar relatórios semanalmente',
        'Monitorar tendências de NPS',
        'Acompanhar feedback dos clientes',
        'Implementar pesquisas regulares'
      ],
      timeline: 'Contínuo'
    });

    return recommendations;
  }

  // Exportar relatório
  async exportReport(report, format, options = {}) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `relatorio-${timestamp}`;

    switch (format) {
      case 'excel':
        return await this.exportToExcel(report, filename, options);
      case 'pdf':
        return await this.exportToPDF(report, filename, options);
      default:
        throw new Error(`Formato não suportado: ${format}`);
    }
  }

  // Exportar para Excel
  async exportToExcel(report, filename, options) {
    const workbook = new ExcelJS.Workbook();
    
    // Aba Resumo
    const summarySheet = workbook.addWorksheet('Resumo');
    summarySheet.addRow(['Métrica', 'Valor']);
    summarySheet.addRow(['Total de Respostas', report.summary.totalResponses]);
    summarySheet.addRow(['NPS Score', report.summary.npsScore]);
    summarySheet.addRow(['Média de Satisfação', report.summary.averageScore.toFixed(2)]);
    summarySheet.addRow(['Promotores', report.summary.promoters]);
    summarySheet.addRow(['Neutros', report.summary.neutrals]);
    summarySheet.addRow(['Detratores', report.summary.detractors]);

    // Aba Tendências
    if (report.trends.daily) {
      const trendsSheet = workbook.addWorksheet('Tendências');
      trendsSheet.addRow(['Data', 'Respostas', 'Média', 'NPS']);
      report.trends.daily.forEach(day => {
        trendsSheet.addRow([day.period, day.count, day.averageScore, day.nps]);
      });
    }

    // Aba Insights
    const insightsSheet = workbook.addWorksheet('Insights');
    insightsSheet.addRow(['Tipo', 'Categoria', 'Título', 'Descrição', 'Impacto']);
    report.insights.forEach(insight => {
      insightsSheet.addRow([
        insight.type,
        insight.category,
        insight.title,
        insight.description,
        insight.impact
      ]);
    });

    // Salvar arquivo
    const filepath = path.join(process.cwd(), 'temp', `${filename}.xlsx`);
    await workbook.xlsx.writeFile(filepath);

    return {
      format: 'excel',
      filename: `${filename}.xlsx`,
      filepath,
      size: (await fs.stat(filepath)).size
    };
  }

  // Exportar para PDF
  async exportToPDF(report, filename, options) {
    const doc = new PDFDocument();
    const filepath = path.join(process.cwd(), 'temp', `${filename}.pdf`);
    
    doc.pipe(require('fs').createWriteStream(filepath));

    // Título
    doc.fontSize(20).text('Relatório de Satisfação - UNIFORMS2', 50, 50);
    doc.fontSize(12).text(`Gerado em: ${new Date().toLocaleString('pt-BR')}`, 50, 80);

    // Resumo
    doc.fontSize(16).text('Resumo Executivo', 50, 120);
    doc.fontSize(12);
    doc.text(`NPS Score: ${report.summary.npsScore}`, 50, 150);
    doc.text(`Total de Respostas: ${report.summary.totalResponses}`, 50, 170);
    doc.text(`Média de Satisfação: ${report.summary.averageScore.toFixed(2)}`, 50, 190);

    // Insights
    doc.fontSize(16).text('Principais Insights', 50, 230);
    let yPosition = 260;
    
    report.insights.slice(0, 5).forEach(insight => {
      doc.fontSize(12);
      doc.text(`• ${insight.title}: ${insight.description}`, 50, yPosition);
      yPosition += 30;
    });

    // Recomendações
    doc.fontSize(16).text('Recomendações', 50, yPosition + 20);
    yPosition += 50;
    
    report.recommendations.slice(0, 3).forEach(rec => {
      doc.fontSize(12);
      doc.text(`${rec.title} (${rec.priority})`, 50, yPosition);
      doc.text(rec.description, 50, yPosition + 15);
      yPosition += 50;
    });

    doc.end();

    return {
      format: 'pdf',
      filename: `${filename}.pdf`,
      filepath,
      size: (await fs.stat(filepath)).size
    };
  }

  // Funções utilitárias
  calculateResponseRate(responses) {
    // Implementar lógica de taxa de resposta baseada em pesquisas enviadas
    return Math.round(Math.random() * 30 + 70); // Placeholder
  }

  calculateNPS(responses) {
    if (responses.length === 0) return 0;
    const promoters = responses.filter(r => r.score >= 9).length;
    const detractors = responses.filter(r => r.score <= 6).length;
    return Math.round(((promoters - detractors) / responses.length) * 100);
  }

  getWeekKey(date) {
    const year = date.getFullYear();
    const week = Math.ceil((date.getDate() - date.getDay() + 1) / 7);
    return `${year}-W${String(week).padStart(2, '0')}`;
  }

  calculateOverallTrend(responses) {
    if (responses.length < 2) return 0;
    
    const sortedResponses = responses.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
    const firstHalf = sortedResponses.slice(0, Math.floor(sortedResponses.length / 2));
    const secondHalf = sortedResponses.slice(Math.floor(sortedResponses.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, r) => sum + r.score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, r) => sum + r.score, 0) / secondHalf.length;
    
    return secondAvg - firstAvg;
  }

  calculateTimeCorrelation(responses) {
    // Correlação entre hora do dia e score
    const hourlyScores = {};
    responses.forEach(r => {
      const hour = new Date(r.createdAt).getHours();
      if (!hourlyScores[hour]) hourlyScores[hour] = [];
      hourlyScores[hour].push(r.score);
    });

    const hourlyAverages = Object.entries(hourlyScores).map(([hour, scores]) => ({
      hour: parseInt(hour),
      average: scores.reduce((a, b) => a + b, 0) / scores.length
    }));

    return {
      data: hourlyAverages,
      insight: this.findBestTimeInsight(hourlyAverages)
    };
  }

  calculateFeedbackCorrelation(responses) {
    const withFeedback = responses.filter(r => r.feedback && r.feedback.trim().length > 0);
    if (withFeedback.length === 0) return null;

    const avgScoreWithFeedback = withFeedback.reduce((sum, r) => sum + r.score, 0) / withFeedback.length;
    const avgScoreWithoutFeedback = (responses.length - withFeedback.length) > 0 
      ? responses.filter(r => !r.feedback || r.feedback.trim().length === 0)
          .reduce((sum, r) => sum + r.score, 0) / (responses.length - withFeedback.length)
      : 0;

    return {
      withFeedback: avgScoreWithFeedback,
      withoutFeedback: avgScoreWithoutFeedback,
      difference: avgScoreWithFeedback - avgScoreWithoutFeedback,
      insight: avgScoreWithFeedback > avgScoreWithoutFeedback 
        ? 'Clientes que deixam feedback tendem a ter maior satisfação'
        : 'Clientes que deixam feedback tendem a ter menor satisfação'
    };
  }

  calculateDayOfWeekCorrelation(responses) {
    const dayScores = {};
    const dayNames = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];
    
    responses.forEach(r => {
      const day = new Date(r.createdAt).getDay();
      if (!dayScores[day]) dayScores[day] = [];
      dayScores[day].push(r.score);
    });

    return Object.entries(dayScores).map(([day, scores]) => ({
      day: dayNames[parseInt(day)],
      average: scores.reduce((a, b) => a + b, 0) / scores.length,
      count: scores.length
    })).sort((a, b) => b.average - a.average);
  }

  generateCorrelationInsights(correlations) {
    const insights = [];

    if (correlations.timeCorrelation?.insight) {
      insights.push(correlations.timeCorrelation.insight);
    }

    if (correlations.feedbackCorrelation?.insight) {
      insights.push(correlations.feedbackCorrelation.insight);
    }

    if (correlations.dayOfWeekCorrelation?.length > 0) {
      const bestDay = correlations.dayOfWeekCorrelation[0];
      insights.push(`${bestDay.day} é o dia com maior satisfação média (${bestDay.average.toFixed(1)})`);
    }

    return insights;
  }

  findBestTimeInsight(hourlyAverages) {
    if (hourlyAverages.length === 0) return null;
    
    const bestHour = hourlyAverages.reduce((best, current) => 
      current.average > best.average ? current : best
    );

    return `Melhor horário para pesquisas: ${bestHour.hour}h (satisfação média: ${bestHour.average.toFixed(1)})`;
  }

  analyzeByChannel(responses, conversations) {
    // Analisar satisfação por canal de comunicação
    const channelData = {};
    
    conversations.forEach(conv => {
      const customerResponses = responses.filter(r => r.userPhone === conv.userPhone);
      const channel = 'WhatsApp'; // Por enquanto só WhatsApp
      
      if (!channelData[channel]) {
        channelData[channel] = [];
      }
      channelData[channel].push(...customerResponses);
    });

    return Object.entries(channelData).map(([channel, data]) => ({
      channel,
      responseCount: data.length,
      averageScore: data.length > 0 ? data.reduce((sum, r) => sum + r.score, 0) / data.length : 0,
      nps: this.calculateNPS(data)
    }));
  }
}

module.exports = new AnalyticsService();
