const QRCode = require("qrcode");
const database = require("../config/database");
const { logger } = require("../utils/logger");
const { asyncHandler } = require("../middleware/errorHandler");

// Listar pesquisas do usuário
const getSurveys = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, search, isActive } = req.query;
  const userId = req.user.id;
  const skip = (page - 1) * limit;

  const where = {
    userId,
    ...(search && {
      OR: [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ],
    }),
    ...(isActive !== undefined && { isActive: isActive === "true" }),
  };

  const [surveys, total] = await Promise.all([
    database.getClient().survey.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      orderBy: { createdAt: "desc" },
      include: {
        _count: {
          select: { responses: true },
        },
      },
    }),
    database.getClient().survey.count({ where }),
  ]);

  res.json({
    surveys: surveys.map((survey) => ({
      ...survey,
      responsesCount: survey._count.responses,
    })),
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit),
    },
  });
});

// Obter pesquisa específica
const getSurvey = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  const survey = await database.getClient().survey.findFirst({
    where: { id, userId },
    include: {
      _count: {
        select: { responses: true },
      },
      responses: {
        take: 5,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          userPhone: true,
          score: true,
          feedback: true,
          isDetractor: true,
          isPromoter: true,
          isNeutral: true,
          createdAt: true,
        },
      },
    },
  });

  if (!survey) {
    return res.status(404).json({
      error: "Pesquisa não encontrada",
      message: "A pesquisa solicitada não foi encontrada",
    });
  }

  res.json({
    ...survey,
    responsesCount: survey._count.responses,
  });
});

// Criar nova pesquisa
const createSurvey = asyncHandler(async (req, res) => {
  const { title, description, questions, isActive = true } = req.body;
  const userId = req.user.id;

  const survey = await database.getClient().survey.create({
    data: {
      title,
      description,
      questions,
      isActive,
      userId,
    },
  });

  // Gerar links e QR codes
  const whatsappLink = `https://wa.me/${
    process.env.WHATSAPP_NUMBER || "5511999999999"
  }?text=IniciarPesquisa_${survey.id}`;
  const htmlLink = `${
    process.env.BACKEND_URL || "http://localhost:3000"
  }/survey/${survey.id}`;

  try {
    const qrCodeWhatsApp = await QRCode.toDataURL(whatsappLink);
    const qrCodeHTML = await QRCode.toDataURL(htmlLink);

    logger.info("Nova pesquisa criada:", {
      surveyId: survey.id,
      title: survey.title,
    });

    res.status(201).json({
      message: "Pesquisa criada com sucesso",
      survey,
      links: {
        whatsapp: whatsappLink,
        html: htmlLink,
        qrCodes: {
          whatsapp: qrCodeWhatsApp,
          html: qrCodeHTML,
        },
      },
    });
  } catch (error) {
    logger.error("Erro ao gerar QR codes:", error);

    res.status(201).json({
      message: "Pesquisa criada com sucesso (erro ao gerar QR codes)",
      survey,
      links: {
        whatsapp: whatsappLink,
        html: htmlLink,
        qrCodes: {
          whatsapp: null,
          html: null,
        },
      },
    });
  }
});

// Atualizar pesquisa
const updateSurvey = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;
  const updateData = req.body;

  // Verificar se a pesquisa existe e pertence ao usuário
  const existingSurvey = await database.getClient().survey.findFirst({
    where: { id, userId },
  });

  if (!existingSurvey) {
    return res.status(404).json({
      error: "Pesquisa não encontrada",
      message: "A pesquisa solicitada não foi encontrada",
    });
  }

  const updatedSurvey = await database.getClient().survey.update({
    where: { id },
    data: updateData,
  });

  logger.info("Pesquisa atualizada:", { surveyId: id });

  res.json({
    message: "Pesquisa atualizada com sucesso",
    survey: updatedSurvey,
  });
});

// Deletar pesquisa
const deleteSurvey = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  // Verificar se a pesquisa existe e pertence ao usuário
  const existingSurvey = await database.getClient().survey.findFirst({
    where: { id, userId },
  });

  if (!existingSurvey) {
    return res.status(404).json({
      error: "Pesquisa não encontrada",
      message: "A pesquisa solicitada não foi encontrada",
    });
  }

  await database.getClient().survey.delete({
    where: { id },
  });

  logger.info("Pesquisa deletada:", { surveyId: id });

  res.json({
    message: "Pesquisa deletada com sucesso",
  });
});

// Gerar novos links e QR codes para uma pesquisa
const generateLinks = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  const survey = await database.getClient().survey.findFirst({
    where: { id, userId },
  });

  if (!survey) {
    return res.status(404).json({
      error: "Pesquisa não encontrada",
      message: "A pesquisa solicitada não foi encontrada",
    });
  }

  const whatsappLink = `https://wa.me/${
    process.env.WHATSAPP_NUMBER || "5511999999999"
  }?text=IniciarPesquisa_${survey.id}`;
  const htmlLink = `${
    process.env.BACKEND_URL || "http://localhost:3000"
  }/survey/${survey.id}`;

  try {
    const qrCodeWhatsApp = await QRCode.toDataURL(whatsappLink);
    const qrCodeHTML = await QRCode.toDataURL(htmlLink);

    res.json({
      links: {
        whatsapp: whatsappLink,
        html: htmlLink,
        qrCodes: {
          whatsapp: qrCodeWhatsApp,
          html: qrCodeHTML,
        },
      },
    });
  } catch (error) {
    logger.error("Erro ao gerar QR codes:", error);

    res.status(500).json({
      error: "Erro ao gerar QR codes",
      message: "Não foi possível gerar os QR codes",
    });
  }
});

// Renderizar formulário HTML da pesquisa
const renderSurveyForm = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const survey = await database.getClient().survey.findUnique({
    where: { id, isActive: true },
  });

  if (!survey) {
    return res.status(404).send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Pesquisa não encontrada</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .error { color: #e74c3c; }
        </style>
      </head>
      <body>
        <h1 class="error">Pesquisa não encontrada</h1>
        <p>A pesquisa solicitada não foi encontrada ou não está mais ativa.</p>
      </body>
      </html>
    `);
  }

  const questions = JSON.parse(JSON.stringify(survey.questions));

  const questionsHTML = questions
    .map((q) => {
      switch (q.type) {
        case "nps":
          return `
          <div class="question">
            <label>${q.question}</label>
            <div class="nps-scale">
              ${Array.from(
                { length: 11 },
                (_, i) => `
                <label class="nps-option">
                  <input type="radio" name="question_${q.id}" value="${i}" ${
                  q.required ? "required" : ""
                }>
                  <span>${i}</span>
                </label>
              `
              ).join("")}
            </div>
            <div class="nps-labels">
              <span>Muito improvável</span>
              <span>Muito provável</span>
            </div>
          </div>
        `;
        case "text":
          return `
          <div class="question">
            <label for="question_${q.id}">${q.question}</label>
            <textarea name="question_${q.id}" rows="4" ${
            q.required ? "required" : ""
          }></textarea>
          </div>
        `;
        case "multiple_choice":
          return `
          <div class="question">
            <label>${q.question}</label>
            <div class="options">
              ${q.options
                .map(
                  (option, index) => `
                <label class="option">
                  <input type="radio" name="question_${
                    q.id
                  }" value="${option}" ${q.required ? "required" : ""}>
                  <span>${option}</span>
                </label>
              `
                )
                .join("")}
            </div>
          </div>
        `;
        case "rating":
          return `
          <div class="question">
            <label>${q.question}</label>
            <div class="rating">
              ${Array.from(
                { length: 5 },
                (_, i) => `
                <label class="star">
                  <input type="radio" name="question_${q.id}" value="${
                  i + 1
                }" ${q.required ? "required" : ""}>
                  <span>★</span>
                </label>
              `
              ).join("")}
            </div>
          </div>
        `;
        case "yes_no":
          return `
          <div class="question">
            <label>${q.question}</label>
            <div class="options">
              <label class="option">
                <input type="radio" name="question_${q.id}" value="sim" ${
            q.required ? "required" : ""
          }>
                <span>Sim</span>
              </label>
              <label class="option">
                <input type="radio" name="question_${q.id}" value="nao" ${
            q.required ? "required" : ""
          }>
                <span>Não</span>
              </label>
            </div>
          </div>
        `;
        default:
          return "";
      }
    })
    .join("");

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${survey.title}</title>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        * { box-sizing: border-box; }
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          margin: 0;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background: white;
          border-radius: 10px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
          overflow: hidden;
        }
        .header {
          background: #2c3e50;
          color: white;
          padding: 30px;
          text-align: center;
        }
        .header h1 { margin: 0; font-size: 24px; }
        .header p { margin: 10px 0 0 0; opacity: 0.8; }
        .form-content { padding: 30px; }
        .question {
          margin-bottom: 30px;
          padding: 20px;
          border: 1px solid #eee;
          border-radius: 8px;
          background: #f9f9f9;
        }
        .question label {
          display: block;
          font-weight: 600;
          margin-bottom: 15px;
          color: #2c3e50;
          font-size: 16px;
        }
        .nps-scale {
          display: flex;
          justify-content: space-between;
          margin: 15px 0;
          flex-wrap: wrap;
        }
        .nps-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          margin: 5px;
        }
        .nps-option input { margin-bottom: 5px; }
        .nps-option span {
          font-weight: bold;
          color: #3498db;
          padding: 5px 10px;
          border-radius: 20px;
          transition: all 0.3s;
        }
        .nps-option:hover span { background: #3498db; color: white; }
        .nps-labels {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #7f8c8d;
          margin-top: 10px;
        }
        .options { display: flex; flex-direction: column; gap: 10px; }
        .option {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 10px;
          border-radius: 5px;
          transition: background 0.3s;
        }
        .option:hover { background: #ecf0f1; }
        .option input { margin-right: 10px; }
        textarea {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 5px;
          font-family: inherit;
          resize: vertical;
          transition: border-color 0.3s;
        }
        textarea:focus {
          outline: none;
          border-color: #3498db;
        }
        .rating { display: flex; gap: 5px; }
        .star {
          cursor: pointer;
          font-size: 24px;
          color: #ddd;
          transition: color 0.3s;
        }
        .star:hover, .star:hover ~ .star { color: #f39c12; }
        .star input { display: none; }
        .star input:checked ~ span, .star input:checked ~ span ~ .star span { color: #f39c12; }
        .phone-input {
          margin-bottom: 20px;
          padding: 20px;
          border: 1px solid #eee;
          border-radius: 8px;
          background: #f0f8ff;
        }
        .phone-input label {
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 10px;
          display: block;
        }
        .phone-input input {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 5px;
          font-size: 16px;
        }
        .submit-btn {
          background: linear-gradient(135deg, #3498db, #2980b9);
          color: white;
          padding: 15px 40px;
          border: none;
          border-radius: 25px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          width: 100%;
          transition: all 0.3s;
        }
        .submit-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .submit-btn:disabled {
          background: #bdc3c7;
          cursor: not-allowed;
          transform: none;
        }
        @media (max-width: 600px) {
          .container { margin: 10px; }
          .nps-scale { justify-content: center; }
          .nps-option { margin: 2px; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${survey.title}</h1>
          ${survey.description ? `<p>${survey.description}</p>` : ""}
        </div>
        <div class="form-content">
          <form action="/submit-survey" method="POST" id="surveyForm">
            <input type="hidden" name="surveyId" value="${survey.id}">

            <div class="phone-input">
              <label for="phone">Seu WhatsApp (opcional):</label>
              <input type="tel" name="phone" id="phone" placeholder="(11) 99999-9999">
            </div>

            ${questionsHTML}

            <button type="submit" class="submit-btn">Enviar Resposta</button>
          </form>
        </div>
      </div>

      <script>
        // Máscara para telefone
        document.getElementById('phone').addEventListener('input', function(e) {
          let value = e.target.value.replace(/\D/g, '');
          if (value.length >= 11) {
            value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
          } else if (value.length >= 7) {
            value = value.replace(/(\d{2})(\d{4})(\d{0,4})/, '($1) $2-$3');
          } else if (value.length >= 3) {
            value = value.replace(/(\d{2})(\d{0,5})/, '($1) $2');
          }
          e.target.value = value;
        });

        // Validação do formulário
        document.getElementById('surveyForm').addEventListener('submit', function(e) {
          const submitBtn = e.target.querySelector('.submit-btn');
          submitBtn.disabled = true;
          submitBtn.textContent = 'Enviando...';
        });

        // Efeito nas estrelas de rating
        document.querySelectorAll('.rating').forEach(rating => {
          const stars = rating.querySelectorAll('.star');
          stars.forEach((star, index) => {
            star.addEventListener('mouseenter', () => {
              stars.forEach((s, i) => {
                s.style.color = i <= index ? '#f39c12' : '#ddd';
              });
            });
            star.addEventListener('mouseleave', () => {
              const checked = rating.querySelector('input:checked');
              const checkedIndex = checked ? Array.from(stars).indexOf(checked.parentElement) : -1;
              stars.forEach((s, i) => {
                s.style.color = i <= checkedIndex ? '#f39c12' : '#ddd';
              });
            });
          });
        });
      </script>
    </body>
    </html>
  `;

  res.send(html);
});

// Processar envio do formulário HTML
const submitSurveyForm = asyncHandler(async (req, res) => {
  const { surveyId, phone, ...answers } = req.body;

  // Buscar a pesquisa
  const survey = await database.getClient().survey.findUnique({
    where: { id: surveyId, isActive: true },
  });

  if (!survey) {
    return res.status(404).send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Erro</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .error { color: #e74c3c; }
        </style>
      </head>
      <body>
        <h1 class="error">Pesquisa não encontrada</h1>
        <p>A pesquisa não foi encontrada ou não está mais ativa.</p>
      </body>
      </html>
    `);
  }

  // Processar respostas
  const questions = JSON.parse(JSON.stringify(survey.questions));
  let npsScore = null;
  let feedback = "";
  const processedAnswers = {};

  // Extrair respostas das perguntas
  questions.forEach((question) => {
    const answerKey = `question_${question.id}`;
    const answer = answers[answerKey];

    if (answer) {
      processedAnswers[question.id] = answer;

      // Se for pergunta NPS, capturar o score
      if (question.type === "nps") {
        npsScore = parseInt(answer);
      }

      // Se for pergunta de texto, adicionar ao feedback
      if (question.type === "text") {
        feedback += (feedback ? " | " : "") + answer;
      }
    }
  });

  // Classificar resposta NPS
  let isDetractor = false;
  let isNeutral = false;
  let isPromoter = false;

  if (npsScore !== null) {
    if (npsScore <= 6) {
      isDetractor = true;
    } else if (npsScore <= 8) {
      isNeutral = true;
    } else {
      isPromoter = true;
    }
  }

  // Limpar telefone
  const cleanPhone = phone ? phone.replace(/\D/g, "") : "unknown";

  try {
    // Salvar resposta no banco
    const response = await database.getClient().response.create({
      data: {
        surveyId,
        userPhone: cleanPhone,
        score: npsScore,
        feedback: feedback || null,
        isDetractor,
        isNeutral,
        isPromoter,
        // Salvar todas as respostas em um campo JSON adicional se necessário
        // answers: processedAnswers
      },
    });

    logger.info("Resposta de formulário HTML recebida:", {
      responseId: response.id,
      surveyId,
      phone: cleanPhone,
      score: npsScore,
      isDetractor,
    });

    // Se for detrator, enviar para n8n
    if (isDetractor && process.env.N8N_WEBHOOK_URL) {
      try {
        const axios = require("axios");
        await axios.post(
          `${process.env.N8N_WEBHOOK_URL}${process.env.N8N_DETRACTOR_WEBHOOK}`,
          {
            responseId: response.id,
            surveyId,
            phone: cleanPhone,
            score: npsScore,
            feedback,
            source: "html_form",
          }
        );
        logger.info("Detrator enviado para n8n:", { responseId: response.id });
      } catch (error) {
        logger.error("Erro ao enviar detrator para n8n:", error);
      }
    }

    // Página de sucesso
    const successHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Obrigado!</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .container {
            max-width: 500px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            padding: 40px;
          }
          .success-icon {
            font-size: 64px;
            color: #27ae60;
            margin-bottom: 20px;
          }
          h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
          }
          p {
            color: #7f8c8d;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
          }
          .score-display {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
          .score-number {
            font-size: 48px;
            font-weight: bold;
            color: ${
              isPromoter ? "#27ae60" : isNeutral ? "#f39c12" : "#e74c3c"
            };
          }
          .score-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
          }
          .back-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
          }
          .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="success-icon">✅</div>
          <h1>Obrigado pela sua resposta!</h1>
          <p>Sua opinião é muito importante para nós e nos ajuda a melhorar nossos serviços.</p>

          ${
            npsScore !== null
              ? `
            <div class="score-display">
              <div class="score-number">${npsScore}</div>
              <div class="score-label">Sua avaliação</div>
            </div>
          `
              : ""
          }

          <p>
            ${
              isPromoter
                ? "Ficamos muito felizes que você recomendaria nossos serviços!"
                : isNeutral
                ? "Obrigado pelo feedback! Vamos trabalhar para melhorar ainda mais."
                : isDetractor
                ? "Agradecemos seu feedback. Nossa equipe entrará em contato para resolver suas preocupações."
                : "Sua resposta foi registrada com sucesso."
            }
          </p>

          <a href="/" class="back-btn">Voltar ao início</a>
        </div>
      </body>
      </html>
    `;

    res.send(successHtml);
  } catch (error) {
    logger.error("Erro ao processar resposta do formulário:", error);

    const errorHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Erro</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .error { color: #e74c3c; }
        </style>
      </head>
      <body>
        <h1 class="error">Erro ao enviar resposta</h1>
        <p>Ocorreu um erro ao processar sua resposta. Tente novamente mais tarde.</p>
        <a href="javascript:history.back()">Voltar</a>
      </body>
      </html>
    `;

    res.status(500).send(errorHtml);
  }
});

module.exports = {
  getSurveys,
  getSurvey,
  createSurvey,
  updateSurvey,
  deleteSurvey,
  generateLinks,
  renderSurveyForm,
  submitSurveyForm,
};
