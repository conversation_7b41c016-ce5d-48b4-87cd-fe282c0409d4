import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

const Integration = () => {
  const [whatsappConfig, setWhatsappConfig] = useState({
    instanceName: '',
    webhookUrl: ''
  });
  const [sendSurvey, setSendSurvey] = useState({
    phone: '',
    surveyId: ''
  });

  const handleCreateInstance = async (e) => {
    e.preventDefault();
    
    if (!whatsappConfig.instanceName || !whatsappConfig.webhookUrl) {
      toast.error('Preencha todos os campos obrigatórios');
      return;
    }

    try {
      const response = await fetch('/api/whatsapp/create-instance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(whatsappConfig)
      });

      if (response.ok) {
        toast.success('Instância WhatsApp criada com sucesso!');
        setWhatsappConfig({ instanceName: '', webhookUrl: '' });
      } else {
        toast.error('Erro ao criar instância WhatsApp');
      }
    } catch (error) {
      toast.error('Erro de conexão');
    }
  };

  const handleSendSurvey = async (e) => {
    e.preventDefault();
    
    if (!sendSurvey.phone || !sendSurvey.surveyId) {
      toast.error('Preencha todos os campos');
      return;
    }

    try {
      const response = await fetch('/api/whatsapp/send-survey', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(sendSurvey)
      });

      if (response.ok) {
        toast.success('Pesquisa enviada com sucesso!');
        setSendSurvey({ phone: '', surveyId: '' });
      } else {
        toast.error('Erro ao enviar pesquisa');
      }
    } catch (error) {
      toast.error('Erro de conexão');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Integração WhatsApp</h1>
        <p className="text-gray-600">Configure e gerencie a integração com WhatsApp</p>
      </div>

      {/* WhatsApp Setup */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuração do WhatsApp</h3>
        <form onSubmit={handleCreateInstance} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome da Instância *
              </label>
              <input
                type="text"
                value={whatsappConfig.instanceName}
                onChange={(e) => setWhatsappConfig(prev => ({ ...prev, instanceName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="minha-empresa"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Webhook URL *
              </label>
              <input
                type="url"
                value={whatsappConfig.webhookUrl}
                onChange={(e) => setWhatsappConfig(prev => ({ ...prev, webhookUrl: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://meusite.com/webhook"
                required
              />
            </div>
          </div>
          <div>
            <button
              type="submit"
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
              </svg>
              <span>Criar Instância</span>
            </button>
          </div>
        </form>
      </div>

      {/* Send Survey */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Enviar Pesquisa</h3>
        <form onSubmit={handleSendSurvey} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Telefone *
              </label>
              <input
                type="tel"
                value={sendSurvey.phone}
                onChange={(e) => setSendSurvey(prev => ({ ...prev, phone: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="+5511999999999"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pesquisa *
              </label>
              <select
                value={sendSurvey.surveyId}
                onChange={(e) => setSendSurvey(prev => ({ ...prev, surveyId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Selecione uma pesquisa</option>
                <option value="1">Satisfação do Cliente</option>
                <option value="2">Avaliação do Atendimento</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                <span>Enviar</span>
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Integration Guide */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Guia de Integração</h3>
        <div className="space-y-6">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
              1
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Configure sua instância WhatsApp</h4>
              <p className="text-sm text-gray-600 mb-3">
                Crie uma instância e configure o webhook para receber respostas automaticamente.
              </p>
              <div className="bg-gray-50 p-3 rounded-lg">
                <code className="text-sm text-gray-800">
                  POST /api/whatsapp/create-instance<br/>
                  {`{ "instanceName": "minha-empresa", "webhookUrl": "https://meusite.com/webhook" }`}
                </code>
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
              2
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Escaneie o QR Code</h4>
              <p className="text-sm text-gray-600">
                Use o WhatsApp Web para conectar sua conta à instância criada.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
              3
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Envie pesquisas</h4>
              <p className="text-sm text-gray-600 mb-3">
                Use a API ou interface para enviar pesquisas aos seus clientes.
              </p>
              <div className="bg-gray-50 p-3 rounded-lg">
                <code className="text-sm text-gray-800">
                  POST /api/whatsapp/send-survey<br/>
                  {`{ "phone": "+5511999999999", "surveyId": "1" }`}
                </code>
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
              4
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Receba respostas automaticamente</h4>
              <p className="text-sm text-gray-600">
                As respostas serão processadas automaticamente e a gamificação aplicada.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* API Documentation */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Documentação da API</h3>
        <div className="space-y-4">
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">POST</span>
              <code className="text-sm font-mono">/api/whatsapp/create-instance</code>
            </div>
            <p className="text-sm text-gray-600 mb-2">Cria uma nova instância do WhatsApp</p>
            <div className="bg-gray-50 p-2 rounded text-xs">
              <strong>Body:</strong> {`{ "instanceName": "string", "webhookUrl": "string" }`}
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">POST</span>
              <code className="text-sm font-mono">/api/whatsapp/send-survey</code>
            </div>
            <p className="text-sm text-gray-600 mb-2">Envia uma pesquisa via WhatsApp</p>
            <div className="bg-gray-50 p-2 rounded text-xs">
              <strong>Body:</strong> {`{ "phone": "string", "surveyId": "string" }`}
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">GET</span>
              <code className="text-sm font-mono">/api/whatsapp/instances</code>
            </div>
            <p className="text-sm text-gray-600">Lista todas as instâncias do WhatsApp</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Integration;
