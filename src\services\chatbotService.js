const database = require('../config/database');
const { logger } = require('../utils/logger');
const mlService = require('./mlService');

class ChatbotService {
  constructor() {
    this.intents = new Map();
    this.responses = new Map();
    this.context = new Map(); // Contexto por usuário
    this.isInitialized = false;
    
    this.initializeIntents();
  }

  // Inicializar intents e respostas do chatbot
  initializeIntents() {
    // Saudações
    this.intents.set('greeting', {
      patterns: [
        /^(oi|olá|ola|hey|ei|bom dia|boa tarde|boa noite)/i,
        /^(hi|hello|good morning|good afternoon|good evening)/i
      ],
      responses: [
        'Olá! 👋 Sou o assistente virtual da UNIFORMS2. Como posso ajudá-lo hoje?',
        'Oi! 😊 Em que posso ser útil?',
        'Olá! Estou aqui para ajudar. O que você precisa?'
      ],
      context: 'greeting'
    });

    // Despedidas
    this.intents.set('goodbye', {
      patterns: [
        /^(tchau|bye|até logo|até mais|obrigado|valeu|thanks)/i,
        /^(goodbye|see you|thank you)/i
      ],
      responses: [
        'Até logo! 👋 Foi um prazer ajudá-lo.',
        'Tchau! 😊 Volte sempre que precisar.',
        'Obrigado pelo contato! Tenha um ótimo dia! ✨'
      ],
      context: 'goodbye'
    });

    // Pesquisa de satisfação
    this.intents.set('survey_info', {
      patterns: [
        /pesquisa|survey|satisfação|avaliação|nota|score/i,
        /como avaliar|dar nota|responder pesquisa/i
      ],
      responses: [
        'Nossa pesquisa de satisfação é rápida e simples! 📊\n\nVocê pode avaliar nosso atendimento de 0 a 10 e deixar um comentário opcional.\n\nGostaria de responder agora?',
        'A pesquisa de satisfação nos ajuda a melhorar nossos serviços! 🎯\n\nSão apenas 2 perguntas rápidas. Posso enviar o link para você?'
      ],
      context: 'survey',
      actions: ['offer_survey']
    });

    // Problemas técnicos
    this.intents.set('technical_issue', {
      patterns: [
        /problema|erro|bug|não funciona|travou|lento/i,
        /issue|error|broken|not working|slow/i
      ],
      responses: [
        'Entendo que você está enfrentando um problema técnico. 🔧\n\nPara ajudá-lo melhor, vou transferir você para nossa equipe técnica especializada.',
        'Problemas técnicos podem ser frustrantes! 😔\n\nVou conectá-lo com um especialista que pode resolver isso rapidamente.'
      ],
      context: 'technical_support',
      actions: ['escalate_to_technical']
    });

    // Informações sobre produtos/serviços
    this.intents.set('product_info', {
      patterns: [
        /produto|serviço|preço|valor|plano|funcionalidade/i,
        /product|service|price|plan|feature/i
      ],
      responses: [
        'Temos várias soluções que podem atender suas necessidades! 💼\n\nVou transferir você para nossa equipe comercial que pode explicar todos os detalhes.',
        'Ótima pergunta sobre nossos produtos! 🎯\n\nNossa equipe comercial tem todas as informações atualizadas. Vou conectá-lo agora.'
      ],
      context: 'sales',
      actions: ['escalate_to_sales']
    });

    // Reclamações
    this.intents.set('complaint', {
      patterns: [
        /reclamação|reclamar|insatisfeito|ruim|péssimo|horrível/i,
        /complaint|complain|unsatisfied|bad|terrible|awful/i
      ],
      responses: [
        'Lamento muito que você tenha tido uma experiência negativa. 😔\n\nSua opinião é muito importante para nós. Vou transferir você imediatamente para um supervisor.',
        'Peço desculpas pelo inconveniente! 🙏\n\nVamos resolver isso juntos. Conectando você com nossa equipe de atendimento especializada.'
      ],
      context: 'complaint',
      actions: ['escalate_to_supervisor', 'mark_as_priority']
    });

    // Elogios
    this.intents.set('compliment', {
      patterns: [
        /parabéns|excelente|ótimo|muito bom|adorei|perfeito/i,
        /congratulations|excellent|great|amazing|perfect|love it/i
      ],
      responses: [
        'Muito obrigado pelo elogio! 😊 Ficamos muito felizes em saber que você está satisfeito!',
        'Que bom saber que você gostou! 🎉 Vamos continuar trabalhando para manter essa qualidade!',
        'Obrigado pelas palavras gentis! ✨ Sua satisfação é nossa maior recompensa!'
      ],
      context: 'positive_feedback',
      actions: ['mark_as_positive']
    });

    // Horário de funcionamento
    this.intents.set('business_hours', {
      patterns: [
        /horário|funcionamento|atendimento|aberto|fechado/i,
        /hours|schedule|open|closed|available/i
      ],
      responses: [
        'Nosso atendimento funciona:\n\n🕐 Segunda a Sexta: 8h às 18h\n🕐 Sábado: 8h às 12h\n🕐 Domingo: Fechado\n\nFora desses horários, você pode deixar sua mensagem que responderemos assim que possível!'
      ],
      context: 'info'
    });

    // Contato
    this.intents.set('contact_info', {
      patterns: [
        /contato|telefone|email|endereço|localização/i,
        /contact|phone|email|address|location/i
      ],
      responses: [
        'Aqui estão nossas informações de contato:\n\n📞 Telefone: (11) 1234-5678\n📧 Email: <EMAIL>\n📍 Endereço: Rua das Empresas, 123 - São Paulo/SP\n\nTambém estamos disponíveis aqui no WhatsApp! 😊'
      ],
      context: 'info'
    });

    this.isInitialized = true;
    logger.info('Chatbot inicializado com sucesso');
  }

  // Processar mensagem do usuário
  async processMessage(userPhone, message, conversationId = null) {
    try {
      logger.debug('Processando mensagem do chatbot:', { userPhone, message });

      // Limpar e normalizar mensagem
      const cleanMessage = message.trim().toLowerCase();
      
      // Verificar contexto do usuário
      const userContext = this.context.get(userPhone) || {};
      
      // Identificar intent
      const intent = this.identifyIntent(cleanMessage);
      
      // Gerar resposta
      const response = await this.generateResponse(intent, userContext, {
        userPhone,
        message: cleanMessage,
        conversationId
      });

      // Atualizar contexto
      if (intent && intent.context) {
        this.context.set(userPhone, {
          ...userContext,
          lastIntent: intent.name,
          context: intent.context,
          timestamp: new Date()
        });
      }

      // Executar ações se necessário
      if (intent && intent.actions) {
        await this.executeActions(intent.actions, {
          userPhone,
          conversationId,
          message: cleanMessage
        });
      }

      return {
        response: response.text,
        intent: intent?.name || 'unknown',
        confidence: response.confidence,
        actions: intent?.actions || [],
        shouldEscalate: response.shouldEscalate || false
      };
    } catch (error) {
      logger.error('Erro no processamento do chatbot:', error);
      return {
        response: 'Desculpe, não consegui processar sua mensagem. Um atendente humano irá ajudá-lo em breve.',
        intent: 'error',
        confidence: 0,
        shouldEscalate: true
      };
    }
  }

  // Identificar intent da mensagem
  identifyIntent(message) {
    let bestMatch = null;
    let highestScore = 0;

    for (const [intentName, intentData] of this.intents) {
      for (const pattern of intentData.patterns) {
        if (pattern.test(message)) {
          const score = this.calculateMatchScore(message, pattern);
          if (score > highestScore) {
            highestScore = score;
            bestMatch = {
              name: intentName,
              ...intentData,
              score
            };
          }
        }
      }
    }

    return bestMatch;
  }

  // Calcular score de match
  calculateMatchScore(message, pattern) {
    const match = message.match(pattern);
    if (!match) return 0;

    // Score baseado no tamanho do match em relação à mensagem
    const matchLength = match[0].length;
    const messageLength = message.length;
    
    return (matchLength / messageLength) * 100;
  }

  // Gerar resposta
  async generateResponse(intent, userContext, messageData) {
    if (!intent) {
      return await this.generateFallbackResponse(messageData);
    }

    // Selecionar resposta aleatória
    const responses = intent.responses;
    const selectedResponse = responses[Math.floor(Math.random() * responses.length)];

    // Personalizar resposta se possível
    const personalizedResponse = await this.personalizeResponse(
      selectedResponse,
      messageData,
      userContext
    );

    return {
      text: personalizedResponse,
      confidence: intent.score,
      shouldEscalate: this.shouldEscalate(intent, userContext)
    };
  }

  // Gerar resposta de fallback
  async generateFallbackResponse(messageData) {
    const fallbackResponses = [
      'Não tenho certeza se entendi sua pergunta. 🤔\n\nPoderia reformular ou me dizer como posso ajudá-lo?',
      'Desculpe, não consegui compreender. 😅\n\nVou transferir você para um atendente humano que pode ajudá-lo melhor.',
      'Hmm, essa é uma pergunta interessante! 🤓\n\nVou conectá-lo com nossa equipe para uma resposta mais precisa.'
    ];

    // Tentar usar ML para análise de sentimento
    let sentiment = null;
    if (mlService.isAvailable()) {
      try {
        sentiment = mlService.analyzeSentiment(messageData.message);
      } catch (error) {
        logger.warn('Erro na análise de sentimento:', error);
      }
    }

    // Ajustar resposta baseada no sentimento
    let selectedResponse;
    if (sentiment && sentiment.sentiment === 'negative') {
      selectedResponse = fallbackResponses[1]; // Transferir para humano
    } else {
      selectedResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
    }

    return {
      text: selectedResponse,
      confidence: 30,
      shouldEscalate: sentiment?.sentiment === 'negative'
    };
  }

  // Personalizar resposta
  async personalizeResponse(response, messageData, userContext) {
    let personalizedResponse = response;

    // Buscar informações do cliente se disponível
    try {
      const customerData = await database.getClient().response.findFirst({
        where: { userPhone: messageData.userPhone },
        orderBy: { createdAt: 'desc' },
        include: {
          survey: {
            select: { title: true }
          }
        }
      });

      if (customerData) {
        // Adicionar informações contextuais
        if (customerData.score <= 6) {
          personalizedResponse += '\n\n💡 Vejo que você teve uma experiência anterior que pode ter sido melhorada. Vamos trabalhar juntos para resolver isso!';
        } else if (customerData.score >= 9) {
          personalizedResponse += '\n\n⭐ Obrigado por ser um cliente tão especial! Sua satisfação é nossa prioridade.';
        }
      }
    } catch (error) {
      logger.warn('Erro ao personalizar resposta:', error);
    }

    return personalizedResponse;
  }

  // Verificar se deve escalar para humano
  shouldEscalate(intent, userContext) {
    // Escalar automaticamente para certos intents
    const escalationIntents = ['complaint', 'technical_issue'];
    if (escalationIntents.includes(intent.name)) {
      return true;
    }

    // Escalar se o usuário está frustrado (múltiplas mensagens sem resolução)
    if (userContext.messageCount && userContext.messageCount > 3) {
      return true;
    }

    return false;
  }

  // Executar ações
  async executeActions(actions, messageData) {
    for (const action of actions) {
      try {
        await this.executeAction(action, messageData);
      } catch (error) {
        logger.error(`Erro ao executar ação ${action}:`, error);
      }
    }
  }

  // Executar ação específica
  async executeAction(action, messageData) {
    const { userPhone, conversationId } = messageData;

    switch (action) {
      case 'offer_survey':
        await this.offerSurvey(userPhone, conversationId);
        break;

      case 'escalate_to_technical':
        await this.escalateToTeam(conversationId, 'TECHNICAL', 'HIGH');
        break;

      case 'escalate_to_sales':
        await this.escalateToTeam(conversationId, 'SALES', 'NORMAL');
        break;

      case 'escalate_to_supervisor':
        await this.escalateToTeam(conversationId, 'SUPPORT', 'URGENT');
        break;

      case 'mark_as_priority':
        await this.markAsPriority(conversationId);
        break;

      case 'mark_as_positive':
        await this.markAsPositive(conversationId);
        break;

      default:
        logger.warn(`Ação desconhecida: ${action}`);
    }
  }

  // Oferecer pesquisa
  async offerSurvey(userPhone, conversationId) {
    try {
      // Buscar pesquisa ativa
      const activeSurvey = await database.getClient().survey.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' }
      });

      if (activeSurvey) {
        // Criar link da pesquisa
        const surveyLink = `${process.env.FRONTEND_URL || 'http://localhost:3001'}/survey/${activeSurvey.id}?phone=${encodeURIComponent(userPhone)}`;
        
        // Enviar link via WhatsApp (implementar integração)
        logger.info('Oferecendo pesquisa:', { userPhone, surveyId: activeSurvey.id });
      }
    } catch (error) {
      logger.error('Erro ao oferecer pesquisa:', error);
    }
  }

  // Escalar para equipe
  async escalateToTeam(conversationId, department, priority) {
    if (!conversationId) return;

    try {
      await database.getClient().conversation.update({
        where: { id: conversationId },
        data: {
          department,
          priority,
          status: 'OPEN',
          escalatedAt: new Date(),
          escalatedBy: 'chatbot'
        }
      });

      logger.info('Conversa escalada:', { conversationId, department, priority });
    } catch (error) {
      logger.error('Erro ao escalar conversa:', error);
    }
  }

  // Marcar como prioridade
  async markAsPriority(conversationId) {
    if (!conversationId) return;

    try {
      await database.getClient().conversation.update({
        where: { id: conversationId },
        data: {
          priority: 'HIGH',
          tags: {
            push: 'priority'
          }
        }
      });
    } catch (error) {
      logger.error('Erro ao marcar como prioridade:', error);
    }
  }

  // Marcar como positivo
  async markAsPositive(conversationId) {
    if (!conversationId) return;

    try {
      await database.getClient().conversation.update({
        where: { id: conversationId },
        data: {
          tags: {
            push: 'positive_feedback'
          }
        }
      });
    } catch (error) {
      logger.error('Erro ao marcar como positivo:', error);
    }
  }

  // Limpar contexto antigo
  cleanupOldContext() {
    const now = new Date();
    const maxAge = 30 * 60 * 1000; // 30 minutos

    for (const [userPhone, context] of this.context) {
      if (now - context.timestamp > maxAge) {
        this.context.delete(userPhone);
      }
    }
  }

  // Adicionar intent personalizado
  addCustomIntent(name, intentData) {
    this.intents.set(name, intentData);
    logger.info('Intent personalizado adicionado:', name);
  }

  // Obter estatísticas do chatbot
  getStats() {
    return {
      totalIntents: this.intents.size,
      activeContexts: this.context.size,
      isInitialized: this.isInitialized,
      intents: Array.from(this.intents.keys())
    };
  }

  // Treinar com novas mensagens
  async trainWithMessage(message, correctIntent, userFeedback) {
    try {
      // Salvar dados de treinamento
      await database.getClient().chatbotTraining.create({
        data: {
          message,
          correctIntent,
          userFeedback,
          createdAt: new Date()
        }
      });

      logger.info('Dados de treinamento salvos:', { message, correctIntent });
    } catch (error) {
      logger.error('Erro ao salvar dados de treinamento:', error);
    }
  }
}

// Inicializar limpeza periódica de contexto
setInterval(() => {
  if (chatbotService.isInitialized) {
    chatbotService.cleanupOldContext();
  }
}, 10 * 60 * 1000); // A cada 10 minutos

const chatbotService = new ChatbotService();
module.exports = chatbotService;
