const database = require('../config/database');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const contentAnalysisService = require('../services/contentAnalysisService');

// Listar departamentos
const getDepartments = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, search, isActive } = req.query;
  const skip = (page - 1) * limit;

  const where = {
    ...(search && {
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }),
    ...(isActive !== undefined && { isActive: isActive === 'true' })
  };

  const [departments, total] = await Promise.all([
    database.getClient().department.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      orderBy: { createdAt: 'desc' }
    }),
    database.getClient().department.count({ where })
  ]);

  res.json({
    departments,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

// Obter departamento específico
const getDepartment = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const department = await database.getClient().department.findUnique({
    where: { id }
  });

  if (!department) {
    return res.status(404).json({
      error: 'Departamento não encontrado',
      message: 'O departamento solicitado não foi encontrado'
    });
  }

  res.json(department);
});

// Criar departamento
const createDepartment = asyncHandler(async (req, res) => {
  const { name, keywords, description, isActive = true } = req.body;

  // Verificar se já existe um departamento com o mesmo nome
  const existingDepartment = await database.getClient().department.findUnique({
    where: { name }
  });

  if (existingDepartment) {
    return res.status(409).json({
      error: 'Departamento já existe',
      message: 'Já existe um departamento com este nome'
    });
  }

  const department = await database.getClient().department.create({
    data: {
      name,
      keywords,
      description,
      isActive
    }
  });

  logger.info('Departamento criado:', { departmentId: department.id, name });

  res.status(201).json({
    message: 'Departamento criado com sucesso',
    department
  });
});

// Atualizar departamento
const updateDepartment = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Verificar se o departamento existe
  const existingDepartment = await database.getClient().department.findUnique({
    where: { id }
  });

  if (!existingDepartment) {
    return res.status(404).json({
      error: 'Departamento não encontrado',
      message: 'O departamento solicitado não foi encontrado'
    });
  }

  // Se estiver atualizando o nome, verificar se não existe outro com o mesmo nome
  if (updateData.name && updateData.name !== existingDepartment.name) {
    const duplicateName = await database.getClient().department.findFirst({
      where: { 
        name: updateData.name,
        NOT: { id }
      }
    });

    if (duplicateName) {
      return res.status(409).json({
        error: 'Nome já existe',
        message: 'Já existe outro departamento com este nome'
      });
    }
  }

  const updatedDepartment = await database.getClient().department.update({
    where: { id },
    data: updateData
  });

  logger.info('Departamento atualizado:', { departmentId: id });

  res.json({
    message: 'Departamento atualizado com sucesso',
    department: updatedDepartment
  });
});

// Deletar departamento
const deleteDepartment = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Verificar se o departamento existe
  const existingDepartment = await database.getClient().department.findUnique({
    where: { id }
  });

  if (!existingDepartment) {
    return res.status(404).json({
      error: 'Departamento não encontrado',
      message: 'O departamento solicitado não foi encontrado'
    });
  }

  await database.getClient().department.delete({
    where: { id }
  });

  logger.info('Departamento deletado:', { departmentId: id });

  res.json({
    message: 'Departamento deletado com sucesso'
  });
});

// Testar análise de conteúdo
const testContentAnalysis = asyncHandler(async (req, res) => {
  const { text } = req.body;

  if (!text) {
    return res.status(400).json({
      error: 'Texto é obrigatório',
      message: 'Você deve fornecer um texto para análise'
    });
  }

  const analysis = await contentAnalysisService.analyzeFullContent(text);

  res.json({
    message: 'Análise de conteúdo realizada com sucesso',
    analysis
  });
});

// Obter estatísticas de direcionamento
const getDepartmentStats = asyncHandler(async (req, res) => {
  const { startDate, endDate, period = '30d' } = req.query;

  // Definir período se não especificado
  let dateFilter = {};
  if (startDate && endDate) {
    dateFilter = {
      createdAt: {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    };
  } else {
    const now = new Date();
    const periodDays = {
      '7d': 7,
      '30d': 30,
      '90d': 90
    };
    const days = periodDays[period] || 30;
    dateFilter = {
      createdAt: {
        gte: new Date(now.getTime() - days * 24 * 60 * 60 * 1000)
      }
    };
  }

  // Buscar conversas por departamento
  const conversationsByDepartment = await database.getClient().conversation.groupBy({
    by: ['department'],
    where: dateFilter,
    _count: {
      id: true
    },
    orderBy: {
      _count: {
        id: 'desc'
      }
    }
  });

  // Buscar total de conversas
  const totalConversations = await database.getClient().conversation.count({
    where: dateFilter
  });

  // Buscar departamentos ativos
  const activeDepartments = await database.getClient().department.findMany({
    where: { isActive: true },
    select: {
      id: true,
      name: true,
      keywords: true
    }
  });

  // Calcular estatísticas
  const stats = conversationsByDepartment.map(item => {
    const department = activeDepartments.find(d => d.name === item.department);
    return {
      department: item.department || 'Não classificado',
      departmentId: department?.id || null,
      count: item._count.id,
      percentage: totalConversations > 0 ? Math.round((item._count.id / totalConversations) * 100) : 0,
      keywords: department?.keywords || []
    };
  });

  res.json({
    period: {
      start: dateFilter.createdAt?.gte || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: dateFilter.createdAt?.lte || new Date()
    },
    totalConversations,
    departmentStats: stats,
    activeDepartments: activeDepartments.length
  });
});

// Sugerir palavras-chave baseadas em conversas
const suggestKeywords = asyncHandler(async (req, res) => {
  const { departmentId, limit = 10 } = req.query;

  if (!departmentId) {
    return res.status(400).json({
      error: 'ID do departamento é obrigatório',
      message: 'Você deve fornecer o ID do departamento'
    });
  }

  // Buscar departamento
  const department = await database.getClient().department.findUnique({
    where: { id: departmentId }
  });

  if (!department) {
    return res.status(404).json({
      error: 'Departamento não encontrado',
      message: 'O departamento solicitado não foi encontrado'
    });
  }

  // Buscar conversas do departamento
  const conversations = await database.getClient().conversation.findMany({
    where: { department: department.name },
    select: { messages: true },
    take: 100, // Limitar para performance
    orderBy: { createdAt: 'desc' }
  });

  // Extrair texto das mensagens
  const allText = conversations
    .flatMap(conv => Array.isArray(conv.messages) ? conv.messages : [])
    .filter(msg => msg.type === 'received' && msg.message)
    .map(msg => msg.message)
    .join(' ');

  if (!allText) {
    return res.json({
      department: department.name,
      suggestedKeywords: [],
      message: 'Não há mensagens suficientes para sugerir palavras-chave'
    });
  }

  // Analisar frequência de palavras
  const words = allText
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3); // Apenas palavras com mais de 3 caracteres

  // Contar frequência
  const wordCount = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });

  // Filtrar palavras já existentes como keywords
  const existingKeywords = department.keywords.map(k => k.toLowerCase());
  
  // Ordenar por frequência e filtrar
  const suggestedKeywords = Object.entries(wordCount)
    .filter(([word, count]) => 
      count >= 3 && // Aparecer pelo menos 3 vezes
      !existingKeywords.includes(word) &&
      !['que', 'para', 'com', 'por', 'uma', 'dos', 'das', 'isso', 'essa', 'este'].includes(word)
    )
    .sort(([, a], [, b]) => b - a)
    .slice(0, parseInt(limit))
    .map(([word, count]) => ({ word, frequency: count }));

  res.json({
    department: department.name,
    currentKeywords: department.keywords,
    suggestedKeywords,
    totalWordsAnalyzed: words.length
  });
});

module.exports = {
  getDepartments,
  getDepartment,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  testContentAnalysis,
  getDepartmentStats,
  suggestKeywords
};
