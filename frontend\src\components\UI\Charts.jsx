import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Line Chart Component
export const LineChartComponent = ({
  data = [],
  xKey = 'name',
  yKey = 'value',
  color = '#2563eb',
  height = 300,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" />}
          <XAxis dataKey={xKey} />
          <YAxis />
          {showTooltip && <Tooltip />}
          {showLegend && <Legend />}
          <Line
            type="monotone"
            dataKey={yKey}
            stroke={color}
            strokeWidth={2}
            dot={{ fill: color, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Area Chart Component
export const AreaChartComponent = ({
  data = [],
  xKey = 'name',
  yKey = 'value',
  color = '#2563eb',
  height = 300,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" />}
          <XAxis dataKey={xKey} />
          <YAxis />
          {showTooltip && <Tooltip />}
          {showLegend && <Legend />}
          <Area
            type="monotone"
            dataKey={yKey}
            stroke={color}
            fill={color}
            fillOpacity={0.3}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart Component
export const BarChartComponent = ({
  data = [],
  xKey = 'name',
  yKey = 'value',
  color = '#2563eb',
  height = 300,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" />}
          <XAxis dataKey={xKey} />
          <YAxis />
          {showTooltip && <Tooltip />}
          {showLegend && <Legend />}
          <Bar dataKey={yKey} fill={color} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart Component
export const PieChartComponent = ({
  data = [],
  nameKey = 'name',
  valueKey = 'value',
  colors = ['#2563eb', '#16a34a', '#dc2626', '#ca8a04', '#9333ea'],
  height = 300,
  showTooltip = true,
  showLegend = true,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="#8884d8"
            dataKey={valueKey}
            nameKey={nameKey}
            label
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Pie>
          {showTooltip && <Tooltip />}
          {showLegend && <Legend />}
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

// NPS Gauge Chart Component
export const NPSGauge = ({
  score = 0,
  size = 200,
  className = ''
}) => {
  const getColor = (score) => {
    if (score >= 70) return '#16a34a'; // Green
    if (score >= 50) return '#ca8a04'; // Yellow
    if (score >= 0) return '#f97316'; // Orange
    return '#dc2626'; // Red
  };

  const getLabel = (score) => {
    if (score >= 70) return 'Excelente';
    if (score >= 50) return 'Bom';
    if (score >= 0) return 'Regular';
    return 'Ruim';
  };

  const color = getColor(score);
  const label = getLabel(score);
  const normalizedScore = Math.max(-100, Math.min(100, score));
  const angle = ((normalizedScore + 100) / 200) * 180;

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="relative" style={{ width: size, height: size / 2 }}>
        <svg width={size} height={size / 2} className="overflow-visible">
          {/* Background arc */}
          <path
            d={`M 20 ${size / 2} A ${size / 2 - 20} ${size / 2 - 20} 0 0 1 ${size - 20} ${size / 2}`}
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="8"
          />
          {/* Progress arc */}
          <path
            d={`M 20 ${size / 2} A ${size / 2 - 20} ${size / 2 - 20} 0 0 1 ${size - 20} ${size / 2}`}
            fill="none"
            stroke={color}
            strokeWidth="8"
            strokeDasharray={`${(angle / 180) * Math.PI * (size / 2 - 20)} ${Math.PI * (size / 2 - 20)}`}
            strokeLinecap="round"
          />
          {/* Center text */}
          <text
            x={size / 2}
            y={size / 2 - 10}
            textAnchor="middle"
            className="text-2xl font-bold"
            fill={color}
          >
            {score}
          </text>
          <text
            x={size / 2}
            y={size / 2 + 10}
            textAnchor="middle"
            className="text-sm"
            fill="#6b7280"
          >
            {label}
          </text>
        </svg>
      </div>
      <div className="mt-2 text-center">
        <div className="text-xs text-gray-500">Net Promoter Score</div>
      </div>
    </div>
  );
};

// Multi-line Chart Component
export const MultiLineChart = ({
  data = [],
  lines = [],
  xKey = 'name',
  height = 300,
  showGrid = true,
  showTooltip = true,
  showLegend = true,
  className = ''
}) => {
  const colors = ['#2563eb', '#16a34a', '#dc2626', '#ca8a04', '#9333ea'];

  return (
    <div className={`w-full ${className}`} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" />}
          <XAxis dataKey={xKey} />
          <YAxis />
          {showTooltip && <Tooltip />}
          {showLegend && <Legend />}
          {lines.map((line, index) => (
            <Line
              key={line.key}
              type="monotone"
              dataKey={line.key}
              stroke={line.color || colors[index % colors.length]}
              strokeWidth={2}
              dot={{ fill: line.color || colors[index % colors.length], strokeWidth: 2, r: 4 }}
              name={line.name || line.key}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
