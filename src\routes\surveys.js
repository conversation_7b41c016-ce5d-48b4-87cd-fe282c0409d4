const express = require('express');
const { validate, surveySchemas } = require('../utils/validation');
const { authenticate, authorize } = require('../middleware/auth');
const {
  getSurveys,
  getSurvey,
  createSurvey,
  updateSurvey,
  deleteSurvey,
  generateLinks
} = require('../controllers/surveyController');

const router = express.Router();

// Todas as rotas de pesquisas requerem autenticação
router.use(authenticate);

// Listar pesquisas do usuário
router.get('/', getSurveys);

// Obter pesquisa específica
router.get('/:id', getSurvey);

// Criar nova pesquisa
router.post('/', 
  validate(surveySchemas.create),
  createSurvey
);

// Atualizar pesquisa
router.put('/:id', 
  validate(surveySchemas.update),
  updateSurvey
);

// Deletar pesquisa
router.delete('/:id', deleteSurvey);

// Gerar novos links e QR codes
router.post('/:id/generate-links', generateLinks);

// Duplicar pesquisa
router.post('/:id/duplicate', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const database = require('../config/database');
    const { logger } = require('../utils/logger');

    // Buscar pesquisa original
    const originalSurvey = await database.getClient().survey.findFirst({
      where: { id, userId }
    });

    if (!originalSurvey) {
      return res.status(404).json({
        error: 'Pesquisa não encontrada',
        message: 'A pesquisa solicitada não foi encontrada'
      });
    }

    // Criar cópia
    const duplicatedSurvey = await database.getClient().survey.create({
      data: {
        title: `${originalSurvey.title} (Cópia)`,
        description: originalSurvey.description,
        questions: originalSurvey.questions,
        isActive: false, // Criar como inativa por padrão
        userId
      }
    });

    logger.info('Pesquisa duplicada:', { 
      originalId: id, 
      duplicatedId: duplicatedSurvey.id 
    });

    res.status(201).json({
      message: 'Pesquisa duplicada com sucesso',
      survey: duplicatedSurvey
    });

  } catch (error) {
    logger.error('Erro ao duplicar pesquisa:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível duplicar a pesquisa'
    });
  }
});

// Ativar/desativar pesquisa
router.patch('/:id/toggle-status', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const database = require('../config/database');
    const { logger } = require('../utils/logger');

    // Buscar pesquisa
    const survey = await database.getClient().survey.findFirst({
      where: { id, userId }
    });

    if (!survey) {
      return res.status(404).json({
        error: 'Pesquisa não encontrada',
        message: 'A pesquisa solicitada não foi encontrada'
      });
    }

    // Alternar status
    const updatedSurvey = await database.getClient().survey.update({
      where: { id },
      data: { isActive: !survey.isActive }
    });

    logger.info('Status da pesquisa alterado:', { 
      surveyId: id, 
      newStatus: updatedSurvey.isActive 
    });

    res.json({
      message: `Pesquisa ${updatedSurvey.isActive ? 'ativada' : 'desativada'} com sucesso`,
      survey: updatedSurvey
    });

  } catch (error) {
    logger.error('Erro ao alterar status da pesquisa:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível alterar o status da pesquisa'
    });
  }
});

// Obter estatísticas da pesquisa
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const database = require('../config/database');

    // Verificar se a pesquisa pertence ao usuário
    const survey = await database.getClient().survey.findFirst({
      where: { id, userId }
    });

    if (!survey) {
      return res.status(404).json({
        error: 'Pesquisa não encontrada',
        message: 'A pesquisa solicitada não foi encontrada'
      });
    }

    // Buscar estatísticas
    const [
      totalResponses,
      promoters,
      neutrals,
      detractors,
      avgScore,
      recentResponses
    ] = await Promise.all([
      // Total de respostas
      database.getClient().response.count({
        where: { surveyId: id }
      }),
      
      // Promotores
      database.getClient().response.count({
        where: { surveyId: id, isPromoter: true }
      }),
      
      // Neutros
      database.getClient().response.count({
        where: { surveyId: id, isNeutral: true }
      }),
      
      // Detratores
      database.getClient().response.count({
        where: { surveyId: id, isDetractor: true }
      }),
      
      // Score médio
      database.getClient().response.aggregate({
        where: { surveyId: id, score: { not: null } },
        _avg: { score: true }
      }),
      
      // Respostas recentes
      database.getClient().response.findMany({
        where: { surveyId: id },
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          id: true,
          score: true,
          feedback: true,
          isDetractor: true,
          isPromoter: true,
          isNeutral: true,
          createdAt: true
        }
      })
    ]);

    // Calcular NPS
    const npsScore = totalResponses > 0 
      ? Math.round(((promoters - detractors) / totalResponses) * 100)
      : 0;

    res.json({
      survey: {
        id: survey.id,
        title: survey.title,
        isActive: survey.isActive
      },
      stats: {
        totalResponses,
        promoters,
        neutrals,
        detractors,
        npsScore,
        averageScore: avgScore._avg.score ? Math.round(avgScore._avg.score * 10) / 10 : null,
        responseRate: {
          promoters: totalResponses > 0 ? Math.round((promoters / totalResponses) * 100) : 0,
          neutrals: totalResponses > 0 ? Math.round((neutrals / totalResponses) * 100) : 0,
          detractors: totalResponses > 0 ? Math.round((detractors / totalResponses) * 100) : 0
        }
      },
      recentResponses
    });

  } catch (error) {
    logger.error('Erro ao buscar estatísticas da pesquisa:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter as estatísticas da pesquisa'
    });
  }
});

module.exports = router;
