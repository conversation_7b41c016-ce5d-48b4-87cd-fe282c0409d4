# Multi-stage Dockerfile para o backend Node.js
FROM node:18-alpine AS base

# Instalar dependências do sistema
RUN apk add --no-cache \
    openssl \
    curl \
    bash \
    netcat-openbsd \
    dumb-init

# Criar diretório da aplicação
WORKDIR /app

# Copiar arquivos de dependências
COPY package*.json ./
COPY prisma ./prisma/

# Stage para desenvolvimento
FROM base AS development
ENV NODE_ENV=development
RUN npm install
RUN npx prisma generate
COPY . .
CMD ["npm", "run", "dev"]

# Stage para build de produção
FROM base AS build
ENV NODE_ENV=production
RUN npm install --only=production && npm cache clean --force
RUN npx prisma generate
COPY . .

# Stage final de produção
FROM node:18-alpine AS production

# Instalar dependências mínimas do sistema
RUN apk add --no-cache \
    openssl \
    curl \
    bash \
    netcat-openbsd \
    dumb-init

WORKDIR /app

# Copiar node_modules e aplicação do stage de build
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/package*.json ./
COPY --from=build /app/prisma ./prisma
COPY --from=build /app/src ./src
COPY --from=build /app/.env.example ./.env.example

# Criar diretórios necessários
RUN mkdir -p logs uploads temp

# Criar usuário não-root
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Copiar scripts utilitários
COPY docker/backend/wait-for-it.sh ./wait-for-it.sh
RUN chmod +x ./wait-for-it.sh

# Alterar propriedade dos arquivos
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expor porta
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Usar dumb-init para gerenciar processos
ENTRYPOINT ["dumb-init", "--"]

# Comando para iniciar a aplicação
CMD ["./wait-for-it.sh", "postgres:5432", "--", "./wait-for-it.sh", "redis:6379", "--", "npm", "start"]
