const { asyncHandler } = require('../middleware/errorHandler');
const notificationService = require('../services/notificationService');
const database = require('../config/database');
const { logger } = require('../utils/logger');

// Obter notificações do usuário
const getNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    page = 1,
    limit = 20,
    unreadOnly = false,
    type
  } = req.query;

  const result = await notificationService.getUserNotifications(userId, {
    page: parseInt(page),
    limit: parseInt(limit),
    unreadOnly: unreadOnly === 'true',
    type
  });

  res.json(result);
});

// Marcar notificação como lida
const markAsRead = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  await notificationService.markAsRead(id, userId);

  res.json({
    message: 'Notificação marcada como lida',
    notificationId: id
  });
});

// Marcar todas as notificações como lidas
const markAllAsRead = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const count = await notificationService.markAllAsRead(userId);

  res.json({
    message: 'Todas as notificações marcadas como lidas',
    count
  });
});

// Obter contagem de notificações não lidas
const getUnreadCount = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const count = await database.getClient().notification.count({
    where: {
      userId,
      isRead: false
    }
  });

  res.json({ unreadCount: count });
});

// Criar subscription para push notifications
const createPushSubscription = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { endpoint, keys } = req.body;

  if (!endpoint || !keys || !keys.p256dh || !keys.auth) {
    return res.status(400).json({
      error: 'Dados da subscription inválidos',
      message: 'endpoint, keys.p256dh e keys.auth são obrigatórios'
    });
  }

  // Verificar se já existe uma subscription para este endpoint
  const existingSubscription = await database.getClient().pushSubscription.findFirst({
    where: {
      userId,
      endpoint
    }
  });

  if (existingSubscription) {
    // Atualizar subscription existente
    const updatedSubscription = await database.getClient().pushSubscription.update({
      where: { id: existingSubscription.id },
      data: {
        p256dh: keys.p256dh,
        auth: keys.auth,
        isActive: true,
        updatedAt: new Date()
      }
    });

    return res.json({
      message: 'Subscription atualizada com sucesso',
      subscription: updatedSubscription
    });
  }

  // Criar nova subscription
  const subscription = await database.getClient().pushSubscription.create({
    data: {
      userId,
      endpoint,
      p256dh: keys.p256dh,
      auth: keys.auth,
      isActive: true
    }
  });

  logger.info('Push subscription criada:', { userId, subscriptionId: subscription.id });

  res.status(201).json({
    message: 'Subscription criada com sucesso',
    subscription
  });
});

// Remover subscription
const removePushSubscription = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { endpoint } = req.body;

  if (!endpoint) {
    return res.status(400).json({
      error: 'Endpoint é obrigatório',
      message: 'Você deve fornecer o endpoint da subscription'
    });
  }

  const result = await database.getClient().pushSubscription.deleteMany({
    where: {
      userId,
      endpoint
    }
  });

  if (result.count === 0) {
    return res.status(404).json({
      error: 'Subscription não encontrada',
      message: 'Nenhuma subscription encontrada para este endpoint'
    });
  }

  logger.info('Push subscription removida:', { userId, endpoint });

  res.json({
    message: 'Subscription removida com sucesso',
    deletedCount: result.count
  });
});

// Obter chaves VAPID públicas
const getVapidPublicKey = asyncHandler(async (req, res) => {
  const publicKey = process.env.VAPID_PUBLIC_KEY;

  if (!publicKey) {
    return res.status(500).json({
      error: 'VAPID não configurado',
      message: 'Push notifications não estão disponíveis'
    });
  }

  res.json({
    publicKey
  });
});

// Testar notificação push
const testPushNotification = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { title = 'Teste', message = 'Esta é uma notificação de teste' } = req.body;

  await notificationService.createNotification({
    userId,
    type: 'test',
    title,
    message,
    data: {
      testId: Date.now()
    },
    priority: 'normal',
    channels: ['websocket', 'push']
  });

  res.json({
    message: 'Notificação de teste enviada',
    userId
  });
});

// Obter configurações de notificação do usuário
const getNotificationSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  let settings = await database.getClient().notificationSettings.findUnique({
    where: { userId }
  });

  // Se não existir, criar configurações padrão
  if (!settings) {
    settings = await database.getClient().notificationSettings.create({
      data: {
        userId,
        emailNotifications: true,
        pushNotifications: true,
        websocketNotifications: true,
        detractorAlerts: true,
        newResponseAlerts: true,
        newMessageAlerts: true,
        surveyCompletedAlerts: false,
        quietHoursEnabled: false,
        quietHoursStart: '22:00',
        quietHoursEnd: '08:00'
      }
    });
  }

  res.json(settings);
});

// Atualizar configurações de notificação
const updateNotificationSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const updateData = req.body;

  // Validar dados
  const allowedFields = [
    'emailNotifications',
    'pushNotifications', 
    'websocketNotifications',
    'detractorAlerts',
    'newResponseAlerts',
    'newMessageAlerts',
    'surveyCompletedAlerts',
    'quietHoursEnabled',
    'quietHoursStart',
    'quietHoursEnd'
  ];

  const filteredData = {};
  Object.keys(updateData).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredData[key] = updateData[key];
    }
  });

  const settings = await database.getClient().notificationSettings.upsert({
    where: { userId },
    update: {
      ...filteredData,
      updatedAt: new Date()
    },
    create: {
      userId,
      ...filteredData
    }
  });

  logger.info('Configurações de notificação atualizadas:', { userId });

  res.json({
    message: 'Configurações atualizadas com sucesso',
    settings
  });
});

// Obter estatísticas de notificações
const getNotificationStats = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { period = '7d' } = req.query;

  // Calcular data de início baseada no período
  const now = new Date();
  const periodDays = {
    '1d': 1,
    '7d': 7,
    '30d': 30,
    '90d': 90
  };
  const days = periodDays[period] || 7;
  const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

  const [
    totalNotifications,
    unreadNotifications,
    notificationsByType,
    notificationsByDay
  ] = await Promise.all([
    database.getClient().notification.count({
      where: {
        userId,
        createdAt: { gte: startDate }
      }
    }),
    database.getClient().notification.count({
      where: {
        userId,
        isRead: false
      }
    }),
    database.getClient().notification.groupBy({
      by: ['type'],
      where: {
        userId,
        createdAt: { gte: startDate }
      },
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } }
    }),
    database.getClient().$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM notifications
      WHERE user_id = ${userId}
        AND created_at >= ${startDate}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `
  ]);

  res.json({
    period: {
      start: startDate,
      end: now,
      days
    },
    summary: {
      total: totalNotifications,
      unread: unreadNotifications,
      readRate: totalNotifications > 0 
        ? Math.round(((totalNotifications - unreadNotifications) / totalNotifications) * 100)
        : 0
    },
    byType: notificationsByType.map(item => ({
      type: item.type,
      count: item._count.id
    })),
    timeline: notificationsByDay.map(row => ({
      date: row.date,
      count: parseInt(row.count)
    }))
  });
});

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  getUnreadCount,
  createPushSubscription,
  removePushSubscription,
  getVapidPublicKey,
  testPushNotification,
  getNotificationSettings,
  updateNotificationSettings,
  getNotificationStats
};
