const { asyncHandler } = require('../middleware/errorHandler');
const gamificationService = require('../services/gamificationService');
const database = require('../config/database');
const { logger } = require('../utils/logger');

// Obter perfil de gamificação do usuário
const getUserProfile = asyncHandler(async (req, res) => {
  const { userPhone } = req.params;

  if (!userPhone) {
    return res.status(400).json({
      error: 'Telefone obrigatório',
      message: 'Número de telefone é obrigatório'
    });
  }

  const profile = await gamificationService.getUserProfile(userPhone);

  if (!profile) {
    return res.status(404).json({
      error: 'Perfil não encontrado',
      message: 'Usu<PERSON>rio não possui perfil de gamificação'
    });
  }

  res.json({
    profile: profile.profile,
    badges: profile.badges,
    achievements: profile.achievements,
    stats: profile.stats,
    progress: {
      nextLevelPoints: profile.nextLevelPoints,
      progressToNextLevel: profile.progressToNextLevel
    }
  });
});

// Obter ranking (leaderboard)
const getLeaderboard = asyncHandler(async (req, res) => {
  const { limit = 10, period = 'all' } = req.query;

  if (parseInt(limit) > 100) {
    return res.status(400).json({
      error: 'Limite inválido',
      message: 'Limite máximo é 100 usuários'
    });
  }

  const validPeriods = ['all', 'week', 'month'];
  if (!validPeriods.includes(period)) {
    return res.status(400).json({
      error: 'Período inválido',
      message: 'Períodos válidos: all, week, month'
    });
  }

  const leaderboard = await gamificationService.getLeaderboard(parseInt(limit), period);

  res.json({
    leaderboard,
    period,
    total: leaderboard.length
  });
});

// Obter badges disponíveis
const getAvailableBadges = asyncHandler(async (req, res) => {
  const badges = Array.from(gamificationService.badgeDefinitions.values()).map(badge => ({
    id: badge.id,
    name: badge.name,
    description: badge.description,
    icon: badge.icon,
    rarity: badge.rarity,
    points: badge.points
  }));

  res.json({
    badges,
    total: badges.length
  });
});

// Obter conquistas disponíveis
const getAvailableAchievements = asyncHandler(async (req, res) => {
  const achievements = Array.from(gamificationService.achievementRules.values()).map(achievement => ({
    id: achievement.id,
    name: achievement.name,
    levels: achievement.levels
  }));

  res.json({
    achievements,
    total: achievements.length
  });
});

// Processar resposta manualmente (para testes)
const processResponse = asyncHandler(async (req, res) => {
  const { userPhone, score, feedback, surveyId, isFirstResponse = false } = req.body;

  if (!userPhone || score === undefined) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'userPhone e score são obrigatórios'
    });
  }

  if (score < 0 || score > 10) {
    return res.status(400).json({
      error: 'Score inválido',
      message: 'Score deve estar entre 0 e 10'
    });
  }

  const result = await gamificationService.processResponse({
    userPhone,
    score,
    feedback,
    surveyId,
    isFirstResponse
  });

  res.json({
    message: 'Resposta processada com sucesso',
    ...result
  });
});

// Obter recompensas disponíveis
const getRewards = asyncHandler(async (req, res) => {
  const { isActive = true } = req.query;

  const rewards = await database.getClient().reward.findMany({
    where: {
      isActive: isActive === 'true',
      ...(req.query.validUntil && {
        validUntil: { gte: new Date() }
      })
    },
    orderBy: { pointsCost: 'asc' }
  });

  res.json({
    rewards,
    total: rewards.length
  });
});

// Resgatar recompensa
const redeemReward = asyncHandler(async (req, res) => {
  const { userPhone, rewardId } = req.body;

  if (!userPhone || !rewardId) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'userPhone e rewardId são obrigatórios'
    });
  }

  // Verificar se recompensa existe e está ativa
  const reward = await database.getClient().reward.findUnique({
    where: { id: rewardId }
  });

  if (!reward) {
    return res.status(404).json({
      error: 'Recompensa não encontrada',
      message: 'A recompensa especificada não existe'
    });
  }

  if (!reward.isActive) {
    return res.status(400).json({
      error: 'Recompensa inativa',
      message: 'Esta recompensa não está mais disponível'
    });
  }

  if (reward.validUntil && reward.validUntil < new Date()) {
    return res.status(400).json({
      error: 'Recompensa expirada',
      message: 'Esta recompensa expirou'
    });
  }

  if (reward.maxRedemptions && reward.currentRedemptions >= reward.maxRedemptions) {
    return res.status(400).json({
      error: 'Recompensa esgotada',
      message: 'Esta recompensa atingiu o limite de resgates'
    });
  }

  // Verificar se usuário tem pontos suficientes
  const profile = await database.getClient().gamificationProfile.findUnique({
    where: { userPhone }
  });

  if (!profile) {
    return res.status(404).json({
      error: 'Perfil não encontrado',
      message: 'Usuário não possui perfil de gamificação'
    });
  }

  if (profile.totalPoints < reward.pointsCost) {
    return res.status(400).json({
      error: 'Pontos insuficientes',
      message: `Você precisa de ${reward.pointsCost} pontos. Você tem ${profile.totalPoints} pontos.`,
      pointsNeeded: reward.pointsCost - profile.totalPoints
    });
  }

  // Processar resgate
  const [redemption] = await database.getClient().$transaction([
    // Criar resgate
    database.getClient().rewardRedemption.create({
      data: {
        userPhone,
        rewardId,
        pointsUsed: reward.pointsCost
      }
    }),
    // Debitar pontos do usuário
    database.getClient().gamificationProfile.update({
      where: { userPhone },
      data: {
        totalPoints: { decrement: reward.pointsCost }
      }
    }),
    // Incrementar contador de resgates
    database.getClient().reward.update({
      where: { id: rewardId },
      data: {
        currentRedemptions: { increment: 1 }
      }
    })
  ]);

  logger.info('Recompensa resgatada:', {
    userPhone,
    rewardId,
    pointsUsed: reward.pointsCost
  });

  res.json({
    message: 'Recompensa resgatada com sucesso!',
    redemption: {
      id: redemption.id,
      reward: {
        name: reward.name,
        description: reward.description,
        value: reward.value
      },
      pointsUsed: reward.pointsCost,
      status: redemption.status,
      redeemedAt: redemption.redeemedAt
    },
    remainingPoints: profile.totalPoints - reward.pointsCost
  });
});

// Obter histórico de resgates do usuário
const getUserRedemptions = asyncHandler(async (req, res) => {
  const { userPhone } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const [redemptions, total] = await Promise.all([
    database.getClient().rewardRedemption.findMany({
      where: { userPhone },
      include: {
        reward: {
          select: {
            name: true,
            description: true,
            value: true,
            type: true
          }
        }
      },
      orderBy: { redeemedAt: 'desc' },
      skip: (parseInt(page) - 1) * parseInt(limit),
      take: parseInt(limit)
    }),
    database.getClient().rewardRedemption.count({
      where: { userPhone }
    })
  ]);

  res.json({
    redemptions,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit))
    }
  });
});

// Obter desafios ativos
const getActiveChallenges = asyncHandler(async (req, res) => {
  const now = new Date();

  const challenges = await database.getClient().challenge.findMany({
    where: {
      isActive: true,
      startDate: { lte: now },
      endDate: { gte: now }
    },
    orderBy: { endDate: 'asc' }
  });

  res.json({
    challenges,
    total: challenges.length
  });
});

// Participar de desafio
const joinChallenge = asyncHandler(async (req, res) => {
  const { userPhone, challengeId } = req.body;

  if (!userPhone || !challengeId) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'userPhone e challengeId são obrigatórios'
    });
  }

  // Verificar se desafio existe e está ativo
  const challenge = await database.getClient().challenge.findUnique({
    where: { id: challengeId }
  });

  if (!challenge) {
    return res.status(404).json({
      error: 'Desafio não encontrado',
      message: 'O desafio especificado não existe'
    });
  }

  if (!challenge.isActive) {
    return res.status(400).json({
      error: 'Desafio inativo',
      message: 'Este desafio não está mais ativo'
    });
  }

  const now = new Date();
  if (now < challenge.startDate || now > challenge.endDate) {
    return res.status(400).json({
      error: 'Desafio fora do período',
      message: 'Este desafio não está no período de participação'
    });
  }

  // Verificar se já está participando
  const existingParticipation = await database.getClient().challengeParticipation.findUnique({
    where: {
      userPhone_challengeId: {
        userPhone,
        challengeId
      }
    }
  });

  if (existingParticipation) {
    return res.status(400).json({
      error: 'Já participando',
      message: 'Você já está participando deste desafio'
    });
  }

  // Criar participação
  const participation = await database.getClient().challengeParticipation.create({
    data: {
      userPhone,
      challengeId
    }
  });

  res.json({
    message: 'Participação no desafio confirmada!',
    participation: {
      id: participation.id,
      challenge: {
        name: challenge.name,
        description: challenge.description,
        target: challenge.target,
        reward: challenge.reward
      },
      progress: participation.progress,
      joinedAt: participation.joinedAt
    }
  });
});

// Obter progresso do usuário em desafios
const getUserChallenges = asyncHandler(async (req, res) => {
  const { userPhone } = req.params;

  const participations = await database.getClient().challengeParticipation.findMany({
    where: { userPhone },
    include: {
      challenge: true
    },
    orderBy: { joinedAt: 'desc' }
  });

  res.json({
    participations: participations.map(p => ({
      id: p.id,
      challenge: p.challenge,
      progress: p.progress,
      completed: p.completed,
      completedAt: p.completedAt,
      joinedAt: p.joinedAt,
      progressPercentage: Math.round((p.progress / p.challenge.target) * 100)
    })),
    total: participations.length
  });
});

// Obter estatísticas gerais do sistema
const getSystemStats = asyncHandler(async (req, res) => {
  const stats = await gamificationService.getSystemStats();

  res.json(stats);
});

// Simular conquista de badge (para testes)
const simulateBadge = asyncHandler(async (req, res) => {
  const { userPhone, badgeId } = req.body;

  if (!userPhone || !badgeId) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'userPhone e badgeId são obrigatórios'
    });
  }

  // Verificar se badge existe
  const badgeDefinition = gamificationService.badgeDefinitions.get(badgeId);
  if (!badgeDefinition) {
    return res.status(404).json({
      error: 'Badge não encontrado',
      message: 'O badge especificado não existe'
    });
  }

  // Verificar se usuário já possui o badge
  const existingBadge = await database.getClient().userBadge.findUnique({
    where: {
      userPhone_badgeId: {
        userPhone,
        badgeId
      }
    }
  });

  if (existingBadge) {
    return res.status(400).json({
      error: 'Badge já conquistado',
      message: 'Usuário já possui este badge'
    });
  }

  // Conceder badge
  await database.getClient().userBadge.create({
    data: {
      userPhone,
      badgeId
    }
  });

  // Adicionar pontos
  await gamificationService.updateUserPoints(userPhone, badgeDefinition.points);

  res.json({
    message: 'Badge concedido com sucesso!',
    badge: {
      id: badgeId,
      name: badgeDefinition.name,
      description: badgeDefinition.description,
      icon: badgeDefinition.icon,
      points: badgeDefinition.points
    }
  });
});

module.exports = {
  getUserProfile,
  getLeaderboard,
  getAvailableBadges,
  getAvailableAchievements,
  processResponse,
  getRewards,
  redeemReward,
  getUserRedemptions,
  getActiveChallenges,
  joinChallenge,
  getUserChallenges,
  getSystemStats,
  simulateBadge
};
