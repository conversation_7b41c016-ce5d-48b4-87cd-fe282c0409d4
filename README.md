# UNIFORMS2 - Sistema SaaS de Pesquisas de Satisfação

Sistema completo para criação, envio e gerenciamento de pesquisas de satisfação com integração WhatsApp, análise NPS e caixa de entrada estilo ChatWOOT.

## 🚀 Funcionalidades

### ✅ Pesquisas Personalizáveis
- Criação de pesquisas com perguntas customizáveis
- Geração automática de QR Codes e links
- Formulários HTML responsivos
- Classificação automática NPS (Promotores, Neutros, Detratores)

### 📱 Integração WhatsApp
- Suporte a Evolution API e WAHA
- Envio de pesquisas via WhatsApp
- Botões interativos e mensagens de texto
- Gerenciamento de múltiplas instâncias

### 💬 Caixa de Entrada (ChatWOOT Style)
- Interface para gerenciar conversas
- Sistema de prioridades (Alta para detratores)
- Histórico completo de mensagens
- Filtros por departamento e status

### 🤖 Automação Inteligente
- Análise automática de conteúdo por palavras-chave
- Direcionamento para departamentos específicos
- Integração com n8n via webhooks
- Detratores enviados automaticamente para agente A

### 📊 Dashboard e Métricas
- Métricas NPS em tempo real
- Gráficos de satisfação
- Relatórios detalhados
- Análise de tendências

## 🛠️ Tecnologias

### Backend
- **Node.js** com Express
- **PostgreSQL** com Prisma ORM
- **Redis** para cache e filas
- **JWT** para autenticação
- **Socket.io** para tempo real

### Frontend
- **React** com hooks
- **Material-UI** ou **Tailwind CSS**
- **Chart.js** para gráficos
- **Socket.io-client** para tempo real

### Integrações
- **Evolution API** ou **WAHA** para WhatsApp
- **n8n** para automação (webhooks)
- **QR Code** para geração de códigos

## 📋 Pré-requisitos

- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- Evolution API ou WAHA configurado
- n8n (opcional, para automação avançada)

## 🚀 Instalação

### 1. Clone o repositório
```bash
git clone <repository-url>
cd uniforms-survey-system
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

### 4. Configure o banco de dados
```bash
npm run prisma:migrate
npm run prisma:generate
```

### 5. Inicie o servidor
```bash
# Desenvolvimento
npm run dev

# Produção
npm start
```

## 📁 Estrutura do Projeto

```
├── src/
│   ├── controllers/     # Controladores da API
│   ├── routes/         # Rotas da API
│   ├── middleware/     # Middlewares
│   ├── services/       # Lógica de negócio
│   ├── utils/          # Utilitários
│   └── config/         # Configurações
├── prisma/             # Schema do banco
├── frontend/           # Aplicação React
├── public/             # Arquivos estáticos
└── docs/              # Documentação
```

## 🔧 Configuração

### WhatsApp API (Evolution API)
```bash
# Configure no .env
WHATSAPP_API_URL=http://localhost:8080
WHATSAPP_API_KEY=your-api-key
WHATSAPP_INSTANCE_NAME=uniforms-instance
```

### n8n Webhooks
```bash
# Configure no .env
N8N_WEBHOOK_URL=https://your-n8n-url.com
N8N_DETRACTOR_WEBHOOK=/webhook/detractor
N8N_DEPARTMENT_WEBHOOK=/webhook/department
```

## 📚 API Endpoints

### Autenticação
- `POST /api/auth/login` - Login
- `POST /api/auth/register` - Registro
- `POST /api/auth/refresh` - Refresh token

### Pesquisas
- `GET /api/surveys` - Listar pesquisas
- `POST /api/surveys` - Criar pesquisa
- `GET /api/surveys/:id` - Obter pesquisa
- `PUT /api/surveys/:id` - Atualizar pesquisa
- `DELETE /api/surveys/:id` - Deletar pesquisa

### Respostas
- `GET /api/responses` - Listar respostas
- `POST /api/responses` - Criar resposta
- `GET /api/responses/analytics` - Métricas NPS

### WhatsApp
- `POST /api/whatsapp/send` - Enviar mensagem
- `GET /api/whatsapp/instances` - Listar instâncias
- `POST /webhooks/whatsapp` - Webhook mensagens

## 🧪 Testes

```bash
# Executar todos os testes
npm test

# Testes com coverage
npm run test:coverage

# Testes em modo watch
npm run test:watch
```

## 📈 Monitoramento

- Health check: `GET /health`
- Logs: `./logs/app.log`
- Métricas: Dashboard interno

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 🆘 Suporte

Para suporte, abra uma issue no GitHub ou entre em contato via email.

---

**UNIFORMS2** - Sistema SaaS de Pesquisas de Satisfação 🚀
