const { asyncHand<PERSON> } = require('../middleware/errorHandler');
const securityService = require('../services/securityService');
const database = require('../config/database');
const { logger } = require('../utils/logger');

// Configurar 2FA
const setup2FA = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const setup = await securityService.setup2FA(userId);

  res.json({
    message: '2FA configurado. Escaneie o QR Code com seu app autenticador.',
    qrCode: setup.qrCode,
    manualEntryKey: setup.manualEntryKey,
    backupCodes: setup.backupCodes
  });
});

// Verificar e ativar 2FA
const verify2FA = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({
      error: 'Token obrigatório',
      message: 'Código do autenticador é obrigatório'
    });
  }

  const result = await securityService.verify2FA(userId, token);

  if (result.success) {
    // Marcar 2FA como verificado na sessão
    req.session.twoFactorVerified = true;
    req.session.twoFactorVerifiedAt = new Date();
  }

  res.json(result);
});

// Desativar 2FA
const disable2FA = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      error: 'Senha obrigatória',
      message: 'Senha é necessária para desativar 2FA'
    });
  }

  const result = await securityService.disable2FA(userId, password);
  
  // Remover verificação 2FA da sessão
  if (result.success) {
    delete req.session.twoFactorVerified;
    delete req.session.twoFactorVerifiedAt;
  }

  res.json(result);
});

// Verificar 2FA para sessão atual
const verify2FASession = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({
      error: 'Token obrigatório',
      message: 'Código do autenticador é obrigatório'
    });
  }

  const result = await securityService.verify2FA(userId, token);

  if (result.success) {
    req.session.twoFactorVerified = true;
    req.session.twoFactorVerifiedAt = new Date();
    
    res.json({
      success: true,
      message: '2FA verificado com sucesso',
      sessionVerified: true
    });
  } else {
    res.status(400).json(result);
  }
});

// Obter status de segurança do usuário
const getSecurityStatus = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const user = await database.getClient().user.findUnique({
    where: { id: userId },
    select: {
      twoFactorEnabled: true,
      twoFactorEnabledAt: true,
      createdAt: true,
      updatedAt: true
    }
  });

  // Buscar atividades recentes
  const recentActivities = await database.getClient().securityLog.findMany({
    where: {
      userId,
      createdAt: {
        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Últimos 30 dias
      }
    },
    orderBy: { createdAt: 'desc' },
    take: 10
  });

  // Buscar sessões ativas
  const activeSessions = await database.getClient().sessionToken.count({
    where: {
      userId,
      isActive: true,
      expiresAt: { gt: new Date() }
    }
  });

  res.json({
    user: {
      twoFactorEnabled: user.twoFactorEnabled,
      twoFactorEnabledAt: user.twoFactorEnabledAt,
      accountAge: Math.floor((new Date() - user.createdAt) / (1000 * 60 * 60 * 24))
    },
    session: {
      twoFactorVerified: !!req.session?.twoFactorVerified,
      twoFactorVerifiedAt: req.session?.twoFactorVerifiedAt,
      activeSessions
    },
    recentActivities: recentActivities.map(activity => ({
      event: activity.event,
      createdAt: activity.createdAt,
      metadata: activity.metadata
    }))
  });
});

// Alterar senha com validação de força
const changePassword = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      error: 'Senhas obrigatórias',
      message: 'Senha atual e nova senha são obrigatórias'
    });
  }

  // Buscar usuário
  const user = await database.getClient().user.findUnique({
    where: { id: userId },
    select: { password: true }
  });

  // Verificar senha atual
  const validPassword = await securityService.verifyPassword(currentPassword, user.password);
  if (!validPassword) {
    await securityService.logSecurityEvent(userId, 'password_change_failed', {
      reason: 'invalid_current_password',
      timestamp: new Date()
    });

    return res.status(400).json({
      error: 'Senha atual inválida',
      message: 'A senha atual está incorreta'
    });
  }

  // Validar força da nova senha
  const passwordStrength = securityService.validatePasswordStrength(newPassword);
  if (!passwordStrength.isValid) {
    return res.status(400).json({
      error: 'Senha fraca',
      message: 'A nova senha não atende aos requisitos de segurança',
      requirements: passwordStrength.requirements,
      strength: passwordStrength.strength
    });
  }

  // Hash da nova senha
  const hashedPassword = await securityService.hashPassword(newPassword);

  // Atualizar senha
  await database.getClient().user.update({
    where: { id: userId },
    data: { password: hashedPassword }
  });

  // Log do evento
  await securityService.logSecurityEvent(userId, 'password_changed', {
    strength: passwordStrength.strength,
    timestamp: new Date()
  });

  // Invalidar todas as sessões exceto a atual
  await database.getClient().sessionToken.updateMany({
    where: {
      userId,
      token: { not: req.session?.token || '' }
    },
    data: {
      isActive: false,
      revokedAt: new Date()
    }
  });

  res.json({
    message: 'Senha alterada com sucesso',
    passwordStrength: passwordStrength.strength,
    sessionsRevoked: true
  });
});

// Obter logs de segurança do usuário
const getSecurityLogs = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 20, event } = req.query;

  const whereClause = {
    userId,
    ...(event && { event })
  };

  const [logs, total] = await Promise.all([
    database.getClient().securityLog.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      skip: (parseInt(page) - 1) * parseInt(limit),
      take: parseInt(limit)
    }),
    database.getClient().securityLog.count({ where: whereClause })
  ]);

  res.json({
    logs: logs.map(log => ({
      id: log.id,
      event: log.event,
      metadata: log.metadata,
      createdAt: log.createdAt
    })),
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit))
    }
  });
});

// Revogar sessões
const revokeSessions = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { revokeAll = false } = req.body;

  let revokedCount = 0;

  if (revokeAll) {
    // Revogar todas as sessões
    const result = await database.getClient().sessionToken.updateMany({
      where: {
        userId,
        isActive: true
      },
      data: {
        isActive: false,
        revokedAt: new Date()
      }
    });
    revokedCount = result.count;
  } else {
    // Revogar apenas outras sessões (manter a atual)
    const result = await database.getClient().sessionToken.updateMany({
      where: {
        userId,
        isActive: true,
        token: { not: req.session?.token || '' }
      },
      data: {
        isActive: false,
        revokedAt: new Date()
      }
    });
    revokedCount = result.count;
  }

  await securityService.logSecurityEvent(userId, 'sessions_revoked', {
    revokedCount,
    revokeAll,
    timestamp: new Date()
  });

  res.json({
    message: `${revokedCount} sessões revogadas com sucesso`,
    revokedCount,
    revokeAll
  });
});

// Validar força da senha
const validatePassword = asyncHandler(async (req, res) => {
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      error: 'Senha obrigatória',
      message: 'Senha é obrigatória para validação'
    });
  }

  const validation = securityService.validatePasswordStrength(password);

  res.json({
    isValid: validation.isValid,
    strength: validation.strength,
    score: validation.score,
    requirements: validation.requirements,
    recommendations: generatePasswordRecommendations(validation.requirements)
  });
});

// Gerar recomendações de senha
function generatePasswordRecommendations(requirements) {
  const recommendations = [];

  if (!requirements.minLength) {
    recommendations.push('Use pelo menos 8 caracteres');
  }
  if (!requirements.hasUppercase) {
    recommendations.push('Inclua pelo menos uma letra maiúscula');
  }
  if (!requirements.hasLowercase) {
    recommendations.push('Inclua pelo menos uma letra minúscula');
  }
  if (!requirements.hasNumbers) {
    recommendations.push('Inclua pelo menos um número');
  }
  if (!requirements.hasSpecialChars) {
    recommendations.push('Inclua pelo menos um caractere especial (!@#$%^&*)');
  }
  if (!requirements.noCommonPatterns) {
    recommendations.push('Evite senhas comuns como "123456" ou "password"');
  }

  return recommendations;
}

// Relatório de segurança (apenas admins)
const getSecurityReport = asyncHandler(async (req, res) => {
  const { period = '30d' } = req.query;

  const report = await securityService.getSecurityReport(period);

  res.json(report);
});

// Limpar logs antigos (apenas admins)
const cleanupLogs = asyncHandler(async (req, res) => {
  const { daysToKeep = 90 } = req.body;

  if (daysToKeep < 30) {
    return res.status(400).json({
      error: 'Período inválido',
      message: 'Deve manter pelo menos 30 dias de logs'
    });
  }

  const result = await securityService.cleanupOldLogs(parseInt(daysToKeep));

  res.json({
    message: 'Limpeza de logs concluída',
    ...result,
    daysKept: parseInt(daysToKeep)
  });
});

// Obter configurações de segurança do sistema
const getSecurityConfig = asyncHandler(async (req, res) => {
  res.json({
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      blockCommonPasswords: true
    },
    twoFactor: {
      enabled: true,
      required: false, // Pode ser configurado por admin
      backupCodesCount: 10
    },
    rateLimiting: {
      auth: { max: 5, windowMs: 15 * 60 * 1000 },
      api: { max: 1000, windowMs: 15 * 60 * 1000 },
      strict: { max: 10, windowMs: 5 * 60 * 1000 }
    },
    session: {
      maxAge: 24 * 60 * 60 * 1000, // 24 horas
      requireReauth: 2 * 60 * 60 * 1000 // 2 horas para ações sensíveis
    },
    audit: {
      enabled: true,
      retentionDays: 90
    }
  });
});

// Testar detecção de atividade suspeita
const testSuspiciousActivity = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { activity } = req.body;

  if (!activity) {
    return res.status(400).json({
      error: 'Atividade obrigatória',
      message: 'Dados da atividade são obrigatórios para teste'
    });
  }

  const detection = await securityService.detectSuspiciousActivity(userId, {
    ...activity,
    timestamp: new Date()
  });

  res.json({
    message: 'Teste de detecção executado',
    detection,
    note: 'Este é apenas um teste - nenhuma ação foi tomada'
  });
});

module.exports = {
  setup2FA,
  verify2FA,
  disable2FA,
  verify2FASession,
  getSecurityStatus,
  changePassword,
  getSecurityLogs,
  revokeSessions,
  validatePassword,
  getSecurityReport,
  cleanupLogs,
  getSecurityConfig,
  testSuspiciousActivity
};
