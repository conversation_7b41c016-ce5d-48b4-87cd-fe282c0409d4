import React, { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { api } from '../../services/api';
import { toast } from 'react-hot-toast';

const GamificationProfile = ({ userPhone }) => {
  const [selectedTab, setSelectedTab] = useState('profile');

  // Buscar perfil de gamificação
  const {
    data: profileData,
    isLoading: profileLoading,
    error: profileError
  } = useQuery(
    ['gamification-profile', userPhone],
    () => api.get(`/gamification/profile/${userPhone}`).then(res => res.data),
    {
      enabled: !!userPhone,
      refetchInterval: 30000 // Atualizar a cada 30 segundos
    }
  );

  // Buscar ranking
  const {
    data: leaderboardData,
    isLoading: leaderboardLoading
  } = useQuery(
    'gamification-leaderboard',
    () => api.get('/gamification/leaderboard?limit=10').then(res => res.data),
    {
      refetchInterval: 60000 // Atualizar a cada minuto
    }
  );

  // Buscar recompensas
  const {
    data: rewardsData,
    isLoading: rewardsLoading
  } = useQuery(
    'gamification-rewards',
    () => api.get('/gamification/rewards').then(res => res.data),
    {
      refetchInterval: 300000 // Atualizar a cada 5 minutos
    }
  );

  if (profileLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (profileError || !profileData) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-red-800 font-medium">Perfil não encontrado</h3>
        <p className="text-red-600 mt-2">
          Não foi possível carregar o perfil de gamificação.
        </p>
      </div>
    );
  }

  const { profile, badges, achievements, stats, progress } = profileData;

  const getRarityColor = (rarity) => {
    const colors = {
      common: 'bg-gray-100 text-gray-800',
      rare: 'bg-blue-100 text-blue-800',
      epic: 'bg-purple-100 text-purple-800',
      legendary: 'bg-yellow-100 text-yellow-800'
    };
    return colors[rarity] || colors.common;
  };

  const handleRedeemReward = async (rewardId) => {
    try {
      const response = await api.post('/gamification/rewards/redeem', {
        userPhone,
        rewardId
      });

      toast.success(response.data.message);
      
      // Refetch dados
      queryClient.invalidateQueries(['gamification-profile', userPhone]);
      queryClient.invalidateQueries('gamification-rewards');
    } catch (error) {
      toast.error(error.response?.data?.message || 'Erro ao resgatar recompensa');
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header do Perfil */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Perfil de Gamificação</h1>
            <p className="text-blue-100 mt-1">Telefone: {userPhone}</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{profile.totalPoints}</div>
            <div className="text-blue-100">pontos totais</div>
          </div>
        </div>

        {/* Barra de Progresso do Nível */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Nível {profile.currentLevel}</span>
            <span className="text-sm text-blue-100">
              {progress.progressToNextLevel}% para o próximo nível
            </span>
          </div>
          <div className="w-full bg-blue-500 rounded-full h-2">
            <div
              className="bg-white h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.progressToNextLevel}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Estatísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-blue-600">{stats.totalResponses}</div>
          <div className="text-gray-600 text-sm">Respostas</div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-green-600">{badges.length}</div>
          <div className="text-gray-600 text-sm">Badges</div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-purple-600">{achievements.length}</div>
          <div className="text-gray-600 text-sm">Conquistas</div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-orange-600">{profile.currentStreak}</div>
          <div className="text-gray-600 text-sm">Sequência Atual</div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'profile', label: 'Perfil', icon: '👤' },
              { id: 'badges', label: 'Badges', icon: '🏆' },
              { id: 'achievements', label: 'Conquistas', icon: '🎯' },
              { id: 'leaderboard', label: 'Ranking', icon: '🏅' },
              { id: 'rewards', label: 'Recompensas', icon: '🎁' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Tab: Perfil */}
          {selectedTab === 'profile' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Estatísticas Detalhadas</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Média de Satisfação:</span>
                      <span className="font-medium">{stats.averageScore?.toFixed(1) || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Respostas de Promotor:</span>
                      <span className="font-medium">{stats.promoterResponses || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Feedbacks Dados:</span>
                      <span className="font-medium">{stats.feedbackCount || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Maior Sequência:</span>
                      <span className="font-medium">{stats.maxStreak || 0} dias</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Dias Participando:</span>
                      <span className="font-medium">{stats.daysSinceFirstResponse || 0}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Progresso do Nível</h3>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-blue-600 mb-2">
                      {profile.currentLevel}
                    </div>
                    <div className="text-gray-600 mb-4">Nível Atual</div>
                    <div className="text-sm text-gray-500">
                      {progress.nextLevelPoints} pontos para o próximo nível
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tab: Badges */}
          {selectedTab === 'badges' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Badges Conquistados ({badges.length})</h3>
              {badges.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">🏆</div>
                  <p>Nenhum badge conquistado ainda.</p>
                  <p className="text-sm mt-1">Continue participando para ganhar badges!</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {badges.map((badge) => (
                    <div key={badge.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-2">
                        <div className="text-3xl">{badge.icon}</div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(badge.rarity)}`}>
                          {badge.rarity}
                        </span>
                      </div>
                      <h4 className="font-medium text-gray-900">{badge.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{badge.description}</p>
                      <div className="flex items-center justify-between mt-3">
                        <span className="text-sm font-medium text-blue-600">+{badge.points} pts</span>
                        <span className="text-xs text-gray-500">
                          {new Date(badge.earnedAt).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Tab: Conquistas */}
          {selectedTab === 'achievements' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Conquistas Desbloqueadas ({achievements.length})</h3>
              {achievements.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">🎯</div>
                  <p>Nenhuma conquista desbloqueada ainda.</p>
                  <p className="text-sm mt-1">Continue participando para desbloquear conquistas!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {achievements.map((achievement) => (
                    <div key={achievement.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{achievement.name}</h4>
                          <p className="text-sm text-gray-600 mt-1">
                            Nível {achievement.level}: {achievement.title}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-purple-600">+{achievement.points} pts</div>
                          <div className="text-xs text-gray-500">
                            {new Date(achievement.earnedAt).toLocaleDateString('pt-BR')}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Tab: Ranking */}
          {selectedTab === 'leaderboard' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Ranking Global</h3>
              {leaderboardLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  {leaderboardData?.leaderboard?.map((user, index) => (
                    <div
                      key={user.userPhone}
                      className={`flex items-center justify-between p-4 rounded-lg ${
                        user.userPhone === userPhone ? 'bg-blue-50 border-2 border-blue-200' : 'bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                          index === 0 ? 'bg-yellow-400 text-yellow-900' :
                          index === 1 ? 'bg-gray-300 text-gray-700' :
                          index === 2 ? 'bg-orange-400 text-orange-900' :
                          'bg-gray-200 text-gray-600'
                        }`}>
                          {user.rank}
                        </div>
                        <div>
                          <div className="font-medium">
                            {user.userPhone === userPhone ? 'Você' : `Usuário ${user.userPhone.slice(-4)}`}
                          </div>
                          <div className="text-sm text-gray-600">
                            Nível {user.currentLevel} • {user.totalResponses} respostas
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-blue-600">{user.totalPoints}</div>
                        <div className="text-xs text-gray-500">pontos</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Tab: Recompensas */}
          {selectedTab === 'rewards' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Recompensas Disponíveis</h3>
              <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-blue-800 font-medium">Seus pontos disponíveis:</span>
                  <span className="text-2xl font-bold text-blue-600">{profile.totalPoints}</span>
                </div>
              </div>
              
              {rewardsLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {rewardsData?.rewards?.map((reward) => (
                    <div key={reward.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{reward.name}</h4>
                        <span className="text-lg font-bold text-green-600">{reward.pointsCost} pts</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{reward.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {reward.type}
                        </span>
                        <button
                          onClick={() => handleRedeemReward(reward.id)}
                          disabled={profile.totalPoints < reward.pointsCost}
                          className={`px-4 py-2 rounded-md text-sm font-medium ${
                            profile.totalPoints >= reward.pointsCost
                              ? 'bg-blue-600 text-white hover:bg-blue-700'
                              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          }`}
                        >
                          {profile.totalPoints >= reward.pointsCost ? 'Resgatar' : 'Pontos Insuficientes'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GamificationProfile;
