const database = require('../config/database');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const contentAnalysisService = require('../services/contentAnalysisService');
const whatsappService = require('../services/whatsappService');

// Listar conversas
const getConversations = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    priority, 
    department,
    search,
    startDate,
    endDate,
    instanceId
  } = req.query;
  
  const skip = (page - 1) * limit;

  // Construir filtros
  const where = {
    ...(status && { status }),
    ...(priority && { priority }),
    ...(department && { department }),
    ...(instanceId && { instanceId }),
    ...(search && {
      OR: [
        { userPhone: { contains: search } },
        { department: { contains: search, mode: 'insensitive' } }
      ]
    }),
    ...(startDate && endDate && {
      createdAt: {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    })
  };

  const [conversations, total] = await Promise.all([
    database.getClient().conversation.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      orderBy: [
        { priority: 'desc' }, // Prioridade alta primeiro
        { updatedAt: 'desc' }  // Mais recentes primeiro
      ],
      include: {
        instance: {
          select: {
            instanceName: true,
            isActive: true
          }
        },
        response: {
          select: {
            id: true,
            score: true,
            isDetractor: true,
            isPromoter: true,
            isNeutral: true,
            feedback: true
          }
        }
      }
    }),
    database.getClient().conversation.count({ where })
  ]);

  // Processar conversas para incluir informações adicionais
  const processedConversations = conversations.map(conv => {
    const messages = Array.isArray(conv.messages) ? conv.messages : [];
    const lastMessage = messages[messages.length - 1];
    
    return {
      ...conv,
      messagesCount: messages.length,
      lastMessage: lastMessage ? {
        text: lastMessage.message?.substring(0, 100) + (lastMessage.message?.length > 100 ? '...' : ''),
        timestamp: lastMessage.timestamp,
        type: lastMessage.type
      } : null,
      unreadCount: messages.filter(m => m.type === 'received' && !m.read).length
    };
  });

  res.json({
    conversations: processedConversations,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

// Obter conversa específica
const getConversation = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const conversation = await database.getClient().conversation.findUnique({
    where: { id },
    include: {
      instance: {
        select: {
          instanceName: true,
          isActive: true
        }
      },
      response: {
        select: {
          id: true,
          score: true,
          isDetractor: true,
          isPromoter: true,
          isNeutral: true,
          feedback: true,
          survey: {
            select: {
              id: true,
              title: true
            }
          }
        }
      }
    }
  });

  if (!conversation) {
    return res.status(404).json({
      error: 'Conversa não encontrada',
      message: 'A conversa solicitada não foi encontrada'
    });
  }

  // Marcar mensagens como lidas
  const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
  const updatedMessages = messages.map(msg => ({
    ...msg,
    read: msg.type === 'received' ? true : msg.read
  }));

  if (JSON.stringify(messages) !== JSON.stringify(updatedMessages)) {
    await database.getClient().conversation.update({
      where: { id },
      data: { messages: updatedMessages }
    });
  }

  res.json({
    ...conversation,
    messages: updatedMessages
  });
});

// Enviar mensagem na conversa
const sendMessage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { message } = req.body;
  const userId = req.user.id;

  if (!message || message.trim().length === 0) {
    return res.status(400).json({
      error: 'Mensagem é obrigatória',
      message: 'Você deve fornecer uma mensagem'
    });
  }

  // Buscar conversa
  const conversation = await database.getClient().conversation.findUnique({
    where: { id },
    include: {
      instance: true
    }
  });

  if (!conversation) {
    return res.status(404).json({
      error: 'Conversa não encontrada',
      message: 'A conversa solicitada não foi encontrada'
    });
  }

  try {
    // Enviar mensagem via WhatsApp
    await whatsappService.sendTextMessage(
      conversation.instance.instanceName,
      conversation.userPhone,
      message
    );

    // Adicionar mensagem ao histórico
    const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
    const newMessage = {
      id: Date.now().toString(),
      type: 'sent',
      message: message.trim(),
      timestamp: new Date().toISOString(),
      userId,
      userName: req.user.name
    };

    messages.push(newMessage);

    // Atualizar conversa
    await database.getClient().conversation.update({
      where: { id },
      data: { 
        messages,
        status: 'IN_PROGRESS',
        updatedAt: new Date()
      }
    });

    logger.info('Mensagem enviada na conversa:', { 
      conversationId: id, 
      userId, 
      messageLength: message.length 
    });

    res.json({
      message: 'Mensagem enviada com sucesso',
      sentMessage: newMessage
    });

  } catch (error) {
    logger.error('Erro ao enviar mensagem:', error);
    res.status(500).json({
      error: 'Erro ao enviar mensagem',
      message: 'Não foi possível enviar a mensagem via WhatsApp'
    });
  }
});

// Atualizar status da conversa
const updateConversationStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  const validStatuses = ['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'];
  
  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      error: 'Status inválido',
      message: `Status deve ser um dos seguintes: ${validStatuses.join(', ')}`
    });
  }

  const conversation = await database.getClient().conversation.findUnique({
    where: { id }
  });

  if (!conversation) {
    return res.status(404).json({
      error: 'Conversa não encontrada',
      message: 'A conversa solicitada não foi encontrada'
    });
  }

  const updatedConversation = await database.getClient().conversation.update({
    where: { id },
    data: { 
      status,
      updatedAt: new Date()
    }
  });

  logger.info('Status da conversa atualizado:', { 
    conversationId: id, 
    oldStatus: conversation.status, 
    newStatus: status 
  });

  res.json({
    message: 'Status da conversa atualizado com sucesso',
    conversation: updatedConversation
  });
});

// Atualizar prioridade da conversa
const updateConversationPriority = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { priority } = req.body;

  const validPriorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT'];
  
  if (!validPriorities.includes(priority)) {
    return res.status(400).json({
      error: 'Prioridade inválida',
      message: `Prioridade deve ser uma das seguintes: ${validPriorities.join(', ')}`
    });
  }

  const conversation = await database.getClient().conversation.findUnique({
    where: { id }
  });

  if (!conversation) {
    return res.status(404).json({
      error: 'Conversa não encontrada',
      message: 'A conversa solicitada não foi encontrada'
    });
  }

  const updatedConversation = await database.getClient().conversation.update({
    where: { id },
    data: { 
      priority,
      updatedAt: new Date()
    }
  });

  logger.info('Prioridade da conversa atualizada:', { 
    conversationId: id, 
    oldPriority: conversation.priority, 
    newPriority: priority 
  });

  res.json({
    message: 'Prioridade da conversa atualizada com sucesso',
    conversation: updatedConversation
  });
});

// Atribuir departamento à conversa
const assignDepartment = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { department } = req.body;

  if (!department) {
    return res.status(400).json({
      error: 'Departamento é obrigatório',
      message: 'Você deve especificar um departamento'
    });
  }

  const conversation = await database.getClient().conversation.findUnique({
    where: { id }
  });

  if (!conversation) {
    return res.status(404).json({
      error: 'Conversa não encontrada',
      message: 'A conversa solicitada não foi encontrada'
    });
  }

  const updatedConversation = await database.getClient().conversation.update({
    where: { id },
    data: { 
      department,
      updatedAt: new Date()
    }
  });

  logger.info('Departamento atribuído à conversa:', { 
    conversationId: id, 
    department 
  });

  res.json({
    message: 'Departamento atribuído com sucesso',
    conversation: updatedConversation
  });
});

// Analisar conteúdo da conversa
const analyzeConversation = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const conversation = await database.getClient().conversation.findUnique({
    where: { id }
  });

  if (!conversation) {
    return res.status(404).json({
      error: 'Conversa não encontrada',
      message: 'A conversa solicitada não foi encontrada'
    });
  }

  // Extrair texto das mensagens recebidas
  const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
  const receivedMessages = messages
    .filter(msg => msg.type === 'received')
    .map(msg => msg.message)
    .join(' ');

  if (!receivedMessages) {
    return res.json({
      message: 'Não há mensagens suficientes para análise',
      analysis: null
    });
  }

  const analysis = await contentAnalysisService.analyzeFullContent(receivedMessages);

  // Se a análise sugerir um departamento diferente, atualizar
  if (analysis.department.department && 
      analysis.department.confidence > 70 && 
      conversation.department !== analysis.department.department) {
    
    await database.getClient().conversation.update({
      where: { id },
      data: { 
        department: analysis.department.department,
        priority: analysis.extractedInfo.priority === 'high' ? 'HIGH' : conversation.priority
      }
    });

    logger.info('Conversa reclassificada automaticamente:', { 
      conversationId: id, 
      newDepartment: analysis.department.department,
      confidence: analysis.department.confidence
    });
  }

  res.json({
    message: 'Análise da conversa realizada com sucesso',
    analysis,
    suggestedActions: {
      changeDepartment: analysis.department.department !== conversation.department,
      increasePriority: analysis.extractedInfo.priority === 'high' && conversation.priority === 'NORMAL'
    }
  });
});

// Obter estatísticas das conversas
const getConversationStats = asyncHandler(async (req, res) => {
  const { period = '7d', department, instanceId } = req.query;

  // Definir período
  const now = new Date();
  const periodDays = {
    '1d': 1,
    '7d': 7,
    '30d': 30,
    '90d': 90
  };
  const days = periodDays[period] || 7;
  const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

  const baseWhere = {
    createdAt: { gte: startDate },
    ...(department && { department }),
    ...(instanceId && { instanceId })
  };

  const [
    totalConversations,
    openConversations,
    inProgressConversations,
    resolvedConversations,
    closedConversations,
    highPriorityConversations,
    conversationsByDepartment,
    conversationsByDay
  ] = await Promise.all([
    database.getClient().conversation.count({ where: baseWhere }),
    database.getClient().conversation.count({ where: { ...baseWhere, status: 'OPEN' } }),
    database.getClient().conversation.count({ where: { ...baseWhere, status: 'IN_PROGRESS' } }),
    database.getClient().conversation.count({ where: { ...baseWhere, status: 'RESOLVED' } }),
    database.getClient().conversation.count({ where: { ...baseWhere, status: 'CLOSED' } }),
    database.getClient().conversation.count({ where: { ...baseWhere, priority: { in: ['HIGH', 'URGENT'] } } }),
    
    database.getClient().conversation.groupBy({
      by: ['department'],
      where: baseWhere,
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } }
    }),

    database.getClient().$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM conversations
      WHERE created_at >= ${startDate}
        ${department ? `AND department = '${department}'` : ''}
        ${instanceId ? `AND instance_id = '${instanceId}'` : ''}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `
  ]);

  res.json({
    period: {
      start: startDate,
      end: now,
      days
    },
    summary: {
      total: totalConversations,
      open: openConversations,
      inProgress: inProgressConversations,
      resolved: resolvedConversations,
      closed: closedConversations,
      highPriority: highPriorityConversations
    },
    distribution: {
      byStatus: {
        open: openConversations,
        inProgress: inProgressConversations,
        resolved: resolvedConversations,
        closed: closedConversations
      },
      byDepartment: conversationsByDepartment.map(item => ({
        department: item.department || 'Não classificado',
        count: item._count.id
      }))
    },
    timeline: conversationsByDay.map(row => ({
      date: row.date,
      count: parseInt(row.count)
    }))
  });
});

module.exports = {
  getConversations,
  getConversation,
  sendMessage,
  updateConversationStatus,
  updateConversationPriority,
  assignDepartment,
  analyzeConversation,
  getConversationStats
};
