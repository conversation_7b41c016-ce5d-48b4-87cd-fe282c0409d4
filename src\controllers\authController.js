const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const database = require('../config/database');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');

// Gerar token JWT
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Registro de usuário
const register = asyncHandler(async (req, res) => {
  const { name, email, password, role = 'USER' } = req.body;

  // Verificar se usuário já existe
  const existingUser = await database.getClient().user.findUnique({
    where: { email }
  });

  if (existingUser) {
    return res.status(409).json({
      error: 'Usuário já existe',
      message: 'Este email já está cadastrado'
    });
  }

  // Hash da senha
  const hashedPassword = await bcrypt.hash(password, 12);

  // Criar usuário
  const user = await database.getClient().user.create({
    data: {
      name,
      email,
      password: hashedPassword,
      role
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      createdAt: true
    }
  });

  // Gerar token
  const token = generateToken(user.id);

  logger.info('Novo usuário registrado:', { email: user.email, role: user.role });

  res.status(201).json({
    message: 'Usuário criado com sucesso',
    user,
    token
  });
});

// Login de usuário
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Buscar usuário
  const user = await database.getClient().user.findUnique({
    where: { email }
  });

  if (!user) {
    return res.status(401).json({
      error: 'Credenciais inválidas',
      message: 'Email ou senha incorretos'
    });
  }

  // Verificar senha
  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid) {
    return res.status(401).json({
      error: 'Credenciais inválidas',
      message: 'Email ou senha incorretos'
    });
  }

  // Gerar token
  const token = generateToken(user.id);

  // Remover senha da resposta
  const { password: _, ...userWithoutPassword } = user;

  logger.info('Usuário logado:', { email: user.email });

  res.json({
    message: 'Login realizado com sucesso',
    user: userWithoutPassword,
    token
  });
});

// Obter perfil do usuário
const getProfile = asyncHandler(async (req, res) => {
  const user = await database.getClient().user.findUnique({
    where: { id: req.user.id },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          surveys: true
        }
      }
    }
  });

  if (!user) {
    return res.status(404).json({
      error: 'Usuário não encontrado',
      message: 'Perfil do usuário não foi encontrado'
    });
  }

  res.json({
    user: {
      ...user,
      surveysCount: user._count.surveys
    }
  });
});

// Atualizar perfil do usuário
const updateProfile = asyncHandler(async (req, res) => {
  const { name, email } = req.body;
  const userId = req.user.id;

  // Verificar se email já está em uso por outro usuário
  if (email) {
    const existingUser = await database.getClient().user.findFirst({
      where: {
        email,
        NOT: { id: userId }
      }
    });

    if (existingUser) {
      return res.status(409).json({
        error: 'Email já está em uso',
        message: 'Este email já está sendo usado por outro usuário'
      });
    }
  }

  // Atualizar usuário
  const updatedUser = await database.getClient().user.update({
    where: { id: userId },
    data: {
      ...(name && { name }),
      ...(email && { email })
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      createdAt: true,
      updatedAt: true
    }
  });

  logger.info('Perfil atualizado:', { userId, email: updatedUser.email });

  res.json({
    message: 'Perfil atualizado com sucesso',
    user: updatedUser
  });
});

// Alterar senha
const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  // Buscar usuário com senha
  const user = await database.getClient().user.findUnique({
    where: { id: userId }
  });

  if (!user) {
    return res.status(404).json({
      error: 'Usuário não encontrado'
    });
  }

  // Verificar senha atual
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);

  if (!isCurrentPasswordValid) {
    return res.status(400).json({
      error: 'Senha atual incorreta',
      message: 'A senha atual informada está incorreta'
    });
  }

  // Hash da nova senha
  const hashedNewPassword = await bcrypt.hash(newPassword, 12);

  // Atualizar senha
  await database.getClient().user.update({
    where: { id: userId },
    data: { password: hashedNewPassword }
  });

  logger.info('Senha alterada:', { userId });

  res.json({
    message: 'Senha alterada com sucesso'
  });
});

// Refresh token
const refreshToken = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // Gerar novo token
  const token = generateToken(userId);

  res.json({
    message: 'Token renovado com sucesso',
    token
  });
});

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  refreshToken
};
