const database = require('../config/database');
const { logger } = require('../utils/logger');
const notificationService = require('./notificationService');

class GamificationService {
  constructor() {
    this.pointsConfig = {
      SURVEY_RESPONSE: 10,
      FIRST_RESPONSE: 50,
      PROMOTER_RESPONSE: 25,
      FEEDBACK_PROVIDED: 15,
      CONVERSATION_RESOLVED: 30,
      DAILY_STREAK: 20,
      WEEKLY_STREAK: 100,
      MONTHLY_STREAK: 500
    };

    this.badgeDefinitions = new Map();
    this.achievementRules = new Map();
    this.isInitialized = false;
    
    this.initializeGamification();
  }

  // Inicializar sistema de gamificação
  async initializeGamification() {
    try {
      await this.loadBadgeDefinitions();
      await this.loadAchievementRules();
      
      this.isInitialized = true;
      logger.info('✅ Sistema de gamificação inicializado');
    } catch (error) {
      logger.error('Erro ao inicializar gamificação:', error);
    }
  }

  // Carregar definições de badges
  async loadBadgeDefinitions() {
    // Badge de Primeiro Passo
    this.badgeDefinitions.set('first_response', {
      id: 'first_response',
      name: 'Primeiro Passo',
      description: 'Respondeu sua primeira pesquisa',
      icon: '🎯',
      rarity: 'common',
      points: 50,
      condition: (stats) => stats.totalResponses >= 1
    });

    // Badge de Participante Ativo
    this.badgeDefinitions.set('active_participant', {
      id: 'active_participant',
      name: 'Participante Ativo',
      description: 'Respondeu 10 pesquisas',
      icon: '⭐',
      rarity: 'common',
      points: 100,
      condition: (stats) => stats.totalResponses >= 10
    });

    // Badge de Promotor
    this.badgeDefinitions.set('promoter', {
      id: 'promoter',
      name: 'Promotor da Marca',
      description: 'Deu nota 9 ou 10 em 5 pesquisas',
      icon: '🏆',
      rarity: 'rare',
      points: 200,
      condition: (stats) => stats.promoterResponses >= 5
    });

    // Badge de Feedback Master
    this.badgeDefinitions.set('feedback_master', {
      id: 'feedback_master',
      name: 'Mestre do Feedback',
      description: 'Deixou comentários em 20 pesquisas',
      icon: '💬',
      rarity: 'rare',
      points: 150,
      condition: (stats) => stats.feedbackCount >= 20
    });

    // Badge de Streak
    this.badgeDefinitions.set('streak_warrior', {
      id: 'streak_warrior',
      name: 'Guerreiro da Sequência',
      description: 'Manteve uma sequência de 7 dias respondendo pesquisas',
      icon: '🔥',
      rarity: 'epic',
      points: 300,
      condition: (stats) => stats.maxStreak >= 7
    });

    // Badge de Influenciador
    this.badgeDefinitions.set('influencer', {
      id: 'influencer',
      name: 'Influenciador',
      description: 'Suas respostas geraram 5 melhorias no sistema',
      icon: '👑',
      rarity: 'legendary',
      points: 500,
      condition: (stats) => stats.improvementsGenerated >= 5
    });

    // Badge de Veterano
    this.badgeDefinitions.set('veteran', {
      id: 'veteran',
      name: 'Veterano',
      description: 'Participando há mais de 1 ano',
      icon: '🎖️',
      rarity: 'epic',
      points: 400,
      condition: (stats) => stats.daysSinceFirstResponse >= 365
    });

    // Badge de Perfeccionista
    this.badgeDefinitions.set('perfectionist', {
      id: 'perfectionist',
      name: 'Perfeccionista',
      description: 'Deu nota 10 em 10 pesquisas consecutivas',
      icon: '💎',
      rarity: 'legendary',
      points: 1000,
      condition: (stats) => stats.perfectStreak >= 10
    });

    logger.info(`${this.badgeDefinitions.size} badges carregados`);
  }

  // Carregar regras de conquistas
  async loadAchievementRules() {
    // Conquista de Participação
    this.achievementRules.set('participation_milestone', {
      id: 'participation_milestone',
      name: 'Marco de Participação',
      levels: [
        { level: 1, threshold: 5, points: 50, title: 'Iniciante' },
        { level: 2, threshold: 25, points: 150, title: 'Participante' },
        { level: 3, threshold: 50, points: 300, title: 'Engajado' },
        { level: 4, threshold: 100, points: 500, title: 'Dedicado' },
        { level: 5, threshold: 250, points: 1000, title: 'Mestre' }
      ],
      getValue: (stats) => stats.totalResponses
    });

    // Conquista de Qualidade
    this.achievementRules.set('quality_milestone', {
      id: 'quality_milestone',
      name: 'Marco de Qualidade',
      levels: [
        { level: 1, threshold: 8.0, points: 100, title: 'Satisfeito' },
        { level: 2, threshold: 8.5, points: 200, title: 'Muito Satisfeito' },
        { level: 3, threshold: 9.0, points: 400, title: 'Promotor' },
        { level: 4, threshold: 9.5, points: 800, title: 'Super Promotor' },
        { level: 5, threshold: 10.0, points: 1500, title: 'Perfeição' }
      ],
      getValue: (stats) => stats.averageScore
    });

    // Conquista de Consistência
    this.achievementRules.set('consistency_milestone', {
      id: 'consistency_milestone',
      name: 'Marco de Consistência',
      levels: [
        { level: 1, threshold: 3, points: 75, title: 'Consistente' },
        { level: 2, threshold: 7, points: 200, title: 'Disciplinado' },
        { level: 3, threshold: 14, points: 500, title: 'Determinado' },
        { level: 4, threshold: 30, points: 1000, title: 'Incansável' },
        { level: 5, threshold: 60, points: 2000, title: 'Lendário' }
      ],
      getValue: (stats) => stats.maxStreak
    });

    logger.info(`${this.achievementRules.size} regras de conquista carregadas`);
  }

  // Processar resposta e atribuir pontos
  async processResponse(responseData) {
    try {
      const { userPhone, score, feedback, surveyId, isFirstResponse } = responseData;

      let totalPoints = 0;
      const earnedRewards = [];

      // Pontos base por resposta
      totalPoints += this.pointsConfig.SURVEY_RESPONSE;
      earnedRewards.push({
        type: 'points',
        reason: 'Resposta à pesquisa',
        points: this.pointsConfig.SURVEY_RESPONSE
      });

      // Bônus primeira resposta
      if (isFirstResponse) {
        totalPoints += this.pointsConfig.FIRST_RESPONSE;
        earnedRewards.push({
          type: 'points',
          reason: 'Primeira resposta',
          points: this.pointsConfig.FIRST_RESPONSE
        });
      }

      // Bônus promotor
      if (score >= 9) {
        totalPoints += this.pointsConfig.PROMOTER_RESPONSE;
        earnedRewards.push({
          type: 'points',
          reason: 'Resposta de promotor',
          points: this.pointsConfig.PROMOTER_RESPONSE
        });
      }

      // Bônus feedback
      if (feedback && feedback.trim().length > 10) {
        totalPoints += this.pointsConfig.FEEDBACK_PROVIDED;
        earnedRewards.push({
          type: 'points',
          reason: 'Feedback detalhado',
          points: this.pointsConfig.FEEDBACK_PROVIDED
        });
      }

      // Atualizar pontos do usuário
      await this.updateUserPoints(userPhone, totalPoints);

      // Verificar badges e conquistas
      const newBadges = await this.checkBadges(userPhone);
      const newAchievements = await this.checkAchievements(userPhone);

      // Verificar streak
      const streakReward = await this.updateStreak(userPhone);
      if (streakReward) {
        earnedRewards.push(streakReward);
        totalPoints += streakReward.points;
      }

      // Notificar usuário sobre recompensas
      if (newBadges.length > 0 || newAchievements.length > 0 || streakReward) {
        await this.notifyRewards(userPhone, {
          points: totalPoints,
          badges: newBadges,
          achievements: newAchievements,
          streak: streakReward
        });
      }

      return {
        pointsEarned: totalPoints,
        rewards: earnedRewards,
        newBadges,
        newAchievements,
        streak: streakReward
      };
    } catch (error) {
      logger.error('Erro ao processar gamificação:', error);
      return { pointsEarned: 0, rewards: [] };
    }
  }

  // Atualizar pontos do usuário
  async updateUserPoints(userPhone, points) {
    try {
      // Buscar ou criar perfil de gamificação
      let profile = await database.getClient().gamificationProfile.findUnique({
        where: { userPhone }
      });

      if (!profile) {
        profile = await database.getClient().gamificationProfile.create({
          data: {
            userPhone,
            totalPoints: points,
            currentLevel: 1,
            totalResponses: 1,
            lastResponseAt: new Date()
          }
        });
      } else {
        profile = await database.getClient().gamificationProfile.update({
          where: { userPhone },
          data: {
            totalPoints: { increment: points },
            totalResponses: { increment: 1 },
            lastResponseAt: new Date()
          }
        });
      }

      // Verificar mudança de nível
      const newLevel = this.calculateLevel(profile.totalPoints);
      if (newLevel > profile.currentLevel) {
        await database.getClient().gamificationProfile.update({
          where: { userPhone },
          data: { currentLevel: newLevel }
        });

        // Notificar sobre novo nível
        await this.notifyLevelUp(userPhone, newLevel);
      }

      return profile;
    } catch (error) {
      logger.error('Erro ao atualizar pontos:', error);
      throw error;
    }
  }

  // Calcular nível baseado nos pontos
  calculateLevel(totalPoints) {
    // Fórmula: Level = floor(sqrt(points / 100)) + 1
    return Math.floor(Math.sqrt(totalPoints / 100)) + 1;
  }

  // Verificar badges conquistados
  async checkBadges(userPhone) {
    try {
      const stats = await this.getUserStats(userPhone);
      const currentBadges = await this.getUserBadges(userPhone);
      const currentBadgeIds = new Set(currentBadges.map(b => b.badgeId));
      
      const newBadges = [];

      for (const [badgeId, badge] of this.badgeDefinitions) {
        if (!currentBadgeIds.has(badgeId) && badge.condition(stats)) {
          // Conquistar badge
          await database.getClient().userBadge.create({
            data: {
              userPhone,
              badgeId,
              earnedAt: new Date()
            }
          });

          // Adicionar pontos do badge
          await this.updateUserPoints(userPhone, badge.points);

          newBadges.push({
            id: badgeId,
            name: badge.name,
            description: badge.description,
            icon: badge.icon,
            rarity: badge.rarity,
            points: badge.points
          });

          logger.info('Badge conquistado:', { userPhone, badgeId, badge: badge.name });
        }
      }

      return newBadges;
    } catch (error) {
      logger.error('Erro ao verificar badges:', error);
      return [];
    }
  }

  // Verificar conquistas
  async checkAchievements(userPhone) {
    try {
      const stats = await this.getUserStats(userPhone);
      const currentAchievements = await this.getUserAchievements(userPhone);
      const newAchievements = [];

      for (const [achievementId, achievement] of this.achievementRules) {
        const currentValue = achievement.getValue(stats);
        const currentAchievement = currentAchievements.find(a => a.achievementId === achievementId);
        const currentLevel = currentAchievement?.level || 0;

        // Verificar próximo nível
        const nextLevel = achievement.levels.find(l => l.level > currentLevel && currentValue >= l.threshold);
        
        if (nextLevel) {
          // Conquistar novo nível
          await database.getClient().userAchievement.upsert({
            where: {
              userPhone_achievementId: {
                userPhone,
                achievementId
              }
            },
            update: {
              level: nextLevel.level,
              earnedAt: new Date()
            },
            create: {
              userPhone,
              achievementId,
              level: nextLevel.level,
              earnedAt: new Date()
            }
          });

          // Adicionar pontos da conquista
          await this.updateUserPoints(userPhone, nextLevel.points);

          newAchievements.push({
            id: achievementId,
            name: achievement.name,
            level: nextLevel.level,
            title: nextLevel.title,
            points: nextLevel.points,
            currentValue,
            threshold: nextLevel.threshold
          });

          logger.info('Conquista desbloqueada:', { 
            userPhone, 
            achievementId, 
            level: nextLevel.level 
          });
        }
      }

      return newAchievements;
    } catch (error) {
      logger.error('Erro ao verificar conquistas:', error);
      return [];
    }
  }

  // Atualizar streak do usuário
  async updateStreak(userPhone) {
    try {
      const profile = await database.getClient().gamificationProfile.findUnique({
        where: { userPhone }
      });

      if (!profile) return null;

      const now = new Date();
      const lastResponse = profile.lastResponseAt;
      const daysDiff = Math.floor((now - lastResponse) / (1000 * 60 * 60 * 24));

      let newStreak = profile.currentStreak || 0;
      let streakReward = null;

      if (daysDiff === 0) {
        // Mesma data, manter streak
        return null;
      } else if (daysDiff === 1) {
        // Dia consecutivo, aumentar streak
        newStreak += 1;
        
        // Verificar recompensas de streak
        if (newStreak === 7) {
          streakReward = {
            type: 'streak',
            reason: 'Sequência de 7 dias',
            points: this.pointsConfig.WEEKLY_STREAK,
            streak: newStreak
          };
        } else if (newStreak === 30) {
          streakReward = {
            type: 'streak',
            reason: 'Sequência de 30 dias',
            points: this.pointsConfig.MONTHLY_STREAK,
            streak: newStreak
          };
        } else if (newStreak > 1) {
          streakReward = {
            type: 'streak',
            reason: `Sequência de ${newStreak} dias`,
            points: this.pointsConfig.DAILY_STREAK,
            streak: newStreak
          };
        }
      } else {
        // Quebrou a sequência
        newStreak = 1;
      }

      // Atualizar streak
      await database.getClient().gamificationProfile.update({
        where: { userPhone },
        data: {
          currentStreak: newStreak,
          maxStreak: Math.max(profile.maxStreak || 0, newStreak)
        }
      });

      return streakReward;
    } catch (error) {
      logger.error('Erro ao atualizar streak:', error);
      return null;
    }
  }

  // Obter estatísticas do usuário
  async getUserStats(userPhone) {
    try {
      const [profile, responses] = await Promise.all([
        database.getClient().gamificationProfile.findUnique({
          where: { userPhone }
        }),
        database.getClient().response.findMany({
          where: { userPhone },
          orderBy: { createdAt: 'asc' }
        })
      ]);

      if (!profile || responses.length === 0) {
        return {
          totalResponses: 0,
          promoterResponses: 0,
          feedbackCount: 0,
          averageScore: 0,
          maxStreak: 0,
          daysSinceFirstResponse: 0,
          perfectStreak: 0,
          improvementsGenerated: 0
        };
      }

      const promoterResponses = responses.filter(r => r.score >= 9).length;
      const feedbackCount = responses.filter(r => r.feedback && r.feedback.trim().length > 0).length;
      const averageScore = responses.reduce((sum, r) => sum + r.score, 0) / responses.length;
      const firstResponse = responses[0];
      const daysSinceFirstResponse = Math.floor((new Date() - firstResponse.createdAt) / (1000 * 60 * 60 * 24));

      // Calcular sequência perfeita (notas 10 consecutivas)
      let perfectStreak = 0;
      let currentPerfectStreak = 0;
      for (const response of responses.reverse()) {
        if (response.score === 10) {
          currentPerfectStreak++;
          perfectStreak = Math.max(perfectStreak, currentPerfectStreak);
        } else {
          currentPerfectStreak = 0;
        }
      }

      return {
        totalResponses: responses.length,
        promoterResponses,
        feedbackCount,
        averageScore: Math.round(averageScore * 100) / 100,
        maxStreak: profile.maxStreak || 0,
        daysSinceFirstResponse,
        perfectStreak,
        improvementsGenerated: profile.improvementsGenerated || 0
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas:', error);
      return {};
    }
  }

  // Obter badges do usuário
  async getUserBadges(userPhone) {
    try {
      return await database.getClient().userBadge.findMany({
        where: { userPhone },
        orderBy: { earnedAt: 'desc' }
      });
    } catch (error) {
      logger.error('Erro ao obter badges:', error);
      return [];
    }
  }

  // Obter conquistas do usuário
  async getUserAchievements(userPhone) {
    try {
      return await database.getClient().userAchievement.findMany({
        where: { userPhone },
        orderBy: { earnedAt: 'desc' }
      });
    } catch (error) {
      logger.error('Erro ao obter conquistas:', error);
      return [];
    }
  }

  // Notificar sobre recompensas
  async notifyRewards(userPhone, rewards) {
    try {
      let message = `🎉 Parabéns! Você ganhou:\n\n`;
      
      if (rewards.points > 0) {
        message += `💰 ${rewards.points} pontos\n`;
      }

      if (rewards.badges && rewards.badges.length > 0) {
        message += `\n🏆 Novos badges:\n`;
        rewards.badges.forEach(badge => {
          message += `${badge.icon} ${badge.name} - ${badge.description}\n`;
        });
      }

      if (rewards.achievements && rewards.achievements.length > 0) {
        message += `\n🎯 Novas conquistas:\n`;
        rewards.achievements.forEach(achievement => {
          message += `⭐ ${achievement.name} - Nível ${achievement.level}: ${achievement.title}\n`;
        });
      }

      if (rewards.streak) {
        message += `\n🔥 Sequência de ${rewards.streak.streak} dias mantida!\n`;
      }

      message += `\nContinue participando para ganhar mais recompensas! 🚀`;

      // Enviar via WhatsApp (integrar com serviço de WhatsApp)
      logger.info('Notificação de recompensa:', { userPhone, message });

      // Também criar notificação no sistema se o usuário estiver cadastrado
      const user = await database.getClient().user.findFirst({
        where: { 
          OR: [
            { email: { contains: userPhone } },
            { phone: userPhone }
          ]
        }
      });

      if (user) {
        await notificationService.createNotification({
          userId: user.id,
          type: 'gamification_reward',
          title: 'Recompensas Conquistadas! 🎉',
          message,
          data: rewards,
          priority: 'normal',
          channels: ['websocket', 'database']
        });
      }
    } catch (error) {
      logger.error('Erro ao notificar recompensas:', error);
    }
  }

  // Notificar sobre novo nível
  async notifyLevelUp(userPhone, newLevel) {
    try {
      const message = `🎊 LEVEL UP! 🎊\n\nVocê alcançou o nível ${newLevel}!\n\nContinue participando para desbloquear mais recompensas e badges especiais! 🌟`;

      logger.info('Level up:', { userPhone, newLevel });

      // Integrar com WhatsApp
      // await whatsappService.sendMessage(userPhone, message);
    } catch (error) {
      logger.error('Erro ao notificar level up:', error);
    }
  }

  // Obter ranking de usuários
  async getLeaderboard(limit = 10, period = 'all') {
    try {
      let whereClause = {};
      
      if (period !== 'all') {
        const now = new Date();
        let startDate;
        
        switch (period) {
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        
        whereClause.lastResponseAt = { gte: startDate };
      }

      const profiles = await database.getClient().gamificationProfile.findMany({
        where: whereClause,
        orderBy: [
          { totalPoints: 'desc' },
          { currentLevel: 'desc' },
          { totalResponses: 'desc' }
        ],
        take: limit
      });

      return profiles.map((profile, index) => ({
        rank: index + 1,
        userPhone: profile.userPhone,
        totalPoints: profile.totalPoints,
        currentLevel: profile.currentLevel,
        totalResponses: profile.totalResponses,
        currentStreak: profile.currentStreak,
        maxStreak: profile.maxStreak
      }));
    } catch (error) {
      logger.error('Erro ao obter leaderboard:', error);
      return [];
    }
  }

  // Obter perfil completo do usuário
  async getUserProfile(userPhone) {
    try {
      const [profile, badges, achievements, stats] = await Promise.all([
        database.getClient().gamificationProfile.findUnique({
          where: { userPhone }
        }),
        this.getUserBadges(userPhone),
        this.getUserAchievements(userPhone),
        this.getUserStats(userPhone)
      ]);

      if (!profile) {
        return null;
      }

      // Enriquecer badges com definições
      const enrichedBadges = badges.map(badge => ({
        ...badge,
        ...this.badgeDefinitions.get(badge.badgeId)
      }));

      // Enriquecer conquistas com definições
      const enrichedAchievements = achievements.map(achievement => {
        const definition = this.achievementRules.get(achievement.achievementId);
        const levelInfo = definition?.levels.find(l => l.level === achievement.level);
        
        return {
          ...achievement,
          name: definition?.name,
          title: levelInfo?.title,
          points: levelInfo?.points
        };
      });

      return {
        profile,
        badges: enrichedBadges,
        achievements: enrichedAchievements,
        stats,
        nextLevelPoints: this.getPointsForNextLevel(profile.currentLevel),
        progressToNextLevel: this.getProgressToNextLevel(profile.totalPoints, profile.currentLevel)
      };
    } catch (error) {
      logger.error('Erro ao obter perfil do usuário:', error);
      return null;
    }
  }

  // Calcular pontos necessários para próximo nível
  getPointsForNextLevel(currentLevel) {
    return Math.pow(currentLevel, 2) * 100;
  }

  // Calcular progresso para próximo nível
  getProgressToNextLevel(totalPoints, currentLevel) {
    const currentLevelPoints = Math.pow(currentLevel - 1, 2) * 100;
    const nextLevelPoints = this.getPointsForNextLevel(currentLevel);
    const pointsInCurrentLevel = totalPoints - currentLevelPoints;
    const pointsNeededForNext = nextLevelPoints - currentLevelPoints;
    
    return Math.min(100, Math.round((pointsInCurrentLevel / pointsNeededForNext) * 100));
  }

  // Obter estatísticas gerais do sistema
  async getSystemStats() {
    try {
      const [
        totalUsers,
        totalPoints,
        totalBadges,
        totalAchievements,
        topUser
      ] = await Promise.all([
        database.getClient().gamificationProfile.count(),
        database.getClient().gamificationProfile.aggregate({
          _sum: { totalPoints: true }
        }),
        database.getClient().userBadge.count(),
        database.getClient().userAchievement.count(),
        database.getClient().gamificationProfile.findFirst({
          orderBy: { totalPoints: 'desc' }
        })
      ]);

      return {
        totalUsers,
        totalPoints: totalPoints._sum.totalPoints || 0,
        totalBadges,
        totalAchievements,
        averagePointsPerUser: totalUsers > 0 ? Math.round((totalPoints._sum.totalPoints || 0) / totalUsers) : 0,
        topUser: topUser ? {
          userPhone: topUser.userPhone,
          totalPoints: topUser.totalPoints,
          currentLevel: topUser.currentLevel
        } : null
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas do sistema:', error);
      return {};
    }
  }
}

module.exports = new GamificationService();
