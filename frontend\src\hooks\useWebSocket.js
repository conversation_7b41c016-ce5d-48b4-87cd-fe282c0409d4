import { useEffect, useRef, useState, useCallback } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

export function useWebSocket() {
  const { token, isAuthenticated } = useAuth();
  const socketRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState(null);

  // Conectar ao WebSocket
  const connect = useCallback(() => {
    if (!isAuthenticated || !token) {
      return;
    }

    try {
      const socket = io(process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:3000', {
        auth: {
          token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000
      });

      // Eventos de conexão
      socket.on('connect', () => {
        console.log('✅ WebSocket conectado');
        setIsConnected(true);
        setConnectionError(null);
      });

      socket.on('disconnect', (reason) => {
        console.log('❌ WebSocket desconectado:', reason);
        setIsConnected(false);
        
        if (reason === 'io server disconnect') {
          // Reconectar se o servidor desconectou
          socket.connect();
        }
      });

      socket.on('connect_error', (error) => {
        console.error('❌ Erro de conexão WebSocket:', error);
        setConnectionError(error.message);
        setIsConnected(false);
      });

      // Eventos de notificação
      socket.on('notification', (notification) => {
        handleNotification(notification);
      });

      socket.on('new-response', (notification) => {
        handleNewResponse(notification);
      });

      socket.on('detractor-alert', (notification) => {
        handleDetractorAlert(notification);
      });

      socket.on('new-message', (data) => {
        handleNewMessage(data);
      });

      socket.on('typing-status', (data) => {
        handleTypingStatus(data);
      });

      socket.on('message-read', (data) => {
        handleMessageRead(data);
      });

      socket.on('user-joined-conversation', (data) => {
        console.log('👤 Usuário entrou na conversa:', data);
      });

      socket.on('user-left-conversation', (data) => {
        console.log('👤 Usuário saiu da conversa:', data);
      });

      socketRef.current = socket;
    } catch (error) {
      console.error('Erro ao conectar WebSocket:', error);
      setConnectionError(error.message);
    }
  }, [token, isAuthenticated]);

  // Desconectar
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  }, []);

  // Handlers de eventos
  const handleNotification = (notification) => {
    console.log('📢 Nova notificação:', notification);
    
    // Mostrar toast baseado na prioridade
    const toastOptions = {
      duration: notification.priority === 'urgent' ? 8000 : 4000,
      position: 'top-right'
    };

    switch (notification.priority) {
      case 'urgent':
        toast.error(notification.message, {
          ...toastOptions,
          icon: '🚨'
        });
        break;
      case 'high':
        toast.error(notification.message, toastOptions);
        break;
      case 'normal':
        toast.success(notification.message, toastOptions);
        break;
      case 'low':
        toast(notification.message, toastOptions);
        break;
      default:
        toast(notification.message, toastOptions);
    }

    // Reproduzir som se permitido
    if (notification.priority === 'urgent' || notification.priority === 'high') {
      playNotificationSound();
    }

    // Atualizar badge de notificações
    updateNotificationBadge();
  };

  const handleNewResponse = (notification) => {
    console.log('📊 Nova resposta:', notification);
    
    toast.success(notification.title, {
      duration: 5000,
      icon: '📊'
    });

    // Disparar evento customizado para atualizar componentes
    window.dispatchEvent(new CustomEvent('newResponse', {
      detail: notification
    }));
  };

  const handleDetractorAlert = (notification) => {
    console.log('🚨 Alerta de detrator:', notification);
    
    toast.error(notification.title, {
      duration: 10000,
      icon: '🚨',
      style: {
        background: '#fee2e2',
        border: '1px solid #fecaca',
        color: '#991b1b'
      }
    });

    // Som de alerta
    playUrgentSound();

    // Disparar evento customizado
    window.dispatchEvent(new CustomEvent('detractorAlert', {
      detail: notification
    }));
  };

  const handleNewMessage = (data) => {
    console.log('💬 Nova mensagem:', data);
    
    const { notification, sender } = data;
    
    toast(notification.message, {
      duration: 4000,
      icon: '💬'
    });

    // Disparar evento customizado
    window.dispatchEvent(new CustomEvent('newMessage', {
      detail: data
    }));
  };

  const handleTypingStatus = (data) => {
    console.log('⌨️ Status de digitação:', data);
    
    // Disparar evento customizado para componentes de conversa
    window.dispatchEvent(new CustomEvent('typingStatus', {
      detail: data
    }));
  };

  const handleMessageRead = (data) => {
    console.log('👁️ Mensagem lida:', data);
    
    // Disparar evento customizado
    window.dispatchEvent(new CustomEvent('messageRead', {
      detail: data
    }));
  };

  // Funções utilitárias
  const playNotificationSound = () => {
    try {
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = 0.5;
      audio.play().catch(console.warn);
    } catch (error) {
      console.warn('Não foi possível reproduzir som de notificação:', error);
    }
  };

  const playUrgentSound = () => {
    try {
      const audio = new Audio('/sounds/urgent.mp3');
      audio.volume = 0.7;
      audio.play().catch(console.warn);
    } catch (error) {
      console.warn('Não foi possível reproduzir som urgente:', error);
    }
  };

  const updateNotificationBadge = () => {
    // Atualizar badge no título da página
    document.title = document.title.includes('(') 
      ? document.title.replace(/\(\d+\)/, '(•)')
      : `(•) ${document.title}`;

    // Remover badge após 3 segundos
    setTimeout(() => {
      document.title = document.title.replace(/\(•\)\s*/, '');
    }, 3000);
  };

  // Métodos públicos do socket
  const joinConversation = useCallback((conversationId) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('join-conversation', conversationId);
    }
  }, [isConnected]);

  const leaveConversation = useCallback((conversationId) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('leave-conversation', conversationId);
    }
  }, [isConnected]);

  const markAsRead = useCallback((data) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('mark-as-read', data);
    }
  }, [isConnected]);

  const startTyping = useCallback((data) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('typing-start', data);
    }
  }, [isConnected]);

  const stopTyping = useCallback((data) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('typing-stop', data);
    }
  }, [isConnected]);

  // Efeitos
  useEffect(() => {
    if (isAuthenticated && token) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated, token, connect, disconnect]);

  // Cleanup ao desmontar
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionError,
    connect,
    disconnect,
    joinConversation,
    leaveConversation,
    markAsRead,
    startTyping,
    stopTyping,
    socket: socketRef.current
  };
}
