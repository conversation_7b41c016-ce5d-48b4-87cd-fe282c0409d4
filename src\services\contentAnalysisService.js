const database = require('../config/database');
const { logger } = require('../utils/logger');

class ContentAnalysisService {
  constructor() {
    this.departmentCache = new Map();
    this.lastCacheUpdate = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutos
  }

  // Carregar departamentos e suas palavras-chave
  async loadDepartments() {
    const now = Date.now();
    
    // Verificar se o cache ainda é válido
    if (this.lastCacheUpdate && (now - this.lastCacheUpdate) < this.cacheTimeout) {
      return;
    }

    try {
      const departments = await database.getClient().department.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          keywords: true,
          description: true
        }
      });

      // Limpar cache anterior
      this.departmentCache.clear();

      // Carregar departamentos no cache
      departments.forEach(dept => {
        this.departmentCache.set(dept.id, {
          name: dept.name,
          keywords: dept.keywords.map(keyword => keyword.toLowerCase()),
          description: dept.description
        });
      });

      this.lastCacheUpdate = now;
      logger.debug('Departamentos carregados no cache:', { count: departments.length });

    } catch (error) {
      logger.error('Erro ao carregar departamentos:', error);
    }
  }

  // Analisar texto e identificar departamento
  async analyzeContent(text) {
    if (!text || typeof text !== 'string') {
      return {
        department: null,
        confidence: 0,
        matchedKeywords: [],
        analysis: 'Texto vazio ou inválido'
      };
    }

    // Carregar departamentos se necessário
    await this.loadDepartments();

    const normalizedText = this.normalizeText(text);
    const words = normalizedText.split(/\s+/);
    
    const departmentScores = new Map();
    const matchedKeywords = new Map();

    // Analisar cada departamento
    for (const [deptId, dept] of this.departmentCache) {
      let score = 0;
      const matches = [];

      // Verificar cada palavra-chave do departamento
      for (const keyword of dept.keywords) {
        const keywordScore = this.calculateKeywordScore(normalizedText, words, keyword);
        
        if (keywordScore > 0) {
          score += keywordScore;
          matches.push({
            keyword,
            score: keywordScore,
            type: this.getMatchType(normalizedText, keyword)
          });
        }
      }

      if (score > 0) {
        departmentScores.set(deptId, score);
        matchedKeywords.set(deptId, matches);
      }
    }

    // Encontrar o departamento com maior score
    let bestDepartment = null;
    let bestScore = 0;
    let bestMatches = [];

    for (const [deptId, score] of departmentScores) {
      if (score > bestScore) {
        bestScore = score;
        bestDepartment = this.departmentCache.get(deptId);
        bestMatches = matchedKeywords.get(deptId) || [];
      }
    }

    // Calcular confiança (0-100)
    const confidence = this.calculateConfidence(bestScore, normalizedText.length);

    return {
      department: bestDepartment ? bestDepartment.name : null,
      departmentId: bestDepartment ? Array.from(this.departmentCache.entries())
        .find(([id, dept]) => dept === bestDepartment)?.[0] : null,
      confidence,
      matchedKeywords: bestMatches,
      analysis: this.generateAnalysisText(bestDepartment, bestMatches, confidence),
      scores: Object.fromEntries(
        Array.from(departmentScores.entries()).map(([id, score]) => [
          this.departmentCache.get(id).name,
          score
        ])
      )
    };
  }

  // Normalizar texto para análise
  normalizeText(text) {
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^\w\s]/g, ' ') // Remove pontuação
      .replace(/\s+/g, ' ') // Normaliza espaços
      .trim();
  }

  // Calcular score de uma palavra-chave
  calculateKeywordScore(text, words, keyword) {
    let score = 0;
    const keywordWords = keyword.split(/\s+/);

    // Busca exata da frase completa
    if (text.includes(keyword)) {
      score += keywordWords.length * 10; // Score alto para match exato
    }

    // Busca por palavras individuais
    for (const keywordWord of keywordWords) {
      const wordCount = words.filter(word => word === keywordWord).length;
      score += wordCount * 5; // Score médio para palavras individuais
    }

    // Busca por palavras similares (substring)
    for (const keywordWord of keywordWords) {
      if (keywordWord.length > 3) { // Apenas para palavras com mais de 3 caracteres
        const similarWords = words.filter(word => 
          word.includes(keywordWord) || keywordWord.includes(word)
        );
        score += similarWords.length * 2; // Score baixo para matches similares
      }
    }

    return score;
  }

  // Determinar tipo de match
  getMatchType(text, keyword) {
    if (text.includes(keyword)) {
      return 'exact';
    }
    
    const keywordWords = keyword.split(/\s+/);
    const textWords = text.split(/\s+/);
    
    for (const keywordWord of keywordWords) {
      if (textWords.includes(keywordWord)) {
        return 'partial';
      }
    }
    
    return 'similar';
  }

  // Calcular confiança baseada no score e tamanho do texto
  calculateConfidence(score, textLength) {
    if (score === 0) return 0;
    
    // Normalizar score baseado no tamanho do texto
    const normalizedScore = (score / Math.max(textLength / 10, 1)) * 100;
    
    // Limitar entre 0 e 100
    return Math.min(Math.max(Math.round(normalizedScore), 0), 100);
  }

  // Gerar texto de análise
  generateAnalysisText(department, matches, confidence) {
    if (!department) {
      return 'Nenhum departamento específico identificado. Direcionando para atendimento geral.';
    }

    const keywordList = matches.map(m => m.keyword).join(', ');
    
    return `Identificado departamento "${department.name}" com ${confidence}% de confiança. ` +
           `Palavras-chave encontradas: ${keywordList}.`;
  }

  // Analisar sentimento básico (positivo, neutro, negativo)
  analyzeSentiment(text) {
    const normalizedText = this.normalizeText(text);
    
    const positiveWords = [
      'bom', 'boa', 'excelente', 'otimo', 'perfeito', 'maravilhoso',
      'satisfeito', 'feliz', 'contente', 'agradeco', 'obrigado',
      'parabens', 'recomendo', 'gostei', 'adorei', 'amei'
    ];
    
    const negativeWords = [
      'ruim', 'pessimo', 'horrivel', 'terrivel', 'insatisfeito',
      'problema', 'erro', 'falha', 'demora', 'lento', 'nao funciona',
      'reclamacao', 'insatisfacao', 'raiva', 'irritado', 'chateado',
      'cancelar', 'desistir', 'nao recomendo', 'odiei', 'detestei'
    ];

    let positiveScore = 0;
    let negativeScore = 0;

    const words = normalizedText.split(/\s+/);

    words.forEach(word => {
      if (positiveWords.includes(word)) {
        positiveScore++;
      } else if (negativeWords.includes(word)) {
        negativeScore++;
      }
    });

    // Determinar sentimento
    let sentiment = 'neutral';
    let confidence = 0;

    if (positiveScore > negativeScore) {
      sentiment = 'positive';
      confidence = Math.min((positiveScore / words.length) * 100, 100);
    } else if (negativeScore > positiveScore) {
      sentiment = 'negative';
      confidence = Math.min((negativeScore / words.length) * 100, 100);
    }

    return {
      sentiment,
      confidence: Math.round(confidence),
      scores: {
        positive: positiveScore,
        negative: negativeScore,
        neutral: words.length - positiveScore - negativeScore
      }
    };
  }

  // Extrair informações específicas do texto
  extractInformation(text) {
    const normalizedText = this.normalizeText(text);
    const info = {};

    // Extrair números de telefone
    const phoneRegex = /(\(?\d{2}\)?\s?\d{4,5}-?\d{4})/g;
    const phones = text.match(phoneRegex);
    if (phones) {
      info.phones = phones;
    }

    // Extrair emails
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
    const emails = text.match(emailRegex);
    if (emails) {
      info.emails = emails;
    }

    // Extrair números de protocolo/pedido
    const protocolRegex = /(?:protocolo|pedido|numero|n[°º]?)\s*:?\s*(\d+)/gi;
    const protocols = [];
    let match;
    while ((match = protocolRegex.exec(text)) !== null) {
      protocols.push(match[1]);
    }
    if (protocols.length > 0) {
      info.protocols = protocols;
    }

    // Identificar urgência
    const urgentWords = ['urgente', 'emergencia', 'rapido', 'imediato', 'agora'];
    const hasUrgentWords = urgentWords.some(word => normalizedText.includes(word));
    if (hasUrgentWords) {
      info.priority = 'high';
    }

    return info;
  }

  // Analisar texto completo (departamento + sentimento + informações)
  async analyzeFullContent(text) {
    const [departmentAnalysis, sentimentAnalysis] = await Promise.all([
      this.analyzeContent(text),
      Promise.resolve(this.analyzeSentiment(text))
    ]);

    const extractedInfo = this.extractInformation(text);

    return {
      text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
      department: departmentAnalysis,
      sentiment: sentimentAnalysis,
      extractedInfo,
      timestamp: new Date().toISOString(),
      summary: this.generateSummary(departmentAnalysis, sentimentAnalysis, extractedInfo)
    };
  }

  // Gerar resumo da análise
  generateSummary(departmentAnalysis, sentimentAnalysis, extractedInfo) {
    let summary = '';

    // Departamento
    if (departmentAnalysis.department) {
      summary += `Direcionado para ${departmentAnalysis.department}. `;
    } else {
      summary += 'Atendimento geral. ';
    }

    // Sentimento
    const sentimentText = {
      positive: 'Cliente satisfeito',
      negative: 'Cliente insatisfeito',
      neutral: 'Sentimento neutro'
    };
    summary += `${sentimentText[sentimentAnalysis.sentiment]}. `;

    // Prioridade
    if (extractedInfo.priority === 'high') {
      summary += 'URGENTE. ';
    }

    // Informações extras
    if (extractedInfo.protocols) {
      summary += `Protocolos mencionados: ${extractedInfo.protocols.join(', ')}. `;
    }

    return summary.trim();
  }
}

module.exports = new ContentAnalysisService();
