# Docker Compose override para desenvolvimento
# Este arquivo é carregado automaticamente junto com docker-compose.yml

# Docker Compose Override para desenvolvimento

services:
  # Backend em modo desenvolvimento
  backend:
    build:
      target: development
    environment:
      - NODE_ENV=development
      - DEBUG=uniforms:*
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    command: npm run dev
    ports:
      - "9229:9229"  # Debug port
    
  # Frontend em modo desenvolvimento
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    environment:
      - CHOKIDAR_USEPOLLING=true
      - REACT_APP_API_URL=http://localhost:3000/api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3001:3000"
    command: npm start

  # PostgreSQL com configurações de desenvolvimento
  postgres:
    environment:
      - POSTGRES_DB=uniforms_survey_dev
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/postgres/init-dev.sql:/docker-entrypoint-initdb.d/init-dev.sql

  # Redis com configurações de desenvolvimento
  redis:
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

  # Evolution API com logs detalhados
  evolution-api:
    environment:
      - LOG_LEVEL=debug
      - WEBHOOK_GLOBAL_URL=http://backend:3000/webhooks/whatsapp
    ports:
      - "8080:8080"

  # n8n com configurações de desenvolvimento
  n8n:
    environment:
      - N8N_LOG_LEVEL=debug
      - N8N_BASIC_AUTH_ACTIVE=false
    ports:
      - "5678:5678"
    volumes:
      - n8n_dev_data:/home/<USER>/.n8n

  # Adminer para gerenciar banco de dados
  adminer:
    image: adminer:latest
    container_name: uniforms-adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    networks:
      - uniforms-network
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
      - ADMINER_DESIGN=pepa-linha

  # Redis Commander para gerenciar Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: uniforms-redis-commander
    restart: unless-stopped
    ports:
      - "8082:8081"
    networks:
      - uniforms-network
    environment:
      - REDIS_HOSTS=local:redis:6379

  # Mailhog para testar emails em desenvolvimento
  mailhog:
    image: mailhog/mailhog:latest
    container_name: uniforms-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - uniforms-network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  n8n_dev_data:
    driver: local
