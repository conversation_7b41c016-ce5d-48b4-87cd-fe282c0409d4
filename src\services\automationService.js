const database = require("../config/database");
const { logger } = require("../utils/logger");
const notificationService = require("./notificationService");
const chatbotService = require("./chatbotService");
const cron = require("node-cron");

class AutomationService {
  constructor() {
    this.rules = new Map();
    this.scheduledTasks = new Map();
    this.templates = new Map();
    this.isInitialized = false;

    this.initializeAutomation();
  }

  // Inicializar automação
  async initializeAutomation() {
    try {
      await this.loadAutomationRules();
      await this.loadResponseTemplates();
      this.setupScheduledTasks();

      this.isInitialized = true;
      logger.info("✅ Serviço de automação inicializado");
    } catch (error) {
      logger.error("Erro ao inicializar automação:", error);
    }
  }

  // Carregar regras de automação
  async loadAutomationRules() {
    // Regra 1: Follow-up automático para detratores
    this.rules.set("detractor_followup", {
      name: "Follow-up Detratores",
      trigger: "response_received",
      conditions: [
        { field: "score", operator: "<=", value: 6 },
        { field: "isDetractor", operator: "==", value: true },
      ],
      actions: [
        {
          type: "schedule_followup",
          delay: "2h",
          template: "detractor_followup",
          priority: "HIGH",
        },
        {
          type: "notify_team",
          team: "SUPPORT",
          message: "Detrator identificado - ação imediata necessária",
        },
      ],
      isActive: true,
    });

    // Regra 2: Agendamento de pesquisa de follow-up
    this.rules.set("followup_survey", {
      name: "Pesquisa de Follow-up",
      trigger: "conversation_resolved",
      conditions: [
        { field: "status", operator: "==", value: "RESOLVED" },
        { field: "priority", operator: "!=", value: "LOW" },
      ],
      actions: [
        {
          type: "schedule_survey",
          delay: "24h",
          template: "followup_survey",
        },
      ],
      isActive: true,
    });

    // Regra 3: Escalação por tempo de resposta
    this.rules.set("response_time_escalation", {
      name: "Escalação por Tempo",
      trigger: "message_received",
      conditions: [
        { field: "responseTime", operator: ">", value: 3600 }, // 1 hora
        { field: "status", operator: "==", value: "OPEN" },
      ],
      actions: [
        {
          type: "escalate",
          to: "SUPERVISOR",
          priority: "HIGH",
        },
        {
          type: "send_message",
          template: "escalation_notice",
        },
      ],
      isActive: true,
    });

    // Regra 4: Resposta automática fora do horário
    this.rules.set("after_hours_response", {
      name: "Resposta Fora do Horário",
      trigger: "message_received",
      conditions: [
        { field: "time", operator: "outside_business_hours", value: true },
      ],
      actions: [
        {
          type: "send_message",
          template: "after_hours_auto_reply",
          delay: "1m",
        },
      ],
      isActive: true,
    });

    // Regra 5: Promotores - oportunidade de referência
    this.rules.set("promoter_referral", {
      name: "Oportunidade de Referência",
      trigger: "response_received",
      conditions: [
        { field: "score", operator: ">=", value: 9 },
        { field: "isPromoter", operator: "==", value: true },
      ],
      actions: [
        {
          type: "schedule_followup",
          delay: "1h",
          template: "promoter_referral",
          priority: "NORMAL",
        },
      ],
      isActive: true,
    });

    logger.info(`${this.rules.size} regras de automação carregadas`);
  }

  // Carregar templates de resposta
  async loadResponseTemplates() {
    this.templates.set("detractor_followup", {
      subject: "Queremos melhorar sua experiência",
      message: `Olá! 😊

Notamos que sua experiência conosco não foi a melhor possível e queremos muito melhorar isso!

Sua opinião é extremamente importante para nós. Poderia nos contar um pouco mais sobre o que aconteceu?

Nossa equipe está pronta para resolver qualquer problema e garantir que você tenha a melhor experiência possível.

Aguardamos seu retorno! 🙏`,
      type: "whatsapp",
    });

    this.templates.set("followup_survey", {
      subject: "Como foi sua experiência?",
      message: `Olá! 👋

Esperamos que seu problema tenha sido resolvido com sucesso!

Gostaríamos muito de saber como foi sua experiência conosco. Sua opinião nos ajuda a melhorar cada vez mais.

Poderia avaliar nosso atendimento? Leva apenas 1 minuto:
{{survey_link}}

Muito obrigado! ✨`,
      type: "whatsapp",
    });

    this.templates.set("escalation_notice", {
      subject: "Sua mensagem foi priorizada",
      message: `Olá! 🚨

Notamos que você está aguardando uma resposta há algum tempo.

Sua mensagem foi priorizada e nossa equipe especializada irá entrar em contato em breve.

Pedimos desculpas pela demora e agradecemos sua paciência! 🙏`,
      type: "whatsapp",
    });

    this.templates.set("after_hours_auto_reply", {
      subject: "Mensagem recebida - Responderemos em breve",
      message: `Olá! 🌙

Recebemos sua mensagem, mas no momento estamos fora do horário de atendimento.

📅 Nosso horário de funcionamento:
• Segunda a Sexta: 8h às 18h
• Sábado: 8h às 12h
• Domingo: Fechado

Responderemos sua mensagem assim que possível no próximo horário comercial.

Obrigado pela compreensão! 😊`,
      type: "whatsapp",
    });

    this.templates.set("promoter_referral", {
      subject: "Obrigado pela avaliação! Que tal indicar um amigo?",
      message: `Muito obrigado pela excelente avaliação! ⭐⭐⭐⭐⭐

Ficamos muito felizes em saber que você está satisfeito com nossos serviços!

Que tal indicar um amigo? Para cada indicação, você e seu amigo ganham benefícios especiais! 🎁

Interessado? Responda esta mensagem que explicamos como funciona!

Obrigado por ser um cliente especial! 💙`,
      type: "whatsapp",
    });

    logger.info(`${this.templates.size} templates carregados`);
  }

  // Configurar tarefas agendadas
  setupScheduledTasks() {
    // Verificar follow-ups pendentes a cada 5 minutos
    cron.schedule("*/5 * * * *", () => {
      this.processScheduledFollowups();
    });

    // Verificar escalações por tempo a cada 10 minutos
    cron.schedule("*/10 * * * *", () => {
      this.checkResponseTimeEscalations();
    });

    // Limpeza de tarefas antigas - diariamente às 2h
    cron.schedule("0 2 * * *", () => {
      this.cleanupOldTasks();
    });

    // Relatório de automação - semanalmente
    cron.schedule("0 9 * * 1", () => {
      this.generateAutomationReport();
    });

    logger.info("Tarefas agendadas configuradas");
  }

  // Processar evento (trigger principal)
  async processEvent(eventType, eventData) {
    try {
      logger.debug("Processando evento de automação:", {
        eventType,
        eventData,
      });

      // Buscar regras que correspondem ao evento
      const matchingRules = Array.from(this.rules.values()).filter(
        (rule) => rule.trigger === eventType && rule.isActive
      );

      for (const rule of matchingRules) {
        if (await this.evaluateConditions(rule.conditions, eventData)) {
          await this.executeActions(rule.actions, eventData, rule.name);
        }
      }
    } catch (error) {
      logger.error("Erro ao processar evento de automação:", error);
    }
  }

  // Avaliar condições
  async evaluateConditions(conditions, eventData) {
    for (const condition of conditions) {
      if (!this.evaluateCondition(condition, eventData)) {
        return false;
      }
    }
    return true;
  }

  // Avaliar condição individual
  evaluateCondition(condition, eventData) {
    const { field, operator, value } = condition;
    const fieldValue = this.getFieldValue(field, eventData);

    switch (operator) {
      case "==":
        return fieldValue === value;
      case "!=":
        return fieldValue !== value;
      case ">":
        return fieldValue > value;
      case "<":
        return fieldValue < value;
      case ">=":
        return fieldValue >= value;
      case "<=":
        return fieldValue <= value;
      case "contains":
        return String(fieldValue)
          .toLowerCase()
          .includes(String(value).toLowerCase());
      case "outside_business_hours":
        return this.isOutsideBusinessHours();
      default:
        return false;
    }
  }

  // Obter valor do campo
  getFieldValue(field, eventData) {
    const fields = field.split(".");
    let value = eventData;

    for (const f of fields) {
      value = value?.[f];
    }

    return value;
  }

  // Verificar se está fora do horário comercial
  isOutsideBusinessHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = domingo, 6 = sábado

    // Domingo
    if (day === 0) return true;

    // Sábado - apenas manhã
    if (day === 6) {
      return hour < 8 || hour >= 12;
    }

    // Segunda a sexta
    return hour < 8 || hour >= 18;
  }

  // Executar ações
  async executeActions(actions, eventData, ruleName) {
    for (const action of actions) {
      try {
        await this.executeAction(action, eventData, ruleName);
      } catch (error) {
        logger.error(`Erro ao executar ação ${action.type}:`, error);
      }
    }
  }

  // Executar ação individual
  async executeAction(action, eventData, ruleName) {
    const { type, delay, template, priority, to, team, message } = action;

    switch (type) {
      case "schedule_followup":
        await this.scheduleFollowup(eventData, template, delay, priority);
        break;

      case "schedule_survey":
        await this.scheduleSurvey(eventData, template, delay);
        break;

      case "send_message":
        if (delay) {
          await this.scheduleMessage(eventData, template, delay);
        } else {
          await this.sendMessage(eventData, template);
        }
        break;

      case "escalate":
        await this.escalateConversation(eventData, to, priority);
        break;

      case "notify_team":
        await this.notifyTeam(team, message, eventData);
        break;

      default:
        logger.warn(`Ação desconhecida: ${type}`);
    }

    // Log da ação executada
    await this.logAction(ruleName, type, eventData);
  }

  // Agendar follow-up
  async scheduleFollowup(eventData, templateName, delay, priority = "NORMAL") {
    const executeAt = this.calculateExecutionTime(delay);
    const taskId = `followup_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    const task = {
      id: taskId,
      type: "followup",
      templateName,
      eventData,
      priority,
      executeAt,
      status: "scheduled",
      createdAt: new Date(),
    };

    this.scheduledTasks.set(taskId, task);

    // Salvar no banco
    await database.getClient().scheduledTask.create({
      data: {
        id: taskId,
        type: "followup",
        data: eventData,
        templateName,
        priority,
        executeAt,
        status: "scheduled",
      },
    });

    logger.info("Follow-up agendado:", { taskId, executeAt, templateName });
  }

  // Agendar pesquisa
  async scheduleSurvey(eventData, templateName, delay) {
    const executeAt = this.calculateExecutionTime(delay);
    const taskId = `survey_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    const task = {
      id: taskId,
      type: "survey",
      templateName,
      eventData,
      executeAt,
      status: "scheduled",
      createdAt: new Date(),
    };

    this.scheduledTasks.set(taskId, task);

    await database.getClient().scheduledTask.create({
      data: {
        id: taskId,
        type: "survey",
        data: eventData,
        templateName,
        executeAt,
        status: "scheduled",
      },
    });

    logger.info("Pesquisa agendada:", { taskId, executeAt });
  }

  // Agendar mensagem
  async scheduleMessage(eventData, templateName, delay) {
    const executeAt = this.calculateExecutionTime(delay);
    const taskId = `message_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    const task = {
      id: taskId,
      type: "message",
      templateName,
      eventData,
      executeAt,
      status: "scheduled",
      createdAt: new Date(),
    };

    this.scheduledTasks.set(taskId, task);

    await database.getClient().scheduledTask.create({
      data: {
        id: taskId,
        type: "message",
        data: eventData,
        templateName,
        executeAt,
        status: "scheduled",
      },
    });

    logger.info("Mensagem agendada:", { taskId, executeAt });
  }

  // Enviar mensagem imediatamente
  async sendMessage(eventData, templateName) {
    const template = this.templates.get(templateName);
    if (!template) {
      logger.warn(`Template não encontrado: ${templateName}`);
      return;
    }

    const message = this.processTemplate(template.message, eventData);

    // Integrar com WhatsApp API
    // Por enquanto, apenas log
    logger.info("Enviando mensagem automática:", {
      to: eventData.userPhone,
      template: templateName,
      message: message.substring(0, 100) + "...",
    });

    // Aqui integraria com o serviço de WhatsApp
    // await whatsappService.sendMessage(eventData.userPhone, message);
  }

  // Escalar conversa
  async escalateConversation(eventData, to, priority) {
    if (!eventData.conversationId) return;

    await database.getClient().conversation.update({
      where: { id: eventData.conversationId },
      data: {
        department: to,
        priority,
      },
    });

    logger.info("Conversa escalada automaticamente:", {
      conversationId: eventData.conversationId,
      to,
      priority,
    });
  }

  // Notificar equipe
  async notifyTeam(team, message, eventData) {
    await notificationService.createNotification({
      type: "automation_alert",
      title: "Alerta de Automação",
      message,
      data: eventData,
      priority: "high",
      channels: ["websocket", "database"],
    });

    logger.info("Equipe notificada:", { team, message });
  }

  // Calcular tempo de execução
  calculateExecutionTime(delay) {
    const now = new Date();
    const delayMs = this.parseDelay(delay);
    return new Date(now.getTime() + delayMs);
  }

  // Parsear delay (ex: "2h", "30m", "1d")
  parseDelay(delay) {
    const match = delay.match(/^(\d+)([smhd])$/);
    if (!match) return 0;

    const [, amount, unit] = match;
    const multipliers = {
      s: 1000,
      m: 60 * 1000,
      h: 60 * 60 * 1000,
      d: 24 * 60 * 60 * 1000,
    };

    return parseInt(amount) * multipliers[unit];
  }

  // Processar template
  processTemplate(template, eventData) {
    let processed = template;

    // Substituir variáveis
    processed = processed.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return eventData[key] || match;
    });

    // Adicionar link de pesquisa se necessário
    if (processed.includes("{{survey_link}}")) {
      const surveyLink = `${
        process.env.FRONTEND_URL || "http://localhost:3001"
      }/survey/latest?phone=${encodeURIComponent(eventData.userPhone)}`;
      processed = processed.replace("{{survey_link}}", surveyLink);
    }

    return processed;
  }

  // Processar follow-ups agendados
  async processScheduledFollowups() {
    const now = new Date();

    // Buscar tarefas pendentes
    const pendingTasks = await database.getClient().scheduledTask.findMany({
      where: {
        status: "scheduled",
        executeAt: { lte: now },
      },
    });

    for (const task of pendingTasks) {
      try {
        await this.executeScheduledTask(task);

        // Marcar como executada
        await database.getClient().scheduledTask.update({
          where: { id: task.id },
          data: {
            status: "executed",
            executedAt: new Date(),
          },
        });

        this.scheduledTasks.delete(task.id);
      } catch (error) {
        logger.error(`Erro ao executar tarefa ${task.id}:`, error);

        // Marcar como erro
        await database.getClient().scheduledTask.update({
          where: { id: task.id },
          data: {
            status: "error",
            error: error.message,
          },
        });
      }
    }
  }

  // Executar tarefa agendada
  async executeScheduledTask(task) {
    const { type, templateName, data } = task;

    switch (type) {
      case "followup":
      case "message":
        await this.sendMessage(data, templateName);
        break;

      case "survey":
        await this.sendSurveyInvitation(data, templateName);
        break;

      default:
        logger.warn(`Tipo de tarefa desconhecido: ${type}`);
    }

    logger.info("Tarefa executada:", { taskId: task.id, type });
  }

  // Enviar convite de pesquisa
  async sendSurveyInvitation(eventData, templateName) {
    // Buscar pesquisa ativa
    const activeSurvey = await database.getClient().survey.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: "desc" },
    });

    if (activeSurvey) {
      const surveyData = {
        ...eventData,
        survey_link: `${
          process.env.FRONTEND_URL || "http://localhost:3001"
        }/survey/${activeSurvey.id}?phone=${encodeURIComponent(
          eventData.userPhone
        )}`,
      };

      await this.sendMessage(surveyData, templateName);
    }
  }

  // Verificar escalações por tempo
  async checkResponseTimeEscalations() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    const overdueConversations = await database
      .getClient()
      .conversation.findMany({
        where: {
          status: "OPEN",
          createdAt: { lte: oneHourAgo },
        },
      });

    for (const conversation of overdueConversations) {
      await this.processEvent("message_received", {
        conversationId: conversation.id,
        userPhone: conversation.userPhone,
        responseTime: Date.now() - new Date(conversation.createdAt).getTime(),
        status: conversation.status,
      });
    }
  }

  // Limpeza de tarefas antigas
  async cleanupOldTasks() {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const result = await database.getClient().scheduledTask.deleteMany({
      where: {
        createdAt: { lte: thirtyDaysAgo },
        status: { in: ["executed", "error"] },
      },
    });

    logger.info(`${result.count} tarefas antigas removidas`);
  }

  // Gerar relatório de automação
  async generateAutomationReport() {
    const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const stats = await database.getClient().scheduledTask.groupBy({
      by: ["type", "status"],
      where: {
        createdAt: { gte: lastWeek },
      },
      _count: { id: true },
    });

    logger.info("Relatório semanal de automação:", stats);
  }

  // Log de ação
  async logAction(ruleName, actionType, eventData) {
    try {
      await database.getClient().automationLog.create({
        data: {
          ruleName,
          actionType,
          eventData,
          executedAt: new Date(),
        },
      });
    } catch (error) {
      logger.warn("Erro ao registrar log de automação:", error);
    }
  }

  // Obter estatísticas
  getStats() {
    return {
      totalRules: this.rules.size,
      activeRules: Array.from(this.rules.values()).filter((r) => r.isActive)
        .length,
      totalTemplates: this.templates.size,
      scheduledTasks: this.scheduledTasks.size,
      isInitialized: this.isInitialized,
    };
  }

  // Adicionar regra personalizada
  addCustomRule(name, ruleData) {
    this.rules.set(name, { ...ruleData, isActive: true });
    logger.info("Regra personalizada adicionada:", name);
  }

  // Adicionar template personalizado
  addCustomTemplate(name, templateData) {
    this.templates.set(name, templateData);
    logger.info("Template personalizado adicionado:", name);
  }
}

module.exports = new AutomationService();
