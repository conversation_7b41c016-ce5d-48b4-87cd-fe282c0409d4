{"name": "uniforms-survey-frontend", "version": "1.0.0", "description": "Frontend React para sistema de pesquisas de satisfação", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.0", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "qrcode.react": "^3.1.0", "socket.io-client": "^4.7.4", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "copy-to-clipboard": "^3.3.3", "react-dropzone": "^14.2.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^4.9.5"}}