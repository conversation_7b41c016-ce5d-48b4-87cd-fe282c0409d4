const { asyncHandler } = require('../middleware/errorHandler');
const automationService = require('../services/automationService');
const chatbotService = require('../services/chatbotService');
const database = require('../config/database');
const { logger } = require('../utils/logger');

// Processar mensagem do chatbot
const processChatbotMessage = asyncHandler(async (req, res) => {
  const { userPhone, message, conversationId } = req.body;

  if (!userPhone || !message) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'userPhone e message são obrigatórios'
    });
  }

  const response = await chatbotService.processMessage(userPhone, message, conversationId);

  // Se deve escalar, criar/atualizar conversa
  if (response.shouldEscalate && conversationId) {
    await database.getClient().conversation.update({
      where: { id: conversationId },
      data: {
        status: 'OPEN',
        priority: 'HIGH',
        escalatedAt: new Date(),
        escalatedBy: 'chatbot'
      }
    });
  }

  res.json({
    response: response.response,
    intent: response.intent,
    confidence: response.confidence,
    shouldEscalate: response.shouldEscalate,
    actions: response.actions
  });
});

// Treinar chatbot
const trainChatbot = asyncHandler(async (req, res) => {
  const { message, correctIntent, userFeedback } = req.body;

  if (!message || !correctIntent) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'message e correctIntent são obrigatórios'
    });
  }

  await chatbotService.trainWithMessage(message, correctIntent, userFeedback);

  res.json({
    message: 'Dados de treinamento salvos com sucesso',
    trainingData: { message, correctIntent, userFeedback }
  });
});

// Obter estatísticas do chatbot
const getChatbotStats = asyncHandler(async (req, res) => {
  const stats = chatbotService.getStats();
  
  // Buscar estatísticas de uso
  const usageStats = await database.getClient().chatbotTraining.groupBy({
    by: ['correctIntent'],
    _count: { id: true },
    orderBy: { _count: { id: 'desc' } },
    take: 10
  });

  res.json({
    ...stats,
    usage: {
      totalTrainingData: await database.getClient().chatbotTraining.count(),
      topIntents: usageStats.map(stat => ({
        intent: stat.correctIntent,
        count: stat._count.id
      }))
    }
  });
});

// Adicionar intent personalizado
const addCustomIntent = asyncHandler(async (req, res) => {
  const { name, patterns, responses, context, actions } = req.body;

  if (!name || !patterns || !responses) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'name, patterns e responses são obrigatórios'
    });
  }

  // Converter patterns para RegExp
  const regexPatterns = patterns.map(pattern => new RegExp(pattern, 'i'));

  const intentData = {
    patterns: regexPatterns,
    responses,
    context,
    actions: actions || []
  };

  chatbotService.addCustomIntent(name, intentData);

  res.json({
    message: 'Intent personalizado adicionado com sucesso',
    intent: { name, ...intentData }
  });
});

// Processar evento de automação
const processAutomationEvent = asyncHandler(async (req, res) => {
  const { eventType, eventData } = req.body;

  if (!eventType || !eventData) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'eventType e eventData são obrigatórios'
    });
  }

  await automationService.processEvent(eventType, eventData);

  res.json({
    message: 'Evento processado com sucesso',
    eventType,
    timestamp: new Date().toISOString()
  });
});

// Obter tarefas agendadas
const getScheduledTasks = asyncHandler(async (req, res) => {
  const { status, type, limit = 50 } = req.query;

  const whereClause = {
    ...(status && { status }),
    ...(type && { type })
  };

  const tasks = await database.getClient().scheduledTask.findMany({
    where: whereClause,
    orderBy: { executeAt: 'asc' },
    take: parseInt(limit)
  });

  const summary = await database.getClient().scheduledTask.groupBy({
    by: ['status'],
    _count: { id: true }
  });

  res.json({
    tasks,
    summary: summary.reduce((acc, item) => {
      acc[item.status] = item._count.id;
      return acc;
    }, {}),
    total: tasks.length
  });
});

// Cancelar tarefa agendada
const cancelScheduledTask = asyncHandler(async (req, res) => {
  const { taskId } = req.params;

  const task = await database.getClient().scheduledTask.findUnique({
    where: { id: taskId }
  });

  if (!task) {
    return res.status(404).json({
      error: 'Tarefa não encontrada',
      message: 'A tarefa especificada não existe'
    });
  }

  if (task.status !== 'scheduled') {
    return res.status(400).json({
      error: 'Tarefa não pode ser cancelada',
      message: `Tarefa está no status: ${task.status}`
    });
  }

  await database.getClient().scheduledTask.update({
    where: { id: taskId },
    data: {
      status: 'cancelled',
      updatedAt: new Date()
    }
  });

  res.json({
    message: 'Tarefa cancelada com sucesso',
    taskId
  });
});

// Obter templates de resposta
const getResponseTemplates = asyncHandler(async (req, res) => {
  const { type, isActive } = req.query;

  const whereClause = {
    ...(type && { type }),
    ...(isActive !== undefined && { isActive: isActive === 'true' })
  };

  const templates = await database.getClient().responseTemplate.findMany({
    where: whereClause,
    orderBy: { name: 'asc' }
  });

  res.json({
    templates,
    total: templates.length
  });
});

// Criar template de resposta
const createResponseTemplate = asyncHandler(async (req, res) => {
  const { name, subject, message, type = 'whatsapp', variables = [] } = req.body;
  const userId = req.user.id;

  if (!name || !message) {
    return res.status(400).json({
      error: 'Dados obrigatórios',
      message: 'name e message são obrigatórios'
    });
  }

  // Verificar se já existe
  const existing = await database.getClient().responseTemplate.findUnique({
    where: { name }
  });

  if (existing) {
    return res.status(409).json({
      error: 'Template já existe',
      message: 'Já existe um template com este nome'
    });
  }

  const template = await database.getClient().responseTemplate.create({
    data: {
      name,
      subject,
      message,
      type,
      variables,
      createdBy: userId
    }
  });

  // Adicionar ao serviço de automação
  automationService.addCustomTemplate(name, {
    subject,
    message,
    type
  });

  res.status(201).json({
    message: 'Template criado com sucesso',
    template
  });
});

// Atualizar template de resposta
const updateResponseTemplate = asyncHandler(async (req, res) => {
  const { templateId } = req.params;
  const { name, subject, message, type, variables, isActive } = req.body;

  const template = await database.getClient().responseTemplate.findUnique({
    where: { id: templateId }
  });

  if (!template) {
    return res.status(404).json({
      error: 'Template não encontrado',
      message: 'O template especificado não existe'
    });
  }

  const updatedTemplate = await database.getClient().responseTemplate.update({
    where: { id: templateId },
    data: {
      ...(name && { name }),
      ...(subject !== undefined && { subject }),
      ...(message && { message }),
      ...(type && { type }),
      ...(variables && { variables }),
      ...(isActive !== undefined && { isActive }),
      updatedAt: new Date()
    }
  });

  // Atualizar no serviço de automação
  if (name || subject || message || type) {
    automationService.addCustomTemplate(updatedTemplate.name, {
      subject: updatedTemplate.subject,
      message: updatedTemplate.message,
      type: updatedTemplate.type
    });
  }

  res.json({
    message: 'Template atualizado com sucesso',
    template: updatedTemplate
  });
});

// Deletar template de resposta
const deleteResponseTemplate = asyncHandler(async (req, res) => {
  const { templateId } = req.params;

  const template = await database.getClient().responseTemplate.findUnique({
    where: { id: templateId }
  });

  if (!template) {
    return res.status(404).json({
      error: 'Template não encontrado',
      message: 'O template especificado não existe'
    });
  }

  await database.getClient().responseTemplate.delete({
    where: { id: templateId }
  });

  res.json({
    message: 'Template deletado com sucesso',
    templateId
  });
});

// Obter estatísticas de automação
const getAutomationStats = asyncHandler(async (req, res) => {
  const { period = '7d' } = req.query;

  // Calcular período
  const now = new Date();
  const days = { '1d': 1, '7d': 7, '30d': 30, '90d': 90 }[period] || 7;
  const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

  const [
    taskStats,
    logStats,
    serviceStats
  ] = await Promise.all([
    database.getClient().scheduledTask.groupBy({
      by: ['type', 'status'],
      where: { createdAt: { gte: startDate } },
      _count: { id: true }
    }),
    database.getClient().automationLog.groupBy({
      by: ['ruleName', 'actionType'],
      where: { executedAt: { gte: startDate } },
      _count: { id: true }
    }),
    automationService.getStats()
  ]);

  // Processar estatísticas de tarefas
  const taskSummary = taskStats.reduce((acc, stat) => {
    if (!acc[stat.type]) acc[stat.type] = {};
    acc[stat.type][stat.status] = stat._count.id;
    return acc;
  }, {});

  // Processar estatísticas de logs
  const ruleSummary = logStats.reduce((acc, stat) => {
    if (!acc[stat.ruleName]) acc[stat.ruleName] = {};
    acc[stat.ruleName][stat.actionType] = stat._count.id;
    return acc;
  }, {});

  res.json({
    period: { start: startDate, end: now, days },
    tasks: {
      summary: taskSummary,
      total: taskStats.reduce((sum, stat) => sum + stat._count.id, 0)
    },
    rules: {
      summary: ruleSummary,
      total: logStats.reduce((sum, stat) => sum + stat._count.id, 0)
    },
    service: serviceStats
  });
});

// Obter logs de automação
const getAutomationLogs = asyncHandler(async (req, res) => {
  const { ruleName, actionType, limit = 100 } = req.query;

  const whereClause = {
    ...(ruleName && { ruleName }),
    ...(actionType && { actionType })
  };

  const logs = await database.getClient().automationLog.findMany({
    where: whereClause,
    orderBy: { executedAt: 'desc' },
    take: parseInt(limit)
  });

  res.json({
    logs,
    total: logs.length
  });
});

// Testar template
const testTemplate = asyncHandler(async (req, res) => {
  const { templateName, testData = {} } = req.body;

  if (!templateName) {
    return res.status(400).json({
      error: 'Template obrigatório',
      message: 'templateName é obrigatório'
    });
  }

  // Buscar template
  const template = await database.getClient().responseTemplate.findUnique({
    where: { name: templateName }
  });

  if (!template) {
    return res.status(404).json({
      error: 'Template não encontrado',
      message: 'O template especificado não existe'
    });
  }

  // Processar template com dados de teste
  let processedMessage = template.message;
  
  // Substituir variáveis
  Object.entries(testData).forEach(([key, value]) => {
    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
    processedMessage = processedMessage.replace(regex, value);
  });

  // Adicionar link de pesquisa se necessário
  if (processedMessage.includes('{{survey_link}}')) {
    const surveyLink = `${process.env.FRONTEND_URL || 'http://localhost:3001'}/survey/latest?phone=${encodeURIComponent(testData.userPhone || 'test')}`;
    processedMessage = processedMessage.replace(/\{\{survey_link\}\}/g, surveyLink);
  }

  res.json({
    template: {
      name: template.name,
      subject: template.subject,
      originalMessage: template.message,
      processedMessage,
      type: template.type,
      variables: template.variables
    },
    testData
  });
});

module.exports = {
  processChatbotMessage,
  trainChatbot,
  getChatbotStats,
  addCustomIntent,
  processAutomationEvent,
  getScheduledTasks,
  cancelScheduledTask,
  getResponseTemplates,
  createResponseTemplate,
  updateResponseTemplate,
  deleteResponseTemplate,
  getAutomationStats,
  getAutomationLogs,
  testTemplate
};
