const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const database = require('../config/database');
const { logger } = require('../utils/logger');

class WebSocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socket
    this.userRooms = new Map(); // userId -> Set of rooms
  }

  // Inicializar WebSocket server
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3001",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Middleware de autenticação
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Token não fornecido'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await database.getClient().user.findUnique({
          where: { id: decoded.id },
          select: { id: true, name: true, email: true, role: true }
        });

        if (!user) {
          return next(new Error('Usuário não encontrado'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();
      } catch (error) {
        logger.error('Erro na autenticação WebSocket:', error);
        next(new Error('Token inválido'));
      }
    });

    // Eventos de conexão
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    logger.info('WebSocket server inicializado');
  }

  // Gerenciar conexão de usuário
  handleConnection(socket) {
    const userId = socket.userId;
    const user = socket.user;

    logger.info('Usuário conectado via WebSocket:', { userId, name: user.name });

    // Armazenar conexão
    this.connectedUsers.set(userId, socket);
    this.userRooms.set(userId, new Set());

    // Entrar em salas baseadas no papel do usuário
    this.joinUserRooms(socket);

    // Eventos do socket
    socket.on('join-conversation', (conversationId) => {
      this.joinConversation(socket, conversationId);
    });

    socket.on('leave-conversation', (conversationId) => {
      this.leaveConversation(socket, conversationId);
    });

    socket.on('mark-as-read', (data) => {
      this.markAsRead(socket, data);
    });

    socket.on('typing-start', (data) => {
      this.handleTyping(socket, data, true);
    });

    socket.on('typing-stop', (data) => {
      this.handleTyping(socket, data, false);
    });

    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });

    // Enviar notificações pendentes
    this.sendPendingNotifications(userId);
  }

  // Entrar em salas baseadas no papel do usuário
  joinUserRooms(socket) {
    const userId = socket.userId;
    const userRole = socket.user.role;

    // Sala geral do usuário
    socket.join(`user:${userId}`);
    this.userRooms.get(userId).add(`user:${userId}`);

    // Salas baseadas no papel
    if (userRole === 'ADMIN') {
      socket.join('admins');
      socket.join('all-conversations');
      socket.join('all-responses');
      this.userRooms.get(userId).add('admins');
      this.userRooms.get(userId).add('all-conversations');
      this.userRooms.get(userId).add('all-responses');
    } else if (userRole === 'AGENT') {
      socket.join('agents');
      socket.join('assigned-conversations');
      this.userRooms.get(userId).add('agents');
      this.userRooms.get(userId).add('assigned-conversations');
    }

    logger.debug('Usuário entrou nas salas:', { 
      userId, 
      rooms: Array.from(this.userRooms.get(userId)) 
    });
  }

  // Entrar em conversa específica
  joinConversation(socket, conversationId) {
    const userId = socket.userId;
    const roomName = `conversation:${conversationId}`;
    
    socket.join(roomName);
    this.userRooms.get(userId).add(roomName);

    logger.debug('Usuário entrou na conversa:', { userId, conversationId });

    // Notificar outros participantes
    socket.to(roomName).emit('user-joined-conversation', {
      userId,
      userName: socket.user.name,
      conversationId
    });
  }

  // Sair de conversa específica
  leaveConversation(socket, conversationId) {
    const userId = socket.userId;
    const roomName = `conversation:${conversationId}`;
    
    socket.leave(roomName);
    this.userRooms.get(userId).delete(roomName);

    logger.debug('Usuário saiu da conversa:', { userId, conversationId });

    // Notificar outros participantes
    socket.to(roomName).emit('user-left-conversation', {
      userId,
      userName: socket.user.name,
      conversationId
    });
  }

  // Marcar mensagem como lida
  async markAsRead(socket, data) {
    try {
      const { conversationId, messageId } = data;
      const userId = socket.userId;

      // Atualizar no banco de dados
      await database.getClient().conversation.update({
        where: { id: conversationId },
        data: {
          messages: {
            // Lógica para marcar mensagem específica como lida
            // Implementar conforme estrutura de dados
          }
        }
      });

      // Notificar outros participantes
      socket.to(`conversation:${conversationId}`).emit('message-read', {
        conversationId,
        messageId,
        readBy: userId,
        readAt: new Date().toISOString()
      });

      logger.debug('Mensagem marcada como lida:', { conversationId, messageId, userId });
    } catch (error) {
      logger.error('Erro ao marcar mensagem como lida:', error);
      socket.emit('error', { message: 'Erro ao marcar mensagem como lida' });
    }
  }

  // Gerenciar indicador de digitação
  handleTyping(socket, data, isTyping) {
    const { conversationId } = data;
    const userId = socket.userId;
    const userName = socket.user.name;

    socket.to(`conversation:${conversationId}`).emit('typing-status', {
      conversationId,
      userId,
      userName,
      isTyping,
      timestamp: new Date().toISOString()
    });

    logger.debug('Status de digitação:', { conversationId, userId, isTyping });
  }

  // Gerenciar desconexão
  handleDisconnection(socket) {
    const userId = socket.userId;
    const user = socket.user;

    logger.info('Usuário desconectado do WebSocket:', { userId, name: user?.name });

    // Remover da lista de conectados
    this.connectedUsers.delete(userId);
    this.userRooms.delete(userId);

    // Notificar salas sobre desconexão
    socket.broadcast.emit('user-disconnected', {
      userId,
      userName: user?.name,
      timestamp: new Date().toISOString()
    });
  }

  // Enviar notificações pendentes
  async sendPendingNotifications(userId) {
    try {
      // Buscar notificações não lidas
      const notifications = await database.getClient().notification.findMany({
        where: {
          userId,
          isRead: false
        },
        orderBy: { createdAt: 'desc' },
        take: 50
      });

      if (notifications.length > 0) {
        const socket = this.connectedUsers.get(userId);
        if (socket) {
          socket.emit('pending-notifications', notifications);
          logger.debug('Notificações pendentes enviadas:', { userId, count: notifications.length });
        }
      }
    } catch (error) {
      logger.error('Erro ao enviar notificações pendentes:', error);
    }
  }

  // Notificar nova resposta NPS
  async notifyNewResponse(response) {
    try {
      const notification = {
        id: `response-${response.id}`,
        type: 'new_response',
        title: 'Nova Resposta Recebida',
        message: `Nova resposta NPS: ${response.score}/10`,
        data: {
          responseId: response.id,
          surveyId: response.surveyId,
          score: response.score,
          isDetractor: response.isDetractor,
          userPhone: response.userPhone
        },
        timestamp: new Date().toISOString(),
        priority: response.isDetractor ? 'high' : 'normal'
      };

      // Notificar admins e agentes
      this.io.to('admins').emit('new-response', notification);
      this.io.to('agents').emit('new-response', notification);

      // Se for detrator, notificação especial
      if (response.isDetractor) {
        const detractorNotification = {
          ...notification,
          type: 'detractor_alert',
          title: '🚨 DETRATOR IDENTIFICADO',
          message: `Cliente insatisfeito (${response.score}/10) precisa de atenção imediata!`,
          priority: 'urgent'
        };

        this.io.to('admins').emit('detractor-alert', detractorNotification);
        
        // Salvar notificação no banco
        await this.saveNotification(detractorNotification, 'ADMIN');
      }

      logger.info('Notificação de nova resposta enviada:', { 
        responseId: response.id, 
        isDetractor: response.isDetractor 
      });
    } catch (error) {
      logger.error('Erro ao notificar nova resposta:', error);
    }
  }

  // Notificar nova mensagem na conversa
  async notifyNewMessage(conversationId, message, sender) {
    try {
      const notification = {
        id: `message-${message.id}`,
        type: 'new_message',
        title: 'Nova Mensagem',
        message: `${sender.name}: ${message.text.substring(0, 50)}...`,
        data: {
          conversationId,
          messageId: message.id,
          senderId: sender.id,
          senderName: sender.name
        },
        timestamp: new Date().toISOString(),
        priority: 'normal'
      };

      // Notificar sala da conversa
      this.io.to(`conversation:${conversationId}`).emit('new-message', {
        conversationId,
        message,
        sender,
        notification
      });

      logger.debug('Notificação de nova mensagem enviada:', { conversationId, messageId: message.id });
    } catch (error) {
      logger.error('Erro ao notificar nova mensagem:', error);
    }
  }

  // Salvar notificação no banco
  async saveNotification(notification, targetRole = null, targetUserId = null) {
    try {
      const whereClause = targetUserId 
        ? { id: targetUserId }
        : targetRole 
        ? { role: targetRole }
        : {};

      const users = await database.getClient().user.findMany({
        where: whereClause,
        select: { id: true }
      });

      const notificationData = users.map(user => ({
        userId: user.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority || 'normal',
        isRead: false
      }));

      await database.getClient().notification.createMany({
        data: notificationData
      });

      logger.debug('Notificações salvas no banco:', { count: notificationData.length });
    } catch (error) {
      logger.error('Erro ao salvar notificação:', error);
    }
  }

  // Verificar se usuário está online
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }

  // Obter usuários online
  getOnlineUsers() {
    return Array.from(this.connectedUsers.keys());
  }

  // Obter estatísticas de conexão
  getConnectionStats() {
    return {
      totalConnections: this.connectedUsers.size,
      onlineUsers: this.getOnlineUsers(),
      totalRooms: Array.from(this.userRooms.values()).reduce((acc, rooms) => acc + rooms.size, 0)
    };
  }
}

module.exports = new WebSocketService();
