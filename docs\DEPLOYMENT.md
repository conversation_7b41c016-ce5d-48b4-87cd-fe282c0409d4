# Guia de Deploy - UNIFORMS2 Survey System

Este guia explica como fazer o deploy do sistema em diferentes ambientes.

## 🐳 Deploy com Docker (Recomendado)

### Pré-requisitos
- Docker 20.10+
- Docker Compose 2.0+
- 4GB RAM mínimo
- 20GB espaço em disco

### Deploy Completo

```bash
# 1. <PERSON><PERSON> o repositório
git clone <repository-url>
cd uniforms-survey-system

# 2. Configure variáveis de ambiente
cp .env.example .env
# Edite o .env com suas configurações

# 3. Inicie todos os serviços
docker-compose up -d

# 4. Execute migrações do banco
docker-compose exec backend npm run prisma:migrate
docker-compose exec backend npm run prisma:seed

# 5. Verifique se tudo está funcionando
docker-compose ps
```

### Serviços Incluídos

- **PostgreSQL** (porta 5432)
- **Redis** (porta 6379)
- **Evolution API** (porta 8080)
- **Backend Node.js** (porta 3000)
- **Frontend React** (porta 3001)
- **n8n** (porta 5678) - opcional
- **Nginx** (portas 80/443) - apenas em produção

### Comandos Úteis

```bash
# Ver logs
docker-compose logs -f backend
docker-compose logs -f evolution-api

# Parar serviços
docker-compose down

# Rebuild e restart
docker-compose up -d --build

# Backup do banco
docker-compose exec postgres pg_dump -U uniforms_user uniforms_survey > backup.sql

# Restore do banco
docker-compose exec -T postgres psql -U uniforms_user uniforms_survey < backup.sql
```

## 🌐 Deploy Manual

### Backend (Node.js)

```bash
# 1. Instalar dependências
npm install

# 2. Configurar banco de dados
npm run prisma:migrate
npm run prisma:seed

# 3. Build (se necessário)
npm run build

# 4. Iniciar em produção
NODE_ENV=production npm start
```

### Frontend (React)

```bash
# 1. Instalar dependências
cd frontend
npm install

# 2. Build para produção
npm run build

# 3. Servir com nginx ou servidor web
# Copiar pasta build/ para servidor web
```

## ☁️ Deploy na Nuvem

### AWS EC2

```bash
# 1. Criar instância EC2 (t3.medium ou superior)
# 2. Instalar Docker
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# 3. Instalar Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 4. Clone e deploy
git clone <repository-url>
cd uniforms-survey-system
docker-compose up -d
```

### Google Cloud Platform

```bash
# 1. Criar VM no Compute Engine
gcloud compute instances create uniforms-server \
  --image-family=ubuntu-2004-lts \
  --image-project=ubuntu-os-cloud \
  --machine-type=e2-standard-2 \
  --boot-disk-size=50GB

# 2. SSH e instalar Docker
gcloud compute ssh uniforms-server
sudo apt update && sudo apt install -y docker.io docker-compose
sudo usermod -a -G docker $USER

# 3. Deploy
git clone <repository-url>
cd uniforms-survey-system
docker-compose up -d
```

### DigitalOcean

```bash
# 1. Criar Droplet (4GB RAM, Ubuntu 20.04)
# 2. Instalar Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -a -G docker $USER

# 3. Deploy
git clone <repository-url>
cd uniforms-survey-system
docker-compose up -d
```

## 🔒 Configurações de Segurança

### SSL/HTTPS

```bash
# 1. Instalar Certbot
sudo apt install certbot python3-certbot-nginx

# 2. Obter certificado
sudo certbot --nginx -d seu-dominio.com

# 3. Configurar renovação automática
sudo crontab -e
# Adicionar: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall

```bash
# Ubuntu/Debian
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### Variáveis de Ambiente Seguras

```env
# Produção - .env
NODE_ENV=production
JWT_SECRET=<gerar-chave-forte-64-caracteres>
DATABASE_URL=postgresql://user:password@localhost:5432/db
WHATSAPP_API_KEY=<chave-forte-evolution-api>

# Não usar valores padrão em produção!
```

## 📊 Monitoramento

### Health Checks

```bash
# Backend
curl http://localhost:3000/health

# Frontend
curl http://localhost:3001

# Evolution API
curl http://localhost:8080

# PostgreSQL
docker-compose exec postgres pg_isready
```

### Logs

```bash
# Logs do sistema
docker-compose logs -f --tail=100

# Logs específicos
docker-compose logs backend
docker-compose logs postgres
docker-compose logs evolution-api
```

### Métricas

```bash
# Uso de recursos
docker stats

# Espaço em disco
df -h

# Memória
free -h

# Processos
htop
```

## 🔄 Backup e Restore

### Backup Automático

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Backup do banco
docker-compose exec -T postgres pg_dump -U uniforms_user uniforms_survey > $BACKUP_DIR/db_$DATE.sql

# Backup dos uploads
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz uploads/

# Backup das configurações
cp .env $BACKUP_DIR/env_$DATE.backup

# Limpar backups antigos (manter 7 dias)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup concluído: $DATE"
```

### Restore

```bash
# Restore do banco
docker-compose exec -T postgres psql -U uniforms_user uniforms_survey < backup.sql

# Restore dos uploads
tar -xzf uploads_backup.tar.gz

# Restart dos serviços
docker-compose restart
```

## 🚀 Otimizações de Performance

### Nginx (Proxy Reverso)

```nginx
# nginx.conf
upstream backend {
    server backend:3000;
}

server {
    listen 80;
    server_name seu-dominio.com;

    # Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript;

    # Static files
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API proxy
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Frontend
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
}
```

### PostgreSQL

```sql
-- Otimizações de performance
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

### Redis

```bash
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 🔧 Troubleshooting

### Problemas Comuns

1. **Erro de conexão com banco**
   ```bash
   # Verificar se PostgreSQL está rodando
   docker-compose ps postgres
   
   # Ver logs
   docker-compose logs postgres
   ```

2. **Evolution API não conecta**
   ```bash
   # Verificar logs
   docker-compose logs evolution-api
   
   # Testar conexão
   curl http://localhost:8080
   ```

3. **Frontend não carrega**
   ```bash
   # Verificar build
   docker-compose logs frontend
   
   # Rebuild
   docker-compose up -d --build frontend
   ```

### Comandos de Debug

```bash
# Entrar no container
docker-compose exec backend bash
docker-compose exec postgres psql -U uniforms_user uniforms_survey

# Verificar rede
docker network ls
docker network inspect uniforms-survey-system_uniforms-network

# Verificar volumes
docker volume ls
docker volume inspect uniforms-survey-system_postgres_data
```

## 📞 Suporte

Para problemas de deploy:

1. Verifique os logs: `docker-compose logs`
2. Consulte a documentação: `docs/`
3. Verifique issues no GitHub
4. Entre em contato com o suporte técnico

---

**UNIFORMS2** - Deploy Guide 🚀
