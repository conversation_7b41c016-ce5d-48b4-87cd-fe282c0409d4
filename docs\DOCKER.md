# 🐳 G<PERSON>a Docker - UNIFORMS2 Survey System

Este guia explica como usar o sistema completamente dockerizado.

## 🚀 <PERSON><PERSON><PERSON> R<PERSON>pido

### Opção 1: Make<PERSON>le (Recomendado)
```bash
# Setup completo em um comando
make quick-start

# Ou passo a passo
make setup-env
make dev
make db-migrate
make db-seed
```

### Opção 2: Docker Compose Manual
```bash
# Desenvolvimento
docker-compose up -d

# Produção
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 📋 Comandos Disponíveis

### Desenvolvimento
```bash
make dev              # Iniciar desenvolvimento
make dev-logs         # Ver logs
make dev-stop         # Parar desenvolvimento
```

### Produção
```bash
make build            # Build das imagens
make start            # Iniciar produção
make stop             # Parar sistema
make restart          # Reiniciar
```

### Banco de Dad<PERSON>
```bash
make db-migrate       # Executar migrações
make db-seed          # Popular com dados iniciais
make db-reset         # Resetar banco (CUIDADO!)
make db-studio        # Abrir Prisma Studio
```

### Backup e Restore
```bash
make backup           # Fazer backup
make restore BACKUP_FILE=backup.sql  # Restaurar backup
```

### Monitoramento
```bash
make logs             # Ver todos os logs
make logs-backend     # Logs do backend
make status           # Status dos containers
make health           # Verificar saúde dos serviços
```

### Utilitários
```bash
make shell-backend    # Shell no backend
make shell-postgres   # Shell no PostgreSQL
make shell-redis      # Shell no Redis
make clean            # Limpar recursos
```

## 🏗️ Arquitetura Docker

### Serviços Principais

#### 🔧 Desenvolvimento (`docker-compose.override.yml`)
- **Backend**: Node.js com hot reload
- **Frontend**: React com hot reload
- **PostgreSQL**: Banco de desenvolvimento
- **Redis**: Cache e filas
- **Evolution API**: WhatsApp
- **n8n**: Automação
- **Adminer**: Interface do banco
- **Redis Commander**: Interface do Redis
- **Mailhog**: Teste de emails

#### 🚀 Produção (`docker-compose.prod.yml`)
- **Backend**: Node.js otimizado (multi-replica)
- **Frontend**: React buildado com Nginx
- **PostgreSQL**: Configuração de produção
- **Redis**: Configuração de produção
- **Evolution API**: Configuração de produção
- **n8n**: Configuração de produção
- **Nginx**: Proxy reverso e load balancer
- **Prometheus**: Métricas
- **Grafana**: Dashboards
- **Node Exporter**: Métricas do sistema

### Volumes Persistentes

```yaml
# Desenvolvimento
postgres_dev_data     # Dados do PostgreSQL dev
redis_dev_data        # Dados do Redis dev
n8n_dev_data         # Workflows do n8n dev

# Produção
postgres_prod_data    # Dados do PostgreSQL prod
redis_prod_data       # Dados do Redis prod
prometheus_data       # Métricas do Prometheus
grafana_data         # Dashboards do Grafana
```

## 🔧 Configuração Avançada

### Variáveis de Ambiente

Crie um arquivo `.env` baseado no `.env.example`:

```env
# Banco de Dados
POSTGRES_PASSWORD=sua_senha_segura
POSTGRES_PORT=5432

# Backend
NODE_ENV=production
JWT_SECRET=sua_chave_jwt_super_secreta
BACKEND_PORT=3000

# WhatsApp
WHATSAPP_API_KEY=sua_chave_evolution_api
WHATSAPP_INSTANCE_NAME=uniforms-main

# Redis
REDIS_PORT=6379

# URLs
FRONTEND_URL=https://seu-dominio.com
BACKEND_URL=https://api.seu-dominio.com
```

### Secrets (Produção)

Para produção, use Docker secrets:

```bash
# Criar diretório de secrets
mkdir -p secrets

# Criar arquivos de secrets
echo "senha_super_segura" > secrets/postgres_password.txt
echo "chave_evolution_api" > secrets/evolution_api_key.txt
echo "senha_n8n" > secrets/n8n_password.txt
echo "senha_grafana" > secrets/grafana_password.txt
```

### Multi-Stage Builds

O Dockerfile do backend usa multi-stage builds:

```dockerfile
# Desenvolvimento
FROM node:18-alpine AS development
# ... configurações de dev

# Build
FROM node:18-alpine AS build
# ... build otimizado

# Produção
FROM node:18-alpine AS production
# ... imagem final mínima
```

## 📊 Monitoramento

### Dashboards Disponíveis

- **Grafana**: http://localhost:3001
  - Dashboard do sistema
  - Métricas de performance
  - Alertas configurados

- **Prometheus**: http://localhost:9090
  - Métricas brutas
  - Queries personalizadas
  - Targets de monitoramento

### Métricas Coletadas

- **Sistema**: CPU, memória, disco, rede
- **Aplicação**: Requests/s, latência, erros
- **Banco**: Conexões, queries, performance
- **Redis**: Hits/misses, memória, comandos
- **WhatsApp**: Mensagens enviadas/recebidas

## 🔒 Segurança

### Configurações de Segurança

1. **Usuários não-root** em todos os containers
2. **Secrets** para senhas sensíveis
3. **Health checks** para todos os serviços
4. **Resource limits** para evitar DoS
5. **Network isolation** entre serviços

### Hardening de Produção

```yaml
# Exemplo de configuração segura
services:
  backend:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
```

## 🚀 Deploy em Produção

### 1. Preparação

```bash
# Clone do repositório
git clone <repository-url>
cd uniforms-survey-system

# Configurar ambiente
cp .env.example .env
# Editar .env com configurações de produção

# Criar secrets
mkdir secrets
echo "senha_segura" > secrets/postgres_password.txt
```

### 2. Build e Deploy

```bash
# Build das imagens
make build

# Iniciar em produção
make start

# Configurar banco
make db-migrate
make db-seed
```

### 3. Configurar SSL

```bash
# Instalar Certbot
sudo apt install certbot

# Obter certificado
sudo certbot certonly --standalone -d seu-dominio.com

# Copiar certificados
sudo cp /etc/letsencrypt/live/seu-dominio.com/*.pem docker/nginx/ssl/
```

### 4. Configurar Backup Automático

```bash
# Adicionar ao crontab
crontab -e

# Backup diário às 2h
0 2 * * * cd /path/to/uniforms && make backup
```

## 🔧 Troubleshooting

### Problemas Comuns

#### Container não inicia
```bash
# Ver logs detalhados
docker-compose logs nome-do-servico

# Verificar recursos
docker stats

# Verificar saúde
docker-compose ps
```

#### Banco não conecta
```bash
# Verificar se PostgreSQL está rodando
make shell-postgres

# Verificar logs
make logs-postgres

# Resetar banco (CUIDADO!)
make db-reset
```

#### WhatsApp não conecta
```bash
# Verificar Evolution API
curl http://localhost:8080

# Ver logs
make logs-evolution

# Gerar novo QR Code
# Acessar http://localhost:8080 e reconectar
```

### Comandos de Debug

```bash
# Entrar no container
docker-compose exec backend bash

# Verificar variáveis de ambiente
docker-compose exec backend env

# Verificar conectividade
docker-compose exec backend ping postgres

# Verificar portas
docker-compose exec backend netstat -tlnp
```

## 📈 Performance

### Otimizações Aplicadas

1. **Multi-stage builds** para imagens menores
2. **Health checks** para recuperação automática
3. **Resource limits** para estabilidade
4. **Cache layers** no Docker build
5. **Configurações otimizadas** para PostgreSQL/Redis

### Monitoramento de Performance

```bash
# Ver uso de recursos
docker stats

# Métricas detalhadas
make monitor

# Logs de performance
make logs | grep -i "slow\|error\|timeout"
```

## 🆘 Suporte

### Logs Importantes

```bash
# Logs da aplicação
make logs-backend

# Logs do banco
make logs-postgres

# Logs do sistema
docker system events
```

### Comandos de Emergência

```bash
# Parar tudo
make stop

# Limpar e reiniciar
make clean
make quick-start

# Backup de emergência
make backup
```

---

**UNIFORMS2** - Sistema Completamente Dockerizado 🐳
