{"name": "uniforms-survey-system", "version": "1.0.0", "description": "Sistema SaaS para pesquisas de satisfação com integração WhatsApp", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "node prisma/seed.js", "prisma:reset": "prisma migrate reset --force", "setup": "npm install && npm run prisma:migrate && npm run prisma:seed", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": ["survey", "whatsapp", "nps", "saas"], "author": "UNIFORMS2", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "qrcode": "^1.5.3", "axios": "^1.6.0", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "@prisma/client": "^5.6.0", "redis": "^4.6.10", "bull": "^4.12.2", "socket.io": "^4.7.4", "web-push": "^3.6.6", "winston": "^3.11.0", "compression": "^1.7.4", "moment": "^2.29.4", "uuid": "^9.0.1", "exceljs": "^4.4.0", "pdfkit": "^0.14.0", "node-cron": "^3.0.3", "speakeasy": "^2.0.0", "express-slow-down": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1", "prisma": "^5.6.0", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}}