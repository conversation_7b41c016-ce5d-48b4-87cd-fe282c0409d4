const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...');

  // Criar usuário admin padrão
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      role: 'ADMIN'
    }
  });

  console.log('✅ Usuário admin criado:', adminUser.email);

  // Criar usuário de teste
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: await bcrypt.hash('teste123', 10),
      name: '<PERSON><PERSON><PERSON><PERSON>',
      role: 'USER'
    }
  });

  console.log('✅ Usuário teste criado:', testUser.email);

  // Criar departamentos padrão
  const departments = [
    {
      name: 'Suporte Técnico',
      keywords: ['suporte', 'técnico', 'problema', 'erro', 'bug', 'não funciona'],
      description: 'Departamento responsável por questões técnicas e suporte'
    },
    {
      name: 'Atendimento ao Cliente',
      keywords: ['atendimento', 'cliente', 'dúvida', 'informação', 'ajuda'],
      description: 'Departamento de atendimento geral ao cliente'
    },
    {
      name: 'Infraestrutura',
      keywords: ['falta de água', 'energia', 'internet', 'infraestrutura', 'serviços'],
      description: 'Departamento responsável por questões de infraestrutura'
    },
    {
      name: 'Comercial',
      keywords: ['vendas', 'comercial', 'preço', 'orçamento', 'contrato'],
      description: 'Departamento comercial e vendas'
    },
    {
      name: 'Financeiro',
      keywords: ['financeiro', 'pagamento', 'cobrança', 'fatura', 'boleto'],
      description: 'Departamento financeiro e cobrança'
    }
  ];

  for (const dept of departments) {
    const department = await prisma.department.upsert({
      where: { name: dept.name },
      update: {},
      create: dept
    });
    console.log('✅ Departamento criado:', department.name);
  }

  // Criar instância WhatsApp padrão
  const whatsappInstance = await prisma.whatsAppInstance.upsert({
    where: { instanceName: 'uniforms-main' },
    update: {},
    create: {
      instanceName: 'uniforms-main',
      apiUrl: process.env.WHATSAPP_API_URL || 'http://localhost:8080',
      apiKey: process.env.WHATSAPP_API_KEY || 'default-key',
      isActive: true
    }
  });

  console.log('✅ Instância WhatsApp criada:', whatsappInstance.instanceName);

  // Criar pesquisa de exemplo
  const sampleSurvey = await prisma.survey.upsert({
    where: { id: 'sample-survey-id' },
    update: {},
    create: {
      id: 'sample-survey-id',
      title: 'Pesquisa de Satisfação - Exemplo',
      description: 'Pesquisa de exemplo para testar o sistema',
      questions: [
        {
          id: 1,
          type: 'nps',
          question: 'De 0 a 10, o quanto você recomendaria nossos serviços?',
          required: true
        },
        {
          id: 2,
          type: 'text',
          question: 'Deixe seu comentário ou sugestão:',
          required: false
        },
        {
          id: 3,
          type: 'multiple_choice',
          question: 'Qual aspecto mais te agrada em nossos serviços?',
          options: [
            'Qualidade do atendimento',
            'Rapidez na resolução',
            'Preço justo',
            'Facilidade de uso',
            'Outro'
          ],
          required: true
        }
      ],
      userId: adminUser.id,
      isActive: true
    }
  });

  console.log('✅ Pesquisa de exemplo criada:', sampleSurvey.title);

  // Criar algumas respostas de exemplo
  const sampleResponses = [
    {
      userPhone: '5511999999999',
      score: 9,
      feedback: 'Excelente atendimento, muito satisfeito!',
      isPromoter: true,
      surveyId: sampleSurvey.id
    },
    {
      userPhone: '5511888888888',
      score: 7,
      feedback: 'Bom serviço, mas pode melhorar',
      isNeutral: true,
      surveyId: sampleSurvey.id
    },
    {
      userPhone: '5511777777777',
      score: 4,
      feedback: 'Atendimento demorado, falta de água constante',
      isDetractor: true,
      surveyId: sampleSurvey.id
    }
  ];

  for (const response of sampleResponses) {
    const createdResponse = await prisma.response.create({
      data: response
    });
    console.log('✅ Resposta de exemplo criada para:', createdResponse.userPhone);
  }

  console.log('🎉 Seed concluído com sucesso!');
}

main()
  .catch((e) => {
    console.error('❌ Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
