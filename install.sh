#!/bin/bash

# Script de instalação do UNIFORMS2 Survey System
# Este script automatiza a configuração inicial do sistema

set -e

echo "🚀 UNIFORMS2 Survey System - Instalação Automática"
echo "=================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se Node.js está instalado
check_nodejs() {
    print_status "Verificando Node.js..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js não encontrado. Por favor, instale Node.js 18+ antes de continuar."
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js versão 18+ é necessária. Versão atual: $(node -v)"
        exit 1
    fi
    
    print_success "Node.js $(node -v) encontrado"
}

# Verificar se PostgreSQL está instalado
check_postgresql() {
    print_status "Verificando PostgreSQL..."
    if ! command -v psql &> /dev/null; then
        print_warning "PostgreSQL não encontrado. Certifique-se de ter PostgreSQL 13+ instalado."
        print_status "Para instalar no Ubuntu/Debian: sudo apt install postgresql postgresql-contrib"
        print_status "Para instalar no macOS: brew install postgresql"
    else
        print_success "PostgreSQL encontrado"
    fi
}

# Instalar dependências do backend
install_backend() {
    print_status "Instalando dependências do backend..."
    npm install
    print_success "Dependências do backend instaladas"
}

# Instalar dependências do frontend
install_frontend() {
    print_status "Instalando dependências do frontend..."
    cd frontend
    npm install
    cd ..
    print_success "Dependências do frontend instaladas"
}

# Configurar variáveis de ambiente
setup_env() {
    print_status "Configurando variáveis de ambiente..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Arquivo .env criado a partir do .env.example"
        
        # Gerar JWT secret aleatório
        JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || echo "your-super-secret-jwt-key-$(date +%s)")
        
        # Substituir valores no .env
        if command -v sed &> /dev/null; then
            sed -i.bak "s/your-super-secret-jwt-key-here/$JWT_SECRET/g" .env
            rm .env.bak 2>/dev/null || true
            print_success "JWT_SECRET gerado automaticamente"
        fi
        
        print_warning "IMPORTANTE: Edite o arquivo .env com suas configurações:"
        print_warning "- DATABASE_URL: Configure sua conexão PostgreSQL"
        print_warning "- WHATSAPP_API_URL: Configure sua instância Evolution API/WAHA"
        print_warning "- N8N_WEBHOOK_URL: Configure sua instância n8n (opcional)"
    else
        print_warning "Arquivo .env já existe. Pulando configuração."
    fi
}

# Configurar banco de dados
setup_database() {
    print_status "Configurando banco de dados..."
    
    # Verificar se DATABASE_URL está configurada
    if grep -q "postgresql://username:password@localhost:5432/uniforms_survey" .env; then
        print_warning "DATABASE_URL ainda está com valores padrão."
        print_warning "Configure sua conexão PostgreSQL no arquivo .env antes de continuar."
        read -p "Pressione Enter quando tiver configurado o DATABASE_URL..."
    fi
    
    # Executar migrações
    print_status "Executando migrações do banco de dados..."
    npm run prisma:migrate
    
    # Executar seed
    print_status "Populando banco com dados iniciais..."
    npm run prisma:seed
    
    print_success "Banco de dados configurado com sucesso"
    print_success "Usuários criados:"
    print_success "  Admin: <EMAIL> / admin123"
    print_success "  Teste: <EMAIL> / teste123"
}

# Verificar serviços externos
check_external_services() {
    print_status "Verificando serviços externos..."
    
    # Verificar Redis (opcional)
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            print_success "Redis está rodando"
        else
            print_warning "Redis instalado mas não está rodando"
        fi
    else
        print_warning "Redis não encontrado (opcional para cache e filas)"
    fi
    
    # Verificar se Evolution API está rodando
    WHATSAPP_URL=$(grep WHATSAPP_API_URL .env | cut -d'=' -f2 | tr -d '"')
    if [ ! -z "$WHATSAPP_URL" ] && [ "$WHATSAPP_URL" != "http://localhost:8080" ]; then
        print_status "Testando conexão com Evolution API..."
        if curl -s "$WHATSAPP_URL" &> /dev/null; then
            print_success "Evolution API está acessível"
        else
            print_warning "Evolution API não está acessível em $WHATSAPP_URL"
        fi
    else
        print_warning "Configure WHATSAPP_API_URL no .env para testar conexão"
    fi
}

# Criar scripts de inicialização
create_scripts() {
    print_status "Criando scripts de inicialização..."
    
    # Script para desenvolvimento
    cat > start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Iniciando UNIFORMS2 em modo desenvolvimento..."

# Iniciar backend
echo "📡 Iniciando backend..."
npm run dev &
BACKEND_PID=$!

# Aguardar backend inicializar
sleep 5

# Iniciar frontend
echo "🎨 Iniciando frontend..."
cd frontend
npm start &
FRONTEND_PID=$!

echo "✅ Sistema iniciado!"
echo "📊 Backend: http://localhost:3000"
echo "🌐 Frontend: http://localhost:3001"
echo ""
echo "Para parar o sistema, pressione Ctrl+C"

# Aguardar interrupção
trap "echo 'Parando sistema...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT
wait
EOF

    chmod +x start-dev.sh
    
    # Script para produção
    cat > start-prod.sh << 'EOF'
#!/bin/bash
echo "🚀 Iniciando UNIFORMS2 em modo produção..."

# Build do frontend
echo "🔨 Fazendo build do frontend..."
cd frontend
npm run build
cd ..

# Iniciar backend
echo "📡 Iniciando backend..."
NODE_ENV=production npm start
EOF

    chmod +x start-prod.sh
    
    print_success "Scripts de inicialização criados:"
    print_success "  ./start-dev.sh - Modo desenvolvimento"
    print_success "  ./start-prod.sh - Modo produção"
}

# Função principal
main() {
    echo ""
    print_status "Iniciando instalação..."
    echo ""
    
    # Verificações
    check_nodejs
    check_postgresql
    
    # Instalação
    install_backend
    install_frontend
    
    # Configuração
    setup_env
    
    # Perguntar se deve configurar o banco
    echo ""
    read -p "Deseja configurar o banco de dados agora? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
    else
        print_warning "Configuração do banco pulada. Execute 'npm run setup' quando estiver pronto."
    fi
    
    # Verificações finais
    check_external_services
    create_scripts
    
    echo ""
    print_success "🎉 Instalação concluída com sucesso!"
    echo ""
    print_status "Próximos passos:"
    print_status "1. Configure o arquivo .env com suas credenciais"
    print_status "2. Configure PostgreSQL e execute: npm run setup"
    print_status "3. Configure Evolution API/WAHA para WhatsApp"
    print_status "4. Execute: ./start-dev.sh para iniciar em desenvolvimento"
    echo ""
    print_status "Documentação completa: docs/SETUP.md"
    print_status "API Documentation: docs/API.md"
    echo ""
    print_success "Bom desenvolvimento! 🚀"
}

# Executar função principal
main "$@"
