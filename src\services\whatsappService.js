const axios = require('axios');
const database = require('../config/database');
const { logger } = require('../utils/logger');

class WhatsAppService {
  constructor() {
    this.apiUrl = process.env.WHATSAPP_API_URL || 'http://localhost:8080';
    this.apiKey = process.env.WHATSAPP_API_KEY;
    this.defaultInstance = process.env.WHATSAPP_INSTANCE_NAME || 'uniforms-main';
  }

  // Configurar headers para requisições
  getHeaders(instanceName = null) {
    return {
      'Content-Type': 'application/json',
      ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` }),
      ...(instanceName && { 'Instance': instanceName })
    };
  }

  // Criar nova instância WhatsApp
  async createInstance(instanceName, webhookUrl = null) {
    try {
      const response = await axios.post(`${this.apiUrl}/instance/create`, {
        instanceName,
        webhook: webhookUrl || `${process.env.BACKEND_URL}/webhooks/whatsapp`,
        webhookByEvents: true,
        webhookBase64: false,
        events: [
          'APPLICATION_STARTUP',
          'QRCODE_UPDATED',
          'CONNECTION_UPDATE',
          'MESSAGES_UPSERT',
          'MESSAGES_UPDATE',
          'SEND_MESSAGE'
        ]
      }, {
        headers: this.getHeaders()
      });

      // Salvar instância no banco
      const instance = await database.getClient().whatsAppInstance.create({
        data: {
          instanceName,
          apiUrl: this.apiUrl,
          apiKey: this.apiKey,
          isActive: true
        }
      });

      logger.info('Instância WhatsApp criada:', { instanceName });
      return { success: true, instance, data: response.data };

    } catch (error) {
      logger.error('Erro ao criar instância WhatsApp:', error);
      throw new Error(`Erro ao criar instância: ${error.message}`);
    }
  }

  // Obter QR Code para conexão
  async getQRCode(instanceName = this.defaultInstance) {
    try {
      const response = await axios.get(`${this.apiUrl}/instance/connect/${instanceName}`, {
        headers: this.getHeaders(instanceName)
      });

      return response.data;
    } catch (error) {
      logger.error('Erro ao obter QR Code:', error);
      throw new Error(`Erro ao obter QR Code: ${error.message}`);
    }
  }

  // Verificar status da instância
  async getInstanceStatus(instanceName = this.defaultInstance) {
    try {
      const response = await axios.get(`${this.apiUrl}/instance/connectionState/${instanceName}`, {
        headers: this.getHeaders(instanceName)
      });

      return response.data;
    } catch (error) {
      logger.error('Erro ao verificar status da instância:', error);
      throw new Error(`Erro ao verificar status: ${error.message}`);
    }
  }

  // Enviar mensagem de texto
  async sendTextMessage(instanceName, number, message) {
    try {
      const response = await axios.post(`${this.apiUrl}/message/sendText/${instanceName}`, {
        number: this.formatPhoneNumber(number),
        text: message
      }, {
        headers: this.getHeaders(instanceName)
      });

      logger.info('Mensagem de texto enviada:', { instanceName, number, messageId: response.data.key?.id });
      return response.data;

    } catch (error) {
      logger.error('Erro ao enviar mensagem de texto:', error);
      throw new Error(`Erro ao enviar mensagem: ${error.message}`);
    }
  }

  // Enviar mensagem com botões
  async sendButtonMessage(instanceName, number, text, buttons) {
    try {
      const response = await axios.post(`${this.apiUrl}/message/sendButtons/${instanceName}`, {
        number: this.formatPhoneNumber(number),
        text,
        buttons: buttons.map((button, index) => ({
          buttonId: `btn_${index}`,
          buttonText: { displayText: button },
          type: 1
        }))
      }, {
        headers: this.getHeaders(instanceName)
      });

      logger.info('Mensagem com botões enviada:', { instanceName, number, buttonsCount: buttons.length });
      return response.data;

    } catch (error) {
      logger.error('Erro ao enviar mensagem com botões:', error);
      throw new Error(`Erro ao enviar mensagem com botões: ${error.message}`);
    }
  }

  // Enviar pesquisa NPS via WhatsApp
  async sendNPSSurvey(instanceName, number, surveyId) {
    try {
      // Buscar dados da pesquisa
      const survey = await database.getClient().survey.findUnique({
        where: { id: surveyId, isActive: true }
      });

      if (!survey) {
        throw new Error('Pesquisa não encontrada ou inativa');
      }

      const questions = JSON.parse(JSON.stringify(survey.questions));
      const npsQuestion = questions.find(q => q.type === 'nps');

      if (!npsQuestion) {
        throw new Error('Pesquisa não possui pergunta NPS');
      }

      // Enviar pergunta NPS com botões
      const npsButtons = Array.from({length: 11}, (_, i) => i.toString());
      
      const message = `📊 *${survey.title}*\n\n${npsQuestion.question}\n\n_Escolha uma nota de 0 a 10:_`;

      await this.sendButtonMessage(instanceName, number, message, npsButtons);

      logger.info('Pesquisa NPS enviada via WhatsApp:', { surveyId, number });
      return { success: true, surveyId, number };

    } catch (error) {
      logger.error('Erro ao enviar pesquisa NPS:', error);
      throw error;
    }
  }

  // Processar resposta de pesquisa
  async processSurveyResponse(instanceName, number, message, surveyId) {
    try {
      // Verificar se é uma resposta numérica (NPS)
      const score = parseInt(message);
      
      if (isNaN(score) || score < 0 || score > 10) {
        // Se não for um score válido, tratar como feedback textual
        await this.sendTextMessage(instanceName, number, 
          'Por favor, envie uma nota de 0 a 10 para a pergunta anterior, ou digite seu comentário.');
        return;
      }

      // Classificar resposta NPS
      let isDetractor = false;
      let isNeutral = false;
      let isPromoter = false;

      if (score <= 6) {
        isDetractor = true;
      } else if (score <= 8) {
        isNeutral = true;
      } else {
        isPromoter = true;
      }

      // Salvar resposta no banco
      const response = await database.getClient().response.create({
        data: {
          surveyId,
          userPhone: this.formatPhoneNumber(number),
          score,
          isDetractor,
          isNeutral,
          isPromoter
        }
      });

      // Enviar mensagem de agradecimento
      let thankYouMessage = '🙏 Obrigado pela sua avaliação!\n\n';
      
      if (isPromoter) {
        thankYouMessage += '😊 Ficamos muito felizes que você recomendaria nossos serviços!';
      } else if (isNeutral) {
        thankYouMessage += '👍 Obrigado pelo feedback! Vamos trabalhar para melhorar ainda mais.';
      } else {
        thankYouMessage += '😔 Agradecemos seu feedback. Nossa equipe entrará em contato para resolver suas preocupações.';
      }

      await this.sendTextMessage(instanceName, number, thankYouMessage);

      // Se for detrator, enviar para n8n
      if (isDetractor && process.env.N8N_WEBHOOK_URL) {
        try {
          await axios.post(`${process.env.N8N_WEBHOOK_URL}${process.env.N8N_DETRACTOR_WEBHOOK}`, {
            responseId: response.id,
            surveyId,
            phone: number,
            score,
            source: 'whatsapp'
          });
          logger.info('Detrator enviado para n8n:', { responseId: response.id });
        } catch (error) {
          logger.error('Erro ao enviar detrator para n8n:', error);
        }
      }

      logger.info('Resposta de pesquisa processada:', { 
        responseId: response.id, 
        score, 
        isDetractor 
      });

      return response;

    } catch (error) {
      logger.error('Erro ao processar resposta de pesquisa:', error);
      await this.sendTextMessage(instanceName, number, 
        'Desculpe, ocorreu um erro ao processar sua resposta. Tente novamente mais tarde.');
      throw error;
    }
  }

  // Formatar número de telefone
  formatPhoneNumber(number) {
    // Remove todos os caracteres não numéricos
    const cleaned = number.replace(/\D/g, '');
    
    // Se não começar com código do país, adicionar 55 (Brasil)
    if (!cleaned.startsWith('55') && cleaned.length >= 10) {
      return `55${cleaned}`;
    }
    
    return cleaned;
  }

  // Listar instâncias
  async listInstances() {
    try {
      const response = await axios.get(`${this.apiUrl}/instance/fetchInstances`, {
        headers: this.getHeaders()
      });

      return response.data;
    } catch (error) {
      logger.error('Erro ao listar instâncias:', error);
      throw new Error(`Erro ao listar instâncias: ${error.message}`);
    }
  }

  // Deletar instância
  async deleteInstance(instanceName) {
    try {
      const response = await axios.delete(`${this.apiUrl}/instance/delete/${instanceName}`, {
        headers: this.getHeaders(instanceName)
      });

      // Remover do banco de dados
      await database.getClient().whatsAppInstance.deleteMany({
        where: { instanceName }
      });

      logger.info('Instância WhatsApp deletada:', { instanceName });
      return response.data;

    } catch (error) {
      logger.error('Erro ao deletar instância:', error);
      throw new Error(`Erro ao deletar instância: ${error.message}`);
    }
  }
}

module.exports = new WhatsAppService();
