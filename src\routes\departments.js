const express = require('express');
const { validate, departmentSchemas } = require('../utils/validation');
const { authenticate, authorize } = require('../middleware/auth');
const {
  getDepartments,
  getDepartment,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  testContentAnalysis,
  getDepartmentStats,
  suggestKeywords
} = require('../controllers/departmentController');

const router = express.Router();

// Todas as rotas de departamentos requerem autenticação
router.use(authenticate);

// Testar análise de conteúdo (rota pública para testes)
router.post('/test-analysis', 
  validate(require('joi').object({
    text: require('joi').string().min(1).max(2000).required()
  })),
  testContentAnalysis
);

// Obter estatísticas de direcionamento
router.get('/stats', getDepartmentStats);

// Sugerir palavras-chave
router.get('/suggest-keywords', suggestKeywords);

// Listar departamentos
router.get('/', getDepartments);

// Obter departamento específico
router.get('/:id', getDepartment);

// Criar departamento (apenas admins)
router.post('/', 
  authorize('ADMIN'),
  validate(departmentSchemas.create),
  createDepartment
);

// Atualizar departamento (apenas admins)
router.put('/:id', 
  authorize('ADMIN'),
  validate(departmentSchemas.update),
  updateDepartment
);

// Deletar departamento (apenas admins)
router.delete('/:id', 
  authorize('ADMIN'),
  deleteDepartment
);

// Ativar/desativar departamento
router.patch('/:id/toggle-status', 
  authorize('ADMIN'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const database = require('../config/database');
      const { logger } = require('../utils/logger');

      // Buscar departamento
      const department = await database.getClient().department.findUnique({
        where: { id }
      });

      if (!department) {
        return res.status(404).json({
          error: 'Departamento não encontrado',
          message: 'O departamento solicitado não foi encontrado'
        });
      }

      // Alternar status
      const updatedDepartment = await database.getClient().department.update({
        where: { id },
        data: { isActive: !department.isActive }
      });

      logger.info('Status do departamento alterado:', { 
        departmentId: id, 
        newStatus: updatedDepartment.isActive 
      });

      res.json({
        message: `Departamento ${updatedDepartment.isActive ? 'ativado' : 'desativado'} com sucesso`,
        department: updatedDepartment
      });

    } catch (error) {
      logger.error('Erro ao alterar status do departamento:', error);
      res.status(500).json({
        error: 'Erro interno',
        message: 'Não foi possível alterar o status do departamento'
      });
    }
  }
);

// Adicionar palavra-chave ao departamento
router.post('/:id/keywords', 
  authorize('ADMIN'),
  validate(require('joi').object({
    keyword: require('joi').string().min(2).max(50).required()
  })),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { keyword } = req.body;
      const database = require('../config/database');
      const { logger } = require('../utils/logger');

      // Buscar departamento
      const department = await database.getClient().department.findUnique({
        where: { id }
      });

      if (!department) {
        return res.status(404).json({
          error: 'Departamento não encontrado',
          message: 'O departamento solicitado não foi encontrado'
        });
      }

      // Verificar se a palavra-chave já existe
      const normalizedKeyword = keyword.toLowerCase().trim();
      const existingKeywords = department.keywords.map(k => k.toLowerCase());

      if (existingKeywords.includes(normalizedKeyword)) {
        return res.status(409).json({
          error: 'Palavra-chave já existe',
          message: 'Esta palavra-chave já está cadastrada para este departamento'
        });
      }

      // Adicionar palavra-chave
      const updatedKeywords = [...department.keywords, keyword.trim()];
      
      const updatedDepartment = await database.getClient().department.update({
        where: { id },
        data: { keywords: updatedKeywords }
      });

      logger.info('Palavra-chave adicionada:', { 
        departmentId: id, 
        keyword: keyword.trim() 
      });

      res.json({
        message: 'Palavra-chave adicionada com sucesso',
        department: updatedDepartment
      });

    } catch (error) {
      logger.error('Erro ao adicionar palavra-chave:', error);
      res.status(500).json({
        error: 'Erro interno',
        message: 'Não foi possível adicionar a palavra-chave'
      });
    }
  }
);

// Remover palavra-chave do departamento
router.delete('/:id/keywords/:keyword', 
  authorize('ADMIN'),
  async (req, res) => {
    try {
      const { id, keyword } = req.params;
      const database = require('../config/database');
      const { logger } = require('../utils/logger');

      // Buscar departamento
      const department = await database.getClient().department.findUnique({
        where: { id }
      });

      if (!department) {
        return res.status(404).json({
          error: 'Departamento não encontrado',
          message: 'O departamento solicitado não foi encontrado'
        });
      }

      // Remover palavra-chave
      const decodedKeyword = decodeURIComponent(keyword);
      const updatedKeywords = department.keywords.filter(k => k !== decodedKeyword);

      if (updatedKeywords.length === department.keywords.length) {
        return res.status(404).json({
          error: 'Palavra-chave não encontrada',
          message: 'A palavra-chave especificada não foi encontrada'
        });
      }

      const updatedDepartment = await database.getClient().department.update({
        where: { id },
        data: { keywords: updatedKeywords }
      });

      logger.info('Palavra-chave removida:', { 
        departmentId: id, 
        keyword: decodedKeyword 
      });

      res.json({
        message: 'Palavra-chave removida com sucesso',
        department: updatedDepartment
      });

    } catch (error) {
      logger.error('Erro ao remover palavra-chave:', error);
      res.status(500).json({
        error: 'Erro interno',
        message: 'Não foi possível remover a palavra-chave'
      });
    }
  }
);

// Analisar texto e retornar departamento sugerido
router.post('/analyze-text', 
  validate(require('joi').object({
    text: require('joi').string().min(1).max(2000).required(),
    includeDetails: require('joi').boolean().default(false)
  })),
  async (req, res) => {
    try {
      const { text, includeDetails } = req.body;
      const contentAnalysisService = require('../services/contentAnalysisService');

      const analysis = includeDetails 
        ? await contentAnalysisService.analyzeFullContent(text)
        : await contentAnalysisService.analyzeContent(text);

      res.json({
        message: 'Análise realizada com sucesso',
        text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        analysis
      });

    } catch (error) {
      logger.error('Erro ao analisar texto:', error);
      res.status(500).json({
        error: 'Erro interno',
        message: 'Não foi possível analisar o texto'
      });
    }
  }
);

module.exports = router;
