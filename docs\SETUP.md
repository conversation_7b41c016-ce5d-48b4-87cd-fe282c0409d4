# Guia de Configuração - UNIFORMS2 Survey System

Este guia irá te ajudar a configurar e executar o sistema de pesquisas de satisfação com integração WhatsApp.

## 📋 Pré-requisitos

- **Node.js** 18+ 
- **PostgreSQL** 13+
- **Redis** 6+ (opcional, para cache e filas)
- **Evolution API** ou **WAHA** configurado
- **n8n** (opcional, para automação avançada)

## 🚀 Instalação Rápida

### 1. Clone e instale dependências

```bash
git clone <repository-url>
cd uniforms-survey-system
npm install
```

### 2. Configure as variáveis de ambiente

```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:

```env
# Servidor
PORT=3000
NODE_ENV=development

# Banco de Dados PostgreSQL
DATABASE_URL="postgresql://username:password@localhost:5432/uniforms_survey?schema=public"

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# WhatsApp API (Evolution API)
WHATSAPP_API_URL=http://localhost:8080
WHATSAPP_API_KEY=your-api-key
WHATSAPP_INSTANCE_NAME=uniforms-instance

# n8n Webhooks
N8N_WEBHOOK_URL=https://your-n8n-url.com
N8N_DETRACTOR_WEBHOOK=/webhook/detractor
N8N_DEPARTMENT_WEBHOOK=/webhook/department

# URLs
FRONTEND_URL=http://localhost:3001
BACKEND_URL=http://localhost:3000
```

### 3. Configure o banco de dados

```bash
# Executar migrações e seed
npm run setup
```

### 4. Inicie o servidor

```bash
# Desenvolvimento
npm run dev

# Produção
npm start
```

## 🔧 Configuração Detalhada

### PostgreSQL

1. Instale o PostgreSQL
2. Crie um banco de dados:
```sql
CREATE DATABASE uniforms_survey;
CREATE USER uniforms_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE uniforms_survey TO uniforms_user;
```

3. Atualize a `DATABASE_URL` no `.env`

### Evolution API / WAHA

#### Evolution API

1. Clone e configure a Evolution API:
```bash
git clone https://github.com/EvolutionAPI/evolution-api.git
cd evolution-api
npm install
```

2. Configure o `.env` da Evolution API:
```env
SERVER_URL=http://localhost:8080
AUTHENTICATION_API_KEY=your-api-key
```

3. Inicie a Evolution API:
```bash
npm run start:prod
```

#### WAHA (Alternativa)

1. Execute via Docker:
```bash
docker run -it --rm -p 3000:3000/tcp devlikeapro/waha
```

### Redis (Opcional)

Para melhor performance com cache e filas:

```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS
brew install redis

# Iniciar Redis
redis-server
```

### n8n (Opcional)

Para automação avançada:

```bash
# Via Docker
docker run -it --rm --name n8n -p 5678:5678 n8nio/n8n

# Via npm
npm install n8n -g
n8n start
```

## 📱 Configuração do WhatsApp

### 1. Criar Instância

Acesse: `POST /api/whatsapp/instances`

```json
{
  "instanceName": "uniforms-main",
  "webhookUrl": "http://your-backend-url:3000/webhooks/whatsapp"
}
```

### 2. Conectar WhatsApp

1. Acesse: `GET /api/whatsapp/instances/uniforms-main/qrcode`
2. Escaneie o QR Code com seu WhatsApp
3. Verifique o status: `GET /api/whatsapp/instances/uniforms-main/status`

## 🔗 Configuração do n8n

### Webhooks para Detratores

Crie um workflow no n8n que:

1. **Recebe webhook** de detratores: `http://your-backend:3000/webhooks/n8n/detractor`
2. **Processa** a resposta (ex: enviar email, criar ticket)
3. **Notifica** o sistema: `POST http://your-backend:3000/webhooks/n8n/detractor-processed`

Exemplo de payload de retorno:
```json
{
  "responseId": "uuid-da-resposta",
  "status": "processed",
  "agentId": "agent-a-id",
  "notes": "Cliente contatado via telefone",
  "contactAttempts": 1
}
```

### Webhooks para Direcionamento

Para direcionamento automático por departamento:

1. **Recebe webhook**: `http://your-backend:3000/webhooks/n8n/department`
2. **Analisa** o conteúdo da mensagem
3. **Direciona** para o departamento correto
4. **Notifica**: `POST http://your-backend:3000/webhooks/n8n/department-assigned`

Exemplo de payload:
```json
{
  "conversationId": "uuid-da-conversa",
  "department": "Infraestrutura",
  "reason": "Palavra-chave 'falta de água' identificada",
  "assignedAgent": "agent-infra-id",
  "priority": "HIGH"
}
```

## 🧪 Testando o Sistema

### 1. Criar usuário admin

```bash
# O seed já cria um usuário admin
# Email: <EMAIL>
# Senha: admin123
```

### 2. Testar API

```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Criar pesquisa
curl -X POST http://localhost:3000/api/surveys \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Pesquisa de Teste",
    "questions": [
      {
        "id": 1,
        "type": "nps",
        "question": "De 0 a 10, o quanto você recomendaria nossos serviços?",
        "required": true
      }
    ]
  }'
```

### 3. Testar formulário HTML

Acesse: `http://localhost:3000/survey/SURVEY_ID`

### 4. Testar análise de conteúdo

```bash
curl -X POST http://localhost:3000/api/departments/test-analysis \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"text":"Estou com problema de falta de água há 3 dias"}'
```

## 📊 Endpoints Principais

### Autenticação
- `POST /api/auth/login` - Login
- `POST /api/auth/register` - Registro
- `GET /api/auth/profile` - Perfil do usuário

### Pesquisas
- `GET /api/surveys` - Listar pesquisas
- `POST /api/surveys` - Criar pesquisa
- `GET /api/surveys/:id/stats` - Estatísticas da pesquisa

### WhatsApp
- `GET /api/whatsapp/instances` - Listar instâncias
- `POST /api/whatsapp/send-survey` - Enviar pesquisa
- `POST /api/whatsapp/send-bulk-survey` - Envio em lote

### Conversas
- `GET /api/conversations` - Listar conversas
- `GET /api/conversations/:id` - Obter conversa
- `POST /api/conversations/:id/messages` - Enviar mensagem

### Departamentos
- `GET /api/departments` - Listar departamentos
- `POST /api/departments/analyze-text` - Analisar texto

## 🔍 Monitoramento

### Logs
- Logs da aplicação: `./logs/app.log`
- Logs de erro: `./logs/error.log`

### Health Check
- `GET /health` - Status do sistema

### Métricas
- `GET /api/responses/analytics` - Métricas NPS
- `GET /api/conversations/stats` - Estatísticas de conversas

## 🚨 Solução de Problemas

### Erro de conexão com PostgreSQL
1. Verifique se o PostgreSQL está rodando
2. Confirme as credenciais no `.env`
3. Execute: `npm run prisma:migrate`

### WhatsApp não conecta
1. Verifique se a Evolution API está rodando
2. Confirme a `WHATSAPP_API_URL`
3. Gere um novo QR Code

### Webhooks não funcionam
1. Verifique se as URLs estão acessíveis
2. Confirme as configurações do n8n
3. Verifique os logs para erros

## 📚 Próximos Passos

1. **Frontend React**: Implementar interface de usuário
2. **Testes**: Adicionar testes unitários e de integração
3. **Docker**: Containerizar a aplicação
4. **CI/CD**: Configurar pipeline de deploy
5. **Monitoramento**: Adicionar métricas avançadas

## 🆘 Suporte

Para suporte técnico:
1. Verifique os logs em `./logs/`
2. Consulte a documentação da API
3. Abra uma issue no repositório

---

**UNIFORMS2** - Sistema SaaS de Pesquisas de Satisfação 🚀
