import React from 'react';

const Badge = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  ...props
}) => {
  const variants = {
    primary: 'badge-primary',
    success: 'badge-success',
    warning: 'badge-warning',
    danger: 'badge-danger',
    gray: 'badge-gray',
    info: 'badge-primary'
  };

  const sizes = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };

  return (
    <span
      className={`badge ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
    </span>
  );
};

// Status Badge Component
export const StatusBadge = ({ status, className = '' }) => {
  const statusConfig = {
    active: { variant: 'success', text: 'Ativo' },
    inactive: { variant: 'gray', text: 'Inativo' },
    pending: { variant: 'warning', text: 'Pendente' },
    completed: { variant: 'success', text: 'Concluído' },
    cancelled: { variant: 'danger', text: 'Cancelado' },
    draft: { variant: 'gray', text: 'Rascunho' },
    published: { variant: 'success', text: 'Publicado' },
    archived: { variant: 'gray', text: 'Arquivado' }
  };

  const config = statusConfig[status] || { variant: 'gray', text: status };

  return (
    <Badge variant={config.variant} className={className}>
      {config.text}
    </Badge>
  );
};

// Priority Badge Component
export const PriorityBadge = ({ priority, className = '' }) => {
  const priorityConfig = {
    low: { variant: 'gray', text: 'Baixa' },
    medium: { variant: 'warning', text: 'Média' },
    high: { variant: 'danger', text: 'Alta' },
    urgent: { variant: 'danger', text: 'Urgente' }
  };

  const config = priorityConfig[priority] || { variant: 'gray', text: priority };

  return (
    <Badge variant={config.variant} className={className}>
      {config.text}
    </Badge>
  );
};

// NPS Badge Component
export const NPSBadge = ({ type, className = '' }) => {
  const npsConfig = {
    promoter: { variant: 'success', text: 'Promotor' },
    neutral: { variant: 'warning', text: 'Neutro' },
    detractor: { variant: 'danger', text: 'Detrator' }
  };

  const config = npsConfig[type] || { variant: 'gray', text: type };

  return (
    <Badge variant={config.variant} className={className}>
      {config.text}
    </Badge>
  );
};

export default Badge;
