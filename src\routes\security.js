const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');
const { validate } = require('../utils/validation');
const { 
  authRateLimit, 
  strictRateLimit, 
  require2FA, 
  requirePermission,
  auditLogger,
  customRateLimit
} = require('../middleware/security');
const Joi = require('joi');
const {
  setup2FA,
  verify2FA,
  disable2FA,
  verify2FASession,
  getSecurityStatus,
  changePassword,
  getSecurityLogs,
  revokeSessions,
  validatePassword,
  getSecurityReport,
  cleanupLogs,
  getSecurityConfig,
  testSuspiciousActivity
} = require('../controllers/securityController');

const router = express.Router();

// Todas as rotas requerem autenticação
router.use(authenticate);

// 2FA (Two-Factor Authentication)

// Configurar 2FA
router.post('/2fa/setup',
  strictRateLimit,
  auditLogger('setup', '2fa'),
  setup2FA
);

// Verificar e ativar 2FA
router.post('/2fa/verify',
  customRateLimit('2fa_verify', 5, 5 * 60 * 1000), // 5 tentativas por 5 min
  validate(Joi.object({
    token: Joi.string().length(6).pattern(/^\d+$/).required()
  })),
  auditLogger('verify', '2fa'),
  verify2FA
);

// Desativar 2FA
router.post('/2fa/disable',
  strictRateLimit,
  validate(Joi.object({
    password: Joi.string().required()
  })),
  auditLogger('disable', '2fa'),
  disable2FA
);

// Verificar 2FA para sessão atual
router.post('/2fa/verify-session',
  customRateLimit('2fa_session_verify', 3, 5 * 60 * 1000),
  validate(Joi.object({
    token: Joi.string().length(6).pattern(/^\d+$/).required()
  })),
  verify2FASession
);

// Gerenciamento de Senha

// Alterar senha
router.post('/password/change',
  strictRateLimit,
  validate(Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(8).required()
  })),
  auditLogger('change', 'password'),
  changePassword
);

// Validar força da senha
router.post('/password/validate',
  validate(Joi.object({
    password: Joi.string().required()
  })),
  validatePassword
);

// Sessões

// Obter status de segurança
router.get('/status', getSecurityStatus);

// Obter logs de segurança do usuário
router.get('/logs',
  validate(Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    event: Joi.string().optional()
  }).options({ allowUnknown: true })),
  getSecurityLogs
);

// Revogar sessões
router.post('/sessions/revoke',
  strictRateLimit,
  validate(Joi.object({
    revokeAll: Joi.boolean().default(false)
  })),
  auditLogger('revoke', 'sessions'),
  revokeSessions
);

// Rotas administrativas (requerem permissões especiais)

// Relatório de segurança
router.get('/report',
  requirePermission('security', 'read'),
  validate(Joi.object({
    period: Joi.string().valid('7d', '30d', '90d').default('30d')
  }).options({ allowUnknown: true })),
  getSecurityReport
);

// Limpar logs antigos
router.post('/logs/cleanup',
  requirePermission('security', 'delete'),
  strictRateLimit,
  validate(Joi.object({
    daysToKeep: Joi.number().integer().min(30).max(365).default(90)
  })),
  auditLogger('cleanup', 'security_logs'),
  cleanupLogs
);

// Obter configurações de segurança
router.get('/config',
  requirePermission('security', 'read'),
  getSecurityConfig
);

// Testar detecção de atividade suspeita (apenas para desenvolvimento)
router.post('/test/suspicious-activity',
  authorize('ADMIN'),
  validate(Joi.object({
    activity: Joi.object({
      action: Joi.string().required(),
      ip: Joi.string().ip().optional(),
      userAgent: Joi.string().optional(),
      metadata: Joi.object().optional()
    }).required()
  })),
  testSuspiciousActivity
);

// Rotas específicas para administradores

// Obter logs de segurança de todos os usuários
router.get('/admin/logs',
  requirePermission('security', 'read'),
  validate(Joi.object({
    userId: Joi.string().uuid().optional(),
    event: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(200).default(50),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional()
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const {
        userId,
        event,
        page,
        limit,
        startDate,
        endDate
      } = req.query;

      const database = require('../config/database');

      const whereClause = {
        ...(userId && { userId }),
        ...(event && { event }),
        ...(startDate && endDate && {
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate)
          }
        })
      };

      const [logs, total] = await Promise.all([
        database.getClient().securityLog.findMany({
          where: whereClause,
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip: (parseInt(page) - 1) * parseInt(limit),
          take: parseInt(limit)
        }),
        database.getClient().securityLog.count({ where: whereClause })
      ]);

      res.json({
        logs: logs.map(log => ({
          id: log.id,
          event: log.event,
          metadata: log.metadata,
          createdAt: log.createdAt,
          user: log.user
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      logger.error('Erro ao obter logs administrativos:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Obter estatísticas de segurança
router.get('/admin/stats',
  requirePermission('security', 'read'),
  validate(Joi.object({
    period: Joi.string().valid('7d', '30d', '90d').default('30d')
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const { period } = req.query;
      const database = require('../config/database');

      // Calcular período
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90 }[period] || 30;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      const [
        securityEvents,
        userStats,
        sessionStats,
        rateLimitStats
      ] = await Promise.all([
        database.getClient().securityLog.groupBy({
          by: ['event'],
          where: { createdAt: { gte: startDate } },
          _count: { id: true }
        }),
        database.getClient().user.groupBy({
          by: ['twoFactorEnabled'],
          _count: { id: true }
        }),
        database.getClient().sessionToken.groupBy({
          by: ['isActive'],
          _count: { id: true }
        }),
        database.getClient().rateLimitLog.groupBy({
          by: ['action'],
          where: { createdAt: { gte: startDate } },
          _count: { id: true }
        })
      ]);

      // Calcular estatísticas de eventos suspeitos
      const suspiciousEvents = securityEvents.filter(e => 
        ['suspicious_activity_detected', 'multiple_failed_logins', 'permission_denied'].includes(e.event)
      );

      res.json({
        period: { start: startDate, end: now, days },
        events: {
          total: securityEvents.reduce((sum, e) => sum + e._count.id, 0),
          byType: securityEvents.map(e => ({
            event: e.event,
            count: e._count.id
          })),
          suspicious: suspiciousEvents.reduce((sum, e) => sum + e._count.id, 0)
        },
        users: {
          total: userStats.reduce((sum, s) => sum + s._count.id, 0),
          with2FA: userStats.find(s => s.twoFactorEnabled)?._count.id || 0,
          without2FA: userStats.find(s => !s.twoFactorEnabled)?._count.id || 0
        },
        sessions: {
          active: sessionStats.find(s => s.isActive)?._count.id || 0,
          inactive: sessionStats.find(s => !s.isActive)?._count.id || 0
        },
        rateLimiting: rateLimitStats.map(r => ({
          action: r.action,
          violations: r._count.id
        }))
      });
    } catch (error) {
      logger.error('Erro ao obter estatísticas de segurança:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Forçar logout de usuário específico
router.post('/admin/force-logout/:userId',
  requirePermission('security', 'update'),
  strictRateLimit,
  validate(Joi.object({
    userId: Joi.string().uuid().required()
  })),
  auditLogger('force_logout', 'user_session'),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const database = require('../config/database');

      // Verificar se usuário existe
      const user = await database.getClient().user.findUnique({
        where: { id: userId },
        select: { id: true, name: true, email: true }
      });

      if (!user) {
        return res.status(404).json({
          error: 'Usuário não encontrado',
          message: 'O usuário especificado não existe'
        });
      }

      // Revogar todas as sessões do usuário
      const result = await database.getClient().sessionToken.updateMany({
        where: {
          userId,
          isActive: true
        },
        data: {
          isActive: false,
          revokedAt: new Date()
        }
      });

      // Log do evento
      const securityService = require('../services/securityService');
      await securityService.logSecurityEvent(userId, 'forced_logout', {
        adminId: req.user.id,
        adminName: req.user.name,
        sessionsRevoked: result.count,
        timestamp: new Date()
      });

      res.json({
        message: `Logout forçado executado para ${user.name}`,
        user: {
          id: user.id,
          name: user.name,
          email: user.email
        },
        sessionsRevoked: result.count
      });
    } catch (error) {
      logger.error('Erro no logout forçado:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Bloquear/desbloquear usuário
router.post('/admin/block-user/:userId',
  requirePermission('security', 'update'),
  strictRateLimit,
  validate(Joi.object({
    userId: Joi.string().uuid().required(),
    blocked: Joi.boolean().required(),
    reason: Joi.string().optional()
  })),
  auditLogger('block_user', 'user'),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const { blocked, reason } = req.body;
      const database = require('../config/database');

      // Verificar se usuário existe
      const user = await database.getClient().user.findUnique({
        where: { id: userId },
        select: { id: true, name: true, email: true, isBlocked: true }
      });

      if (!user) {
        return res.status(404).json({
          error: 'Usuário não encontrado',
          message: 'O usuário especificado não existe'
        });
      }

      // Atualizar status
      await database.getClient().user.update({
        where: { id: userId },
        data: {
          isBlocked: blocked,
          blockedAt: blocked ? new Date() : null,
          blockedReason: blocked ? reason : null
        }
      });

      // Se bloqueando, revogar sessões
      if (blocked) {
        await database.getClient().sessionToken.updateMany({
          where: {
            userId,
            isActive: true
          },
          data: {
            isActive: false,
            revokedAt: new Date()
          }
        });
      }

      // Log do evento
      const securityService = require('../services/securityService');
      await securityService.logSecurityEvent(userId, blocked ? 'user_blocked' : 'user_unblocked', {
        adminId: req.user.id,
        adminName: req.user.name,
        reason,
        timestamp: new Date()
      });

      res.json({
        message: `Usuário ${blocked ? 'bloqueado' : 'desbloqueado'} com sucesso`,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          blocked
        },
        reason
      });
    } catch (error) {
      logger.error('Erro ao bloquear/desbloquear usuário:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

module.exports = router;
