import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '../services/api';
import { toast } from 'react-hot-toast';

export function useNotifications() {
  const queryClient = useQueryClient();
  const [pushSupported, setPushSupported] = useState(false);
  const [pushPermission, setPushPermission] = useState('default');
  const [pushSubscription, setPushSubscription] = useState(null);

  // Verificar suporte a push notifications
  useEffect(() => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      setPushSupported(true);
      setPushPermission(Notification.permission);
    }
  }, []);

  // Buscar notificações
  const {
    data: notifications,
    isLoading: notificationsLoading,
    error: notificationsError,
    refetch: refetchNotifications
  } = useQuery(
    'notifications',
    () => api.get('/notifications').then(res => res.data),
    {
      refetchInterval: 30000, // Atualizar a cada 30 segundos
      staleTime: 10000
    }
  );

  // Buscar contagem de não lidas
  const {
    data: unreadCount,
    refetch: refetchUnreadCount
  } = useQuery(
    'notifications-unread-count',
    () => api.get('/notifications/unread-count').then(res => res.data.unreadCount),
    {
      refetchInterval: 15000, // Atualizar a cada 15 segundos
      staleTime: 5000
    }
  );

  // Buscar configurações
  const {
    data: settings,
    isLoading: settingsLoading
  } = useQuery(
    'notification-settings',
    () => api.get('/notifications/settings').then(res => res.data)
  );

  // Marcar como lida
  const markAsReadMutation = useMutation(
    (notificationId) => api.patch(`/notifications/${notificationId}/read`),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications');
        queryClient.invalidateQueries('notifications-unread-count');
      },
      onError: (error) => {
        toast.error('Erro ao marcar notificação como lida');
        console.error('Erro ao marcar como lida:', error);
      }
    }
  );

  // Marcar todas como lidas
  const markAllAsReadMutation = useMutation(
    () => api.patch('/notifications/read-all'),
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries('notifications');
        queryClient.invalidateQueries('notifications-unread-count');
        toast.success(`${data.data.count} notificações marcadas como lidas`);
      },
      onError: (error) => {
        toast.error('Erro ao marcar todas as notificações como lidas');
        console.error('Erro ao marcar todas como lidas:', error);
      }
    }
  );

  // Atualizar configurações
  const updateSettingsMutation = useMutation(
    (newSettings) => api.put('/notifications/settings', newSettings),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notification-settings');
        toast.success('Configurações atualizadas com sucesso');
      },
      onError: (error) => {
        toast.error('Erro ao atualizar configurações');
        console.error('Erro ao atualizar configurações:', error);
      }
    }
  );

  // Solicitar permissão para notificações
  const requestNotificationPermission = useCallback(async () => {
    if (!pushSupported) {
      toast.error('Notificações push não são suportadas neste navegador');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      setPushPermission(permission);

      if (permission === 'granted') {
        toast.success('Permissão para notificações concedida!');
        return true;
      } else if (permission === 'denied') {
        toast.error('Permissão para notificações negada');
        return false;
      } else {
        toast.warning('Permissão para notificações não foi concedida');
        return false;
      }
    } catch (error) {
      console.error('Erro ao solicitar permissão:', error);
      toast.error('Erro ao solicitar permissão para notificações');
      return false;
    }
  }, [pushSupported]);

  // Registrar service worker e subscription
  const registerPushSubscription = useCallback(async () => {
    if (!pushSupported || pushPermission !== 'granted') {
      return false;
    }

    try {
      // Registrar service worker
      const registration = await navigator.serviceWorker.register('/sw.js');
      await navigator.serviceWorker.ready;

      // Obter chave VAPID pública
      const vapidResponse = await api.get('/notifications/vapid-public-key');
      const publicKey = vapidResponse.data.publicKey;

      // Criar subscription
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(publicKey)
      });

      // Enviar subscription para o servidor
      await api.post('/notifications/push-subscription', {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: arrayBufferToBase64(subscription.getKey('p256dh')),
          auth: arrayBufferToBase64(subscription.getKey('auth'))
        }
      });

      setPushSubscription(subscription);
      toast.success('Notificações push ativadas com sucesso!');
      return true;
    } catch (error) {
      console.error('Erro ao registrar push subscription:', error);
      toast.error('Erro ao ativar notificações push');
      return false;
    }
  }, [pushSupported, pushPermission]);

  // Desregistrar subscription
  const unregisterPushSubscription = useCallback(async () => {
    if (!pushSubscription) {
      return;
    }

    try {
      // Desregistrar no servidor
      await api.delete('/notifications/push-subscription', {
        data: { endpoint: pushSubscription.endpoint }
      });

      // Desregistrar no navegador
      await pushSubscription.unsubscribe();
      setPushSubscription(null);
      
      toast.success('Notificações push desativadas');
    } catch (error) {
      console.error('Erro ao desregistrar push subscription:', error);
      toast.error('Erro ao desativar notificações push');
    }
  }, [pushSubscription]);

  // Testar notificação push
  const testPushNotification = useCallback(async () => {
    try {
      await api.post('/notifications/test-push', {
        title: 'Teste de Notificação',
        message: 'Esta é uma notificação de teste do UNIFORMS2!'
      });
      toast.success('Notificação de teste enviada!');
    } catch (error) {
      console.error('Erro ao testar notificação:', error);
      toast.error('Erro ao enviar notificação de teste');
    }
  }, []);

  // Funções utilitárias
  const urlBase64ToUint8Array = (base64String) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  };

  const arrayBufferToBase64 = (buffer) => {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  };

  // Verificar subscription existente
  useEffect(() => {
    const checkExistingSubscription = async () => {
      if (!pushSupported) return;

      try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
          const subscription = await registration.pushManager.getSubscription();
          setPushSubscription(subscription);
        }
      } catch (error) {
        console.error('Erro ao verificar subscription existente:', error);
      }
    };

    checkExistingSubscription();
  }, [pushSupported]);

  // Atualizar contadores quando há novas notificações
  useEffect(() => {
    const handleNewNotification = () => {
      refetchNotifications();
      refetchUnreadCount();
    };

    window.addEventListener('newResponse', handleNewNotification);
    window.addEventListener('detractorAlert', handleNewNotification);
    window.addEventListener('newMessage', handleNewNotification);

    return () => {
      window.removeEventListener('newResponse', handleNewNotification);
      window.removeEventListener('detractorAlert', handleNewNotification);
      window.removeEventListener('newMessage', handleNewNotification);
    };
  }, [refetchNotifications, refetchUnreadCount]);

  return {
    // Dados
    notifications: notifications?.notifications || [],
    unreadCount: unreadCount || 0,
    settings,
    
    // Estados
    notificationsLoading,
    settingsLoading,
    notificationsError,
    
    // Push notifications
    pushSupported,
    pushPermission,
    pushSubscription: !!pushSubscription,
    
    // Ações
    markAsRead: markAsReadMutation.mutate,
    markAllAsRead: markAllAsReadMutation.mutate,
    updateSettings: updateSettingsMutation.mutate,
    requestNotificationPermission,
    registerPushSubscription,
    unregisterPushSubscription,
    testPushNotification,
    
    // Refetch
    refetchNotifications,
    refetchUnreadCount
  };
}
