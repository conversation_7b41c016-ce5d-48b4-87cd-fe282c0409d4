// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  password  String
  name      String
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 2FA
  twoFactorEnabled    Boolean   @default(false)
  twoFactorSecret     String?
  tempTwoFactorSecret String?
  twoFactorEnabledAt  DateTime?

  // Criptografia
  encryptionSalt      String?
  keyDerivationIter   Int?

  // Relacionamentos
  surveys              Survey[]
  notifications        Notification[]
  pushSubscriptions    PushSubscription[]
  notificationSettings NotificationSettings?
  securityLogs         SecurityLog[]

  @@map("users")
}

model Survey {
  id          String   @id @default(uuid())
  title       String
  description String?
  questions   Json     // Array de perguntas em JSON
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  responses   Response[]
  
  @@map("surveys")
}

model Response {
  id          String   @id @default(uuid())
  userPhone   String
  score       Int?     // NPS Score (0-10)
  feedback    String?
  isDetractor Boolean  @default(false)
  isPromoter  Boolean  @default(false)
  isNeutral   Boolean  @default(false)
  createdAt   DateTime @default(now())
  
  surveyId    String
  survey      Survey   @relation(fields: [surveyId], references: [id], onDelete: Cascade)
  
  conversations Conversation[]
  
  @@map("responses")
}

model WhatsAppInstance {
  id           String   @id @default(uuid())
  instanceName String   @unique
  apiUrl       String
  apiKey       String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  conversations Conversation[]
  
  @@map("whatsapp_instances")
}

model Conversation {
  id          String   @id @default(uuid())
  userPhone   String
  messages    Json     // Array de mensagens em JSON
  department  String?  // Departamento responsável
  priority    Priority @default(NORMAL)
  status      ConversationStatus @default(OPEN)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  instanceId  String
  instance    WhatsAppInstance @relation(fields: [instanceId], references: [id], onDelete: Cascade)
  
  responseId  String?
  response    Response? @relation(fields: [responseId], references: [id], onDelete: SetNull)
  
  @@map("conversations")
}

model Department {
  id          String   @id @default(uuid())
  name        String   @unique
  keywords    String[] // Palavras-chave para direcionamento automático
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("departments")
}

enum Role {
  USER
  ADMIN
  AGENT
}

enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum ConversationStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  type      String   // 'new_response', 'detractor_alert', 'new_message', etc.
  title     String
  message   String
  data      Json?    // Dados adicionais da notificação
  priority  String   @default("normal") // 'low', 'normal', 'high', 'urgent'
  isRead    Boolean  @default(false)
  readAt    DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRead])
  @@index([createdAt])
  @@map("notifications")
}

model PushSubscription {
  id        String   @id @default(uuid())
  userId    String
  endpoint  String
  p256dh    String
  auth      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, endpoint])
  @@map("push_subscriptions")
}

model NotificationSettings {
  id                     String   @id @default(uuid())
  userId                 String   @unique
  emailNotifications     Boolean  @default(true)
  pushNotifications      Boolean  @default(true)
  websocketNotifications Boolean  @default(true)
  detractorAlerts        Boolean  @default(true)
  newResponseAlerts      Boolean  @default(true)
  newMessageAlerts       Boolean  @default(true)
  surveyCompletedAlerts  Boolean  @default(false)
  quietHoursEnabled      Boolean  @default(false)
  quietHoursStart        String   @default("22:00")
  quietHoursEnd          String   @default("08:00")
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_settings")
}

model ScheduledTask {
  id          String   @id @default(uuid())
  type        String   // 'followup', 'survey', 'message'
  data        Json     // Dados do evento original
  templateName String?
  priority    String   @default("normal")
  executeAt   DateTime
  status      String   @default("scheduled") // 'scheduled', 'executed', 'error'
  executedAt  DateTime?
  error       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([status, executeAt])
  @@map("scheduled_tasks")
}

model AutomationLog {
  id         String   @id @default(uuid())
  ruleName   String
  actionType String
  eventData  Json
  executedAt DateTime @default(now())

  @@index([ruleName, executedAt])
  @@map("automation_logs")
}

model ChatbotTraining {
  id            String   @id @default(uuid())
  message       String
  correctIntent String
  userFeedback  String?
  createdAt     DateTime @default(now())

  @@map("chatbot_training")
}

model ResponseTemplate {
  id          String   @id @default(uuid())
  name        String   @unique
  subject     String?
  message     String
  type        String   @default("whatsapp") // 'whatsapp', 'email', 'sms'
  variables   String[] // Variáveis disponíveis no template
  isActive    Boolean  @default(true)
  createdBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("response_templates")
}

model SecurityLog {
  id        String   @id @default(uuid())
  userId    String?
  event     String   // 'login_failed', 'suspicious_activity_detected', etc.
  metadata  Json?    // Dados adicionais do evento
  createdAt DateTime @default(now())

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId, event])
  @@index([createdAt])
  @@map("security_logs")
}

model RateLimitLog {
  id         String   @id @default(uuid())
  identifier String   // IP, userId, etc.
  action     String   // 'login', 'api_call', etc.
  createdAt  DateTime @default(now())

  @@index([identifier, action, createdAt])
  @@map("rate_limit_logs")
}

model Permission {
  id       String @id @default(uuid())
  resource String // 'surveys', 'users', 'analytics', etc.
  action   String // 'create', 'read', 'update', 'delete'

  roles RolePermission[]

  @@unique([resource, action])
  @@map("permissions")
}

model RolePermission {
  id           String @id @default(uuid())
  roleId       String
  permissionId String

  role       UserRole   @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserRole {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  permissions RolePermission[]

  @@map("user_roles")
}

model AuditLog {
  id         String   @id @default(uuid())
  userId     String?
  action     String   // 'create', 'update', 'delete'
  resource   String   // 'user', 'survey', 'response'
  resourceId String?
  oldValues  Json?
  newValues  Json?
  metadata   Json?    // IP, user agent, etc.
  createdAt  DateTime @default(now())

  @@index([userId, action])
  @@index([resource, resourceId])
  @@index([createdAt])
  @@map("audit_logs")
}

model SessionToken {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  isActive  Boolean  @default(true)
  metadata  Json?    // IP, user agent, etc.
  createdAt DateTime @default(now())
  revokedAt DateTime?

  @@index([userId, isActive])
  @@index([token])
  @@index([expiresAt])
  @@map("session_tokens")
}

model GamificationProfile {
  id                    String   @id @default(uuid())
  userPhone             String   @unique
  totalPoints           Int      @default(0)
  currentLevel          Int      @default(1)
  totalResponses        Int      @default(0)
  currentStreak         Int      @default(0)
  maxStreak             Int      @default(0)
  improvementsGenerated Int      @default(0)
  lastResponseAt        DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  badges       UserBadge[]
  achievements UserAchievement[]

  @@index([totalPoints])
  @@index([currentLevel])
  @@map("gamification_profiles")
}

model UserBadge {
  id        String   @id @default(uuid())
  userPhone String
  badgeId   String
  earnedAt  DateTime @default(now())

  profile GamificationProfile @relation(fields: [userPhone], references: [userPhone], onDelete: Cascade)

  @@unique([userPhone, badgeId])
  @@map("user_badges")
}

model UserAchievement {
  id            String   @id @default(uuid())
  userPhone     String
  achievementId String
  level         Int
  earnedAt      DateTime @default(now())

  profile GamificationProfile @relation(fields: [userPhone], references: [userPhone], onDelete: Cascade)

  @@unique([userPhone, achievementId])
  @@map("user_achievements")
}

model Reward {
  id          String   @id @default(uuid())
  name        String
  description String
  type        String   // 'discount', 'gift', 'access', etc.
  value       String   // Valor do desconto, descrição do presente, etc.
  pointsCost  Int
  isActive    Boolean  @default(true)
  validUntil  DateTime?
  maxRedemptions Int?
  currentRedemptions Int @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  redemptions RewardRedemption[]

  @@map("rewards")
}

model RewardRedemption {
  id         String   @id @default(uuid())
  userPhone  String
  rewardId   String
  pointsUsed Int
  status     String   @default("pending") // 'pending', 'approved', 'delivered', 'cancelled'
  redeemedAt DateTime @default(now())
  deliveredAt DateTime?

  reward Reward @relation(fields: [rewardId], references: [id], onDelete: Cascade)

  @@map("reward_redemptions")
}

model Challenge {
  id          String   @id @default(uuid())
  name        String
  description String
  type        String   // 'daily', 'weekly', 'monthly', 'special'
  target      Int      // Meta a ser atingida
  reward      Int      // Pontos de recompensa
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  participations ChallengeParticipation[]

  @@map("challenges")
}

model ChallengeParticipation {
  id          String   @id @default(uuid())
  userPhone   String
  challengeId String
  progress    Int      @default(0)
  completed   Boolean  @default(false)
  completedAt DateTime?
  joinedAt    DateTime @default(now())

  challenge Challenge @relation(fields: [challengeId], references: [id], onDelete: Cascade)

  @@unique([userPhone, challengeId])
  @@map("challenge_participations")
}
