# Configurações do Servidor
PORT=3000
NODE_ENV=development

# Banco de Dados PostgreSQL
DATABASE_URL="postgresql://username:password@localhost:5432/uniforms_survey?schema=public"

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# WhatsApp API (Evolution API ou WAHA)
WHATSAPP_API_URL=http://localhost:8080
WHATSAPP_API_KEY=your-whatsapp-api-key
WHATSAPP_INSTANCE_NAME=uniforms-instance

# Redis (Cache e Filas)
REDIS_URL=redis://localhost:6379

# n8n Webhooks
N8N_WEBHOOK_URL=https://your-n8n-url.com
N8N_DETRACTOR_WEBHOOK=/webhook/detractor
N8N_DEPARTMENT_WEBHOOK=/webhook/department

# URLs do Sistema
FRONTEND_URL=http://localhost:3001
BACKEND_URL=http://localhost:3000

# Configurações de Email (opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Configurações de Upload
MAX_FILE_SIZE=5MB
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
