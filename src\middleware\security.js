const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const helmet = require('helmet');
const securityService = require('../services/securityService');
const { logger } = require('../utils/logger');

// Rate limiting avançado
const createRateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutos
    max = 100, // máximo de requests
    message = 'Muitas tentativas. Tente novamente mais tarde.',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    keyGenerator = (req) => req.ip,
    onLimitReached = null
  } = options;

  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests,
    skipFailedRequests,
    keyGenerator,
    handler: (req, res) => {
      logger.warn('Rate limit excedido:', {
        ip: req.ip,
        path: req.path,
        userAgent: req.get('User-Agent')
      });

      if (onLimitReached) {
        onLimitReached(req, res);
      }

      res.status(429).json({
        error: 'Rate limit excedido',
        message,
        retryAfter: Math.round(windowMs / 1000)
      });
    }
  });
};

// Rate limits específicos
const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // 5 tentativas de login
  message: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
  skipSuccessfulRequests: true,
  keyGenerator: (req) => `auth:${req.ip}:${req.body.email || 'unknown'}`,
  onLimitReached: async (req, res) => {
    await securityService.logSecurityEvent(null, 'auth_rate_limit_exceeded', {
      ip: req.ip,
      email: req.body.email,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });
  }
});

const apiRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000,
  max: 1000, // 1000 requests por 15 min
  message: 'Limite de API excedido. Tente novamente mais tarde.'
});

const strictRateLimit = createRateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutos
  max: 10, // 10 requests
  message: 'Ação sensível limitada. Aguarde 5 minutos.'
});

// Slow down para requests suspeitos
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000,
  delayAfter: 50, // Começar delay após 50 requests
  delayMs: 500, // Aumentar 500ms a cada request
  maxDelayMs: 20000, // Máximo 20 segundos de delay
  skipFailedRequests: false,
  skipSuccessfulRequests: false
});

// Middleware de segurança avançada
const advancedSecurity = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "wss:", "ws:"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  },
  crossOriginEmbedderPolicy: false, // Para compatibilidade
  hsts: {
    maxAge: 31536000, // 1 ano
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
});

// Middleware de detecção de atividade suspeita
const suspiciousActivityDetector = async (req, res, next) => {
  try {
    if (req.user) {
      const activity = {
        action: `${req.method} ${req.path}`,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date(),
        metadata: {
          path: req.path,
          method: req.method,
          query: req.query,
          body: req.method === 'POST' ? Object.keys(req.body) : undefined
        }
      };

      const detection = await securityService.detectSuspiciousActivity(req.user.id, activity);
      
      if (detection.suspicious && detection.action === 'block') {
        logger.warn('Atividade suspeita bloqueada:', {
          userId: req.user.id,
          indicators: detection.indicators,
          activity
        });

        return res.status(403).json({
          error: 'Atividade suspeita detectada',
          message: 'Sua conta foi temporariamente restrita por segurança.',
          contactSupport: true
        });
      }

      // Adicionar informações de segurança ao request
      req.security = {
        riskLevel: detection.riskLevel,
        suspicious: detection.suspicious,
        indicators: detection.indicators || []
      };
    }

    next();
  } catch (error) {
    logger.error('Erro no detector de atividade suspeita:', error);
    next(); // Continuar em caso de erro
  }
};

// Middleware de validação de entrada
const inputValidation = (schema) => {
  return (req, res, next) => {
    try {
      // Validar e sanitizar body
      if (req.body && Object.keys(req.body).length > 0) {
        req.body = securityService.validateAndSanitize(req.body, schema.body || {});
      }

      // Validar query parameters
      if (req.query && Object.keys(req.query).length > 0) {
        req.query = securityService.validateAndSanitize(req.query, schema.query || {});
      }

      // Validar params
      if (req.params && Object.keys(req.params).length > 0) {
        req.params = securityService.validateAndSanitize(req.params, schema.params || {});
      }

      next();
    } catch (error) {
      logger.warn('Validação de entrada falhou:', {
        error: error.message,
        path: req.path,
        ip: req.ip
      });

      res.status(400).json({
        error: 'Dados inválidos',
        message: error.message
      });
    }
  };
};

// Middleware de auditoria
const auditLogger = (action, resource) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    const originalJson = res.json;

    // Capturar dados antes da operação
    const oldValues = req.method === 'PUT' || req.method === 'PATCH' 
      ? await captureOldValues(resource, req.params.id)
      : null;

    // Override response methods para capturar resultado
    res.send = function(data) {
      logAuditEvent(req, res, action, resource, oldValues, data);
      return originalSend.call(this, data);
    };

    res.json = function(data) {
      logAuditEvent(req, res, action, resource, oldValues, data);
      return originalJson.call(this, data);
    };

    next();
  };
};

// Capturar valores antigos para auditoria
async function captureOldValues(resource, resourceId) {
  if (!resourceId) return null;

  try {
    const database = require('../config/database');
    
    switch (resource) {
      case 'user':
        return await database.getClient().user.findUnique({
          where: { id: resourceId },
          select: { id: true, name: true, email: true, role: true }
        });
      case 'survey':
        return await database.getClient().survey.findUnique({
          where: { id: resourceId },
          select: { id: true, title: true, isActive: true }
        });
      default:
        return null;
    }
  } catch (error) {
    logger.warn('Erro ao capturar valores antigos:', error);
    return null;
  }
}

// Log de evento de auditoria
async function logAuditEvent(req, res, action, resource, oldValues, responseData) {
  try {
    // Só logar se a operação foi bem-sucedida
    if (res.statusCode >= 400) return;

    const database = require('../config/database');
    
    let newValues = null;
    let resourceId = null;

    // Extrair novos valores da resposta
    if (responseData && typeof responseData === 'object') {
      const parsed = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;
      
      if (parsed.id) {
        resourceId = parsed.id;
        newValues = parsed;
      } else if (parsed.data && parsed.data.id) {
        resourceId = parsed.data.id;
        newValues = parsed.data;
      }
    }

    // Extrair ID dos parâmetros se não encontrado na resposta
    if (!resourceId && req.params.id) {
      resourceId = req.params.id;
    }

    await database.getClient().auditLog.create({
      data: {
        userId: req.user?.id,
        action,
        resource,
        resourceId,
        oldValues,
        newValues,
        metadata: {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.path,
          method: req.method,
          timestamp: new Date()
        }
      }
    });
  } catch (error) {
    logger.error('Erro ao registrar auditoria:', error);
  }
}

// Middleware de verificação de 2FA
const require2FA = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Não autenticado',
        message: 'Login necessário'
      });
    }

    // Verificar se usuário tem 2FA habilitado
    const user = await database.getClient().user.findUnique({
      where: { id: req.user.id },
      select: { twoFactorEnabled: true }
    });

    if (!user?.twoFactorEnabled) {
      return res.status(403).json({
        error: '2FA necessário',
        message: 'Esta ação requer autenticação de dois fatores',
        setup2FA: true
      });
    }

    // Verificar se 2FA foi validado na sessão atual
    if (!req.session?.twoFactorVerified) {
      return res.status(403).json({
        error: '2FA não verificado',
        message: 'Confirme sua identidade com o código 2FA',
        require2FAVerification: true
      });
    }

    next();
  } catch (error) {
    logger.error('Erro na verificação 2FA:', error);
    res.status(500).json({ error: 'Erro interno' });
  }
};

// Middleware de verificação de permissão RBAC
const requirePermission = (resource, action) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Não autenticado',
          message: 'Login necessário'
        });
      }

      const hasPermission = await securityService.checkPermission(req.user.id, resource, action);
      
      if (!hasPermission) {
        await securityService.logSecurityEvent(req.user.id, 'permission_denied', {
          resource,
          action,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date()
        });

        return res.status(403).json({
          error: 'Permissão negada',
          message: `Você não tem permissão para ${action} em ${resource}`
        });
      }

      next();
    } catch (error) {
      logger.error('Erro na verificação de permissão:', error);
      res.status(500).json({ error: 'Erro interno' });
    }
  };
};

// Middleware de rate limiting personalizado
const customRateLimit = (action, limit = 10, windowMs = 60000) => {
  return async (req, res, next) => {
    try {
      const identifier = req.user?.id || req.ip;
      const rateLimitResult = await securityService.checkRateLimit(identifier, action, limit, windowMs);

      if (!rateLimitResult.allowed) {
        return res.status(429).json({
          error: 'Rate limit excedido',
          message: `Muitas tentativas de ${action}. Tente novamente mais tarde.`,
          retryAfter: Math.round((rateLimitResult.resetTime - new Date()) / 1000)
        });
      }

      // Adicionar headers de rate limit
      res.set({
        'X-RateLimit-Limit': limit,
        'X-RateLimit-Remaining': rateLimitResult.remaining,
        'X-RateLimit-Reset': rateLimitResult.resetTime.toISOString()
      });

      next();
    } catch (error) {
      logger.error('Erro no rate limiting personalizado:', error);
      next(); // Continuar em caso de erro
    }
  };
};

module.exports = {
  // Rate limiting
  authRateLimit,
  apiRateLimit,
  strictRateLimit,
  speedLimiter,
  customRateLimit,
  
  // Segurança geral
  advancedSecurity,
  suspiciousActivityDetector,
  inputValidation,
  
  // Auditoria
  auditLogger,
  
  // Autenticação e autorização
  require2FA,
  requirePermission,
  
  // Utilitários
  createRateLimit
};
