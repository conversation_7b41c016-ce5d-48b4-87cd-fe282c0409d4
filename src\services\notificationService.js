const webpush = require('web-push');
const database = require('../config/database');
const { logger } = require('../utils/logger');
const websocketService = require('./websocketService');

class NotificationService {
  constructor() {
    this.setupWebPush();
  }

  // Configurar Web Push
  setupWebPush() {
    if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
      webpush.setVapidDetails(
        process.env.VAPID_SUBJECT || 'mailto:<EMAIL>',
        process.env.VAPID_PUBLIC_KEY,
        process.env.VAPID_PRIVATE_KEY
      );
      logger.info('Web Push configurado com sucesso');
    } else {
      logger.warn('VAPID keys não configuradas - notificações push desabilitadas');
    }
  }

  // Criar notificação
  async createNotification(data) {
    try {
      const {
        userId,
        type,
        title,
        message,
        data: notificationData = {},
        priority = 'normal',
        channels = ['websocket', 'database']
      } = data;

      const notification = {
        id: `notif-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        userId,
        type,
        title,
        message,
        data: notificationData,
        priority,
        timestamp: new Date().toISOString(),
        isRead: false
      };

      // Salvar no banco de dados
      if (channels.includes('database')) {
        await this.saveToDatabase(notification);
      }

      // Enviar via WebSocket
      if (channels.includes('websocket')) {
        await this.sendViaWebSocket(notification);
      }

      // Enviar push notification
      if (channels.includes('push')) {
        await this.sendPushNotification(notification);
      }

      // Enviar por email se for alta prioridade
      if (priority === 'urgent' && channels.includes('email')) {
        await this.sendEmailNotification(notification);
      }

      logger.info('Notificação criada:', { 
        notificationId: notification.id, 
        userId, 
        type, 
        priority 
      });

      return notification;
    } catch (error) {
      logger.error('Erro ao criar notificação:', error);
      throw error;
    }
  }

  // Salvar notificação no banco
  async saveToDatabase(notification) {
    try {
      await database.getClient().notification.create({
        data: {
          id: notification.id,
          userId: notification.userId,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          priority: notification.priority,
          isRead: false
        }
      });
    } catch (error) {
      logger.error('Erro ao salvar notificação no banco:', error);
      throw error;
    }
  }

  // Enviar via WebSocket
  async sendViaWebSocket(notification) {
    try {
      const socket = websocketService.connectedUsers.get(notification.userId);
      
      if (socket) {
        socket.emit('notification', notification);
        logger.debug('Notificação enviada via WebSocket:', { 
          userId: notification.userId, 
          type: notification.type 
        });
      } else {
        logger.debug('Usuário não está online para receber notificação WebSocket:', { 
          userId: notification.userId 
        });
      }
    } catch (error) {
      logger.error('Erro ao enviar notificação via WebSocket:', error);
    }
  }

  // Enviar push notification
  async sendPushNotification(notification) {
    try {
      // Buscar subscriptions do usuário
      const subscriptions = await database.getClient().pushSubscription.findMany({
        where: { 
          userId: notification.userId,
          isActive: true 
        }
      });

      if (subscriptions.length === 0) {
        logger.debug('Nenhuma subscription encontrada para push notification:', { 
          userId: notification.userId 
        });
        return;
      }

      const payload = JSON.stringify({
        title: notification.title,
        body: notification.message,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        data: {
          ...notification.data,
          notificationId: notification.id,
          url: this.getNotificationUrl(notification)
        },
        actions: this.getNotificationActions(notification),
        requireInteraction: notification.priority === 'urgent',
        silent: notification.priority === 'low'
      });

      const promises = subscriptions.map(async (subscription) => {
        try {
          await webpush.sendNotification(
            {
              endpoint: subscription.endpoint,
              keys: {
                p256dh: subscription.p256dh,
                auth: subscription.auth
              }
            },
            payload
          );
          
          logger.debug('Push notification enviada:', { 
            userId: notification.userId,
            subscriptionId: subscription.id 
          });
        } catch (error) {
          if (error.statusCode === 410) {
            // Subscription expirada, remover do banco
            await database.getClient().pushSubscription.delete({
              where: { id: subscription.id }
            });
            logger.info('Subscription expirada removida:', { subscriptionId: subscription.id });
          } else {
            logger.error('Erro ao enviar push notification:', error);
          }
        }
      });

      await Promise.allSettled(promises);
    } catch (error) {
      logger.error('Erro no serviço de push notification:', error);
    }
  }

  // Enviar notificação por email
  async sendEmailNotification(notification) {
    try {
      const user = await database.getClient().user.findUnique({
        where: { id: notification.userId },
        select: { email: true, name: true }
      });

      if (!user) {
        logger.warn('Usuário não encontrado para email notification:', { 
          userId: notification.userId 
        });
        return;
      }

      // Implementar envio de email (usar serviço de email configurado)
      const emailService = require('./emailService');
      
      await emailService.sendNotificationEmail({
        to: user.email,
        name: user.name,
        subject: `[UNIFORMS] ${notification.title}`,
        notification
      });

      logger.info('Email de notificação enviado:', { 
        userId: notification.userId, 
        email: user.email 
      });
    } catch (error) {
      logger.error('Erro ao enviar email de notificação:', error);
    }
  }

  // Obter URL da notificação
  getNotificationUrl(notification) {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3001';
    
    switch (notification.type) {
      case 'new_response':
        return `${baseUrl}/responses/${notification.data.responseId}`;
      case 'detractor_alert':
        return `${baseUrl}/conversations?priority=high`;
      case 'new_message':
        return `${baseUrl}/conversations/${notification.data.conversationId}`;
      case 'survey_completed':
        return `${baseUrl}/surveys/${notification.data.surveyId}/stats`;
      default:
        return `${baseUrl}/notifications`;
    }
  }

  // Obter ações da notificação
  getNotificationActions(notification) {
    switch (notification.type) {
      case 'detractor_alert':
        return [
          {
            action: 'view',
            title: 'Ver Conversa',
            icon: '/icons/view.png'
          },
          {
            action: 'assign',
            title: 'Atribuir a Mim',
            icon: '/icons/assign.png'
          }
        ];
      case 'new_message':
        return [
          {
            action: 'reply',
            title: 'Responder',
            icon: '/icons/reply.png'
          },
          {
            action: 'view',
            title: 'Ver Conversa',
            icon: '/icons/view.png'
          }
        ];
      default:
        return [
          {
            action: 'view',
            title: 'Ver Detalhes',
            icon: '/icons/view.png'
          }
        ];
    }
  }

  // Notificar nova resposta
  async notifyNewResponse(response) {
    try {
      // Buscar usuários que devem ser notificados
      const users = await database.getClient().user.findMany({
        where: {
          OR: [
            { role: 'ADMIN' },
            { role: 'AGENT' }
          ]
        },
        select: { id: true }
      });

      const notifications = users.map(user => ({
        userId: user.id,
        type: response.isDetractor ? 'detractor_alert' : 'new_response',
        title: response.isDetractor ? '🚨 DETRATOR IDENTIFICADO' : 'Nova Resposta NPS',
        message: response.isDetractor 
          ? `Cliente insatisfeito (${response.score}/10) precisa de atenção imediata!`
          : `Nova resposta recebida: ${response.score}/10`,
        data: {
          responseId: response.id,
          surveyId: response.surveyId,
          score: response.score,
          isDetractor: response.isDetractor,
          userPhone: response.userPhone
        },
        priority: response.isDetractor ? 'urgent' : 'normal',
        channels: response.isDetractor 
          ? ['websocket', 'database', 'push', 'email']
          : ['websocket', 'database', 'push']
      }));

      // Criar notificações em paralelo
      await Promise.all(
        notifications.map(notification => this.createNotification(notification))
      );

      logger.info('Notificações de nova resposta enviadas:', { 
        responseId: response.id, 
        isDetractor: response.isDetractor,
        usersNotified: users.length 
      });
    } catch (error) {
      logger.error('Erro ao notificar nova resposta:', error);
    }
  }

  // Notificar nova mensagem
  async notifyNewMessage(conversationId, message, excludeUserId = null) {
    try {
      // Buscar usuários que devem ser notificados (participantes da conversa)
      const conversation = await database.getClient().conversation.findUnique({
        where: { id: conversationId },
        select: { 
          department: true,
          priority: true 
        }
      });

      if (!conversation) return;

      // Buscar agentes do departamento ou todos se for admin
      const users = await database.getClient().user.findMany({
        where: {
          AND: [
            {
              OR: [
                { role: 'ADMIN' },
                { role: 'AGENT' }
              ]
            },
            excludeUserId ? { NOT: { id: excludeUserId } } : {}
          ]
        },
        select: { id: true }
      });

      const notifications = users.map(user => ({
        userId: user.id,
        type: 'new_message',
        title: 'Nova Mensagem',
        message: `${message.senderName || 'Cliente'}: ${message.text.substring(0, 100)}...`,
        data: {
          conversationId,
          messageId: message.id,
          senderId: message.senderId,
          senderName: message.senderName
        },
        priority: conversation.priority === 'HIGH' ? 'high' : 'normal',
        channels: ['websocket', 'database', 'push']
      }));

      await Promise.all(
        notifications.map(notification => this.createNotification(notification))
      );

      logger.debug('Notificações de nova mensagem enviadas:', { 
        conversationId, 
        usersNotified: users.length 
      });
    } catch (error) {
      logger.error('Erro ao notificar nova mensagem:', error);
    }
  }

  // Marcar notificação como lida
  async markAsRead(notificationId, userId) {
    try {
      await database.getClient().notification.updateMany({
        where: {
          id: notificationId,
          userId
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });

      logger.debug('Notificação marcada como lida:', { notificationId, userId });
    } catch (error) {
      logger.error('Erro ao marcar notificação como lida:', error);
      throw error;
    }
  }

  // Marcar todas as notificações como lidas
  async markAllAsRead(userId) {
    try {
      const result = await database.getClient().notification.updateMany({
        where: {
          userId,
          isRead: false
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });

      logger.info('Todas as notificações marcadas como lidas:', { 
        userId, 
        count: result.count 
      });

      return result.count;
    } catch (error) {
      logger.error('Erro ao marcar todas as notificações como lidas:', error);
      throw error;
    }
  }

  // Obter notificações do usuário
  async getUserNotifications(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        unreadOnly = false,
        type = null
      } = options;

      const where = {
        userId,
        ...(unreadOnly && { isRead: false }),
        ...(type && { type })
      };

      const [notifications, total] = await Promise.all([
        database.getClient().notification.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit
        }),
        database.getClient().notification.count({ where })
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Erro ao obter notificações do usuário:', error);
      throw error;
    }
  }

  // Limpar notificações antigas
  async cleanupOldNotifications(daysToKeep = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await database.getClient().notification.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          },
          isRead: true
        }
      });

      logger.info('Notificações antigas limpas:', { 
        deletedCount: result.count,
        cutoffDate 
      });

      return result.count;
    } catch (error) {
      logger.error('Erro ao limpar notificações antigas:', error);
      throw error;
    }
  }
}

module.exports = new NotificationService();
