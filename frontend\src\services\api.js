import axios from 'axios';
import { toast } from 'react-hot-toast';

// URL base da API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

// Instância principal do axios
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Instância específica para autenticação
export const authAPI = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar token automaticamente
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor para tratar respostas e erros
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Tratar diferentes tipos de erro
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Token inválido ou expirado
          localStorage.removeItem('token');
          window.location.href = '/login';
          toast.error('Sessão expirada. Faça login novamente.');
          break;
          
        case 403:
          toast.error('Você não tem permissão para realizar esta ação.');
          break;
          
        case 404:
          toast.error('Recurso não encontrado.');
          break;
          
        case 422:
          // Erros de validação
          if (data.details && Array.isArray(data.details)) {
            data.details.forEach(detail => {
              toast.error(detail.message);
            });
          } else {
            toast.error(data.message || 'Dados inválidos.');
          }
          break;
          
        case 429:
          toast.error('Muitas requisições. Tente novamente em alguns minutos.');
          break;
          
        case 500:
          toast.error('Erro interno do servidor. Tente novamente mais tarde.');
          break;
          
        default:
          toast.error(data.message || 'Ocorreu um erro inesperado.');
      }
    } else if (error.request) {
      // Erro de rede
      toast.error('Erro de conexão. Verifique sua internet.');
    } else {
      // Outros erros
      toast.error('Ocorreu um erro inesperado.');
    }
    
    return Promise.reject(error);
  }
);

// Serviços da API

// Autenticação
export const authService = {
  login: (email, password) => authAPI.post('/auth/login', { email, password }),
  register: (userData) => authAPI.post('/auth/register', userData),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (userData) => api.put('/auth/profile', userData),
  changePassword: (currentPassword, newPassword) => 
    api.post('/auth/change-password', { currentPassword, newPassword }),
  refreshToken: () => api.post('/auth/refresh-token'),
};

// Pesquisas
export const surveyService = {
  getAll: (params) => api.get('/surveys', { params }),
  getById: (id) => api.get(`/surveys/${id}`),
  create: (data) => api.post('/surveys', data),
  update: (id, data) => api.put(`/surveys/${id}`, data),
  delete: (id) => api.delete(`/surveys/${id}`),
  duplicate: (id) => api.post(`/surveys/${id}/duplicate`),
  toggleStatus: (id) => api.patch(`/surveys/${id}/toggle-status`),
  getStats: (id) => api.get(`/surveys/${id}/stats`),
  generateLinks: (id) => api.post(`/surveys/${id}/generate-links`),
};

// Respostas
export const responseService = {
  getAll: (params) => api.get('/responses', { params }),
  getById: (id) => api.get(`/responses/${id}`),
  create: (data) => api.post('/responses', data),
  getAnalytics: (params) => api.get('/responses/analytics', { params }),
  getSummaryBySurvey: () => api.get('/responses/summary/by-survey'),
  exportCSV: (params) => api.get('/responses/export/csv', { 
    params,
    responseType: 'blob'
  }),
  markAsRead: (id) => api.patch(`/responses/${id}/read`),
  addNote: (id, note) => api.post(`/responses/${id}/notes`, { note }),
};

// WhatsApp
export const whatsappService = {
  getInstances: () => api.get('/whatsapp/instances'),
  createInstance: (data) => api.post('/whatsapp/instances', data),
  getQRCode: (instanceName) => api.get(`/whatsapp/instances/${instanceName}/qrcode`),
  getInstanceStatus: (instanceName) => api.get(`/whatsapp/instances/${instanceName}/status`),
  deleteInstance: (instanceName) => api.delete(`/whatsapp/instances/${instanceName}`),
  sendMessage: (data) => api.post('/whatsapp/send-message', data),
  sendSurvey: (data) => api.post('/whatsapp/send-survey', data),
  sendBulkSurvey: (data) => api.post('/whatsapp/send-bulk-survey', data),
  validateNumber: (instanceName, number) => 
    api.post('/whatsapp/validate-number', { instanceName, number }),
  getContact: (instanceName, number) => 
    api.get(`/whatsapp/contact/${instanceName}/${number}`),
  getChats: (instanceName) => api.get(`/whatsapp/chats/${instanceName}`),
  getMessages: (instanceName, number, limit) => 
    api.get(`/whatsapp/messages/${instanceName}/${number}`, { params: { limit } }),
  testConnection: () => api.get('/whatsapp/test-connection'),
  getApiInfo: () => api.get('/whatsapp/api-info'),
};

// Conversas
export const conversationService = {
  getAll: (params) => api.get('/conversations', { params }),
  getById: (id) => api.get(`/conversations/${id}`),
  sendMessage: (id, message) => api.post(`/conversations/${id}/messages`, { message }),
  updateStatus: (id, status) => api.patch(`/conversations/${id}/status`, { status }),
  updatePriority: (id, priority) => api.patch(`/conversations/${id}/priority`, { priority }),
  assignDepartment: (id, department) => 
    api.patch(`/conversations/${id}/department`, { department }),
  analyze: (id) => api.post(`/conversations/${id}/analyze`),
  markAsRead: (id) => api.patch(`/conversations/${id}/read`),
  addNote: (id, note, isPrivate) => 
    api.post(`/conversations/${id}/notes`, { note, isPrivate }),
  transfer: (id, data) => api.post(`/conversations/${id}/transfer`, data),
  getStats: (params) => api.get('/conversations/stats', { params }),
  getUnassigned: (limit) => api.get('/conversations/unassigned/list', { params: { limit } }),
  searchByPhone: (phone) => api.get(`/conversations/search/by-phone/${phone}`),
};

// Departamentos
export const departmentService = {
  getAll: (params) => api.get('/departments', { params }),
  getById: (id) => api.get(`/departments/${id}`),
  create: (data) => api.post('/departments', data),
  update: (id, data) => api.put(`/departments/${id}`, data),
  delete: (id) => api.delete(`/departments/${id}`),
  toggleStatus: (id) => api.patch(`/departments/${id}/toggle-status`),
  addKeyword: (id, keyword) => api.post(`/departments/${id}/keywords`, { keyword }),
  removeKeyword: (id, keyword) => api.delete(`/departments/${id}/keywords/${keyword}`),
  analyzeText: (text, includeDetails) => 
    api.post('/departments/analyze-text', { text, includeDetails }),
  testAnalysis: (text) => api.post('/departments/test-analysis', { text }),
  getStats: (params) => api.get('/departments/stats', { params }),
  suggestKeywords: (departmentId, limit) => 
    api.get('/departments/suggest-keywords', { params: { departmentId, limit } }),
};

// Utilitários
export const utilService = {
  healthCheck: () => api.get('/health'),
  uploadFile: (file, onProgress) => {
    const formData = new FormData();
    formData.append('file', file);
    
    return api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    });
  },
};

// Função para fazer download de arquivos
export const downloadFile = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob',
    });
    
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
    
    toast.success('Download iniciado!');
  } catch (error) {
    toast.error('Erro ao fazer download do arquivo.');
    throw error;
  }
};

// Função para formatar erros da API
export const formatApiError = (error) => {
  if (error.response?.data?.details) {
    return error.response.data.details.map(d => d.message).join(', ');
  }
  return error.response?.data?.message || error.message || 'Erro desconhecido';
};

export default api;
