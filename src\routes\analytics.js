const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');
const { validate } = require('../utils/validation');
const Joi = require('joi');
const {
  generateAdvancedReport,
  getSatisfactionPredictions,
  getChurnAnalysis,
  getSentimentAnalysis,
  getSegmentPatterns,
  getRealTimeMetrics,
  getTemporalComparison,
  downloadFile,
  getMLStatus
} = require('../controllers/analyticsController');

const router = express.Router();

// Todas as rotas requerem autenticação
router.use(authenticate);

// Validações comuns
const dateValidation = Joi.date().iso();
const periodValidation = Joi.string().valid('1h', '6h', '24h', '7d', '30d', '90d', '365d');

// Relatório avançado
router.get('/report/advanced',
  validate(Joi.object({
    surveyId: Joi.string().uuid().optional(),
    startDate: dateValidation.optional(),
    endDate: dateValidation.optional(),
    department: Joi.string().optional(),
    includeML: Joi.boolean().default(true),
    format: Joi.string().valid('json', 'excel', 'pdf').default('json'),
    filters: Joi.string().optional() // JSON string
  }).options({ allowUnknown: true })),
  generateAdvancedReport
);

// Predições de satisfação (ML)
router.get('/predictions/satisfaction',
  validate(Joi.object({
    userPhone: Joi.string().optional(),
    limit: Joi.number().integer().min(1).max(100).default(10)
  }).options({ allowUnknown: true })),
  getSatisfactionPredictions
);

// Análise de churn (ML)
router.get('/predictions/churn',
  validate(Joi.object({
    userPhone: Joi.string().optional(),
    riskLevel: Joi.string().valid('high', 'medium', 'low').optional(),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }).options({ allowUnknown: true })),
  getChurnAnalysis
);

// Análise de sentimento
router.get('/sentiment',
  validate(Joi.object({
    text: Joi.string().optional(),
    surveyId: Joi.string().uuid().optional(),
    startDate: dateValidation.optional(),
    endDate: dateValidation.optional()
  }).options({ allowUnknown: true })),
  getSentimentAnalysis
);

// Padrões por segmento (ML)
router.get('/patterns/segments',
  validate(Joi.object({
    department: Joi.string().optional(),
    timeRange: Joi.string().valid('7d', '30d', '90d', '365d').default('30d'),
    minResponses: Joi.number().integer().min(1).default(5)
  }).options({ allowUnknown: true })),
  getSegmentPatterns
);

// Métricas em tempo real
router.get('/realtime',
  validate(Joi.object({
    period: Joi.string().valid('1h', '6h', '24h', '7d').default('24h')
  }).options({ allowUnknown: true })),
  getRealTimeMetrics
);

// Comparação temporal
router.get('/comparison',
  validate(Joi.object({
    currentPeriod: periodValidation.default('30d'),
    comparisonPeriod: periodValidation.default('30d'),
    surveyId: Joi.string().uuid().optional()
  }).options({ allowUnknown: true })),
  getTemporalComparison
);

// Status do ML
router.get('/ml/status', getMLStatus);

// Download de arquivos exportados
router.get('/download/:filename',
  validate(Joi.object({
    filename: Joi.string().pattern(/^[\w\-. ]+\.(xlsx|pdf)$/).required()
  })),
  downloadFile
);

// Rotas específicas para admins

// Análise completa do sistema (apenas admins)
router.get('/system/overview',
  authorize('ADMIN'),
  async (req, res) => {
    try {
      const { period = '30d' } = req.query;
      const database = require('../config/database');
      
      // Calcular período
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90, '365d': 365 }[period] || 30;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      // Buscar dados do sistema
      const [
        totalUsers,
        totalSurveys,
        totalResponses,
        totalConversations,
        recentActivity
      ] = await Promise.all([
        database.getClient().user.count(),
        database.getClient().survey.count(),
        database.getClient().response.count({
          where: { createdAt: { gte: startDate } }
        }),
        database.getClient().conversation.count({
          where: { createdAt: { gte: startDate } }
        }),
        database.getClient().response.findMany({
          where: { createdAt: { gte: startDate } },
          include: {
            survey: { select: { title: true } }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        })
      ]);

      // Estatísticas por usuário
      const userStats = await database.getClient().user.findMany({
        include: {
          surveys: {
            include: {
              _count: {
                select: { responses: true }
              }
            }
          },
          _count: {
            select: { surveys: true }
          }
        }
      });

      res.json({
        period: { start: startDate, end: now, days },
        overview: {
          totalUsers,
          totalSurveys,
          totalResponses,
          totalConversations,
          avgResponsesPerSurvey: totalSurveys > 0 ? Math.round(totalResponses / totalSurveys) : 0
        },
        userStats: userStats.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          surveysCreated: user._count.surveys,
          totalResponses: user.surveys.reduce((sum, survey) => sum + survey._count.responses, 0)
        })),
        recentActivity: recentActivity.map(response => ({
          id: response.id,
          score: response.score,
          surveyTitle: response.survey.title,
          userPhone: response.userPhone,
          createdAt: response.createdAt
        }))
      });
    } catch (error) {
      logger.error('Erro na análise do sistema:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Análise de performance por usuário (apenas admins)
router.get('/users/performance',
  authorize('ADMIN'),
  validate(Joi.object({
    userId: Joi.string().uuid().optional(),
    period: periodValidation.default('30d'),
    sortBy: Joi.string().valid('responses', 'nps', 'surveys').default('responses')
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const { userId, period, sortBy } = req.query;
      const database = require('../config/database');
      
      // Calcular período
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90, '365d': 365 }[period] || 30;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      // Buscar usuários e suas métricas
      const whereClause = userId ? { id: userId } : {};
      
      const users = await database.getClient().user.findMany({
        where: whereClause,
        include: {
          surveys: {
            include: {
              responses: {
                where: { createdAt: { gte: startDate } }
              }
            }
          }
        }
      });

      const performance = users.map(user => {
        const allResponses = user.surveys.flatMap(survey => survey.responses);
        const totalResponses = allResponses.length;
        const promoters = allResponses.filter(r => r.score >= 9).length;
        const detractors = allResponses.filter(r => r.score <= 6).length;
        const nps = totalResponses > 0 ? Math.round(((promoters - detractors) / totalResponses) * 100) : 0;
        const avgScore = totalResponses > 0 ? allResponses.reduce((sum, r) => sum + r.score, 0) / totalResponses : 0;

        return {
          userId: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          surveysCreated: user.surveys.length,
          totalResponses,
          npsScore: nps,
          averageScore: Math.round(avgScore * 100) / 100,
          promoters,
          detractors,
          responseRate: user.surveys.length > 0 ? Math.round(totalResponses / user.surveys.length) : 0
        };
      });

      // Ordenar por critério especificado
      performance.sort((a, b) => {
        switch (sortBy) {
          case 'nps':
            return b.npsScore - a.npsScore;
          case 'surveys':
            return b.surveysCreated - a.surveysCreated;
          default:
            return b.totalResponses - a.totalResponses;
        }
      });

      res.json({
        period: { start: startDate, end: now, days },
        performance,
        summary: {
          totalUsers: performance.length,
          avgNPS: Math.round(performance.reduce((sum, p) => sum + p.npsScore, 0) / performance.length),
          totalSurveys: performance.reduce((sum, p) => sum + p.surveysCreated, 0),
          totalResponses: performance.reduce((sum, p) => sum + p.totalResponses, 0)
        }
      });
    } catch (error) {
      logger.error('Erro na análise de performance:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Análise de tendências do sistema (apenas admins)
router.get('/system/trends',
  authorize('ADMIN'),
  validate(Joi.object({
    period: periodValidation.default('90d'),
    granularity: Joi.string().valid('daily', 'weekly', 'monthly').default('daily')
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const { period, granularity } = req.query;
      const database = require('../config/database');
      
      // Calcular período
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90, '365d': 365 }[period] || 90;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      // Query baseada na granularidade
      let dateFormat;
      switch (granularity) {
        case 'weekly':
          dateFormat = "DATE_TRUNC('week', created_at)";
          break;
        case 'monthly':
          dateFormat = "DATE_TRUNC('month', created_at)";
          break;
        default:
          dateFormat = "DATE_TRUNC('day', created_at)";
      }

      // Buscar tendências de respostas
      const responseTrends = await database.getClient().$queryRaw`
        SELECT 
          ${dateFormat} as period,
          COUNT(*) as total_responses,
          AVG(score) as avg_score,
          COUNT(CASE WHEN score >= 9 THEN 1 END) as promoters,
          COUNT(CASE WHEN score <= 6 THEN 1 END) as detractors
        FROM responses 
        WHERE created_at >= ${startDate}
        GROUP BY ${dateFormat}
        ORDER BY period
      `;

      // Buscar tendências de conversas
      const conversationTrends = await database.getClient().$queryRaw`
        SELECT 
          ${dateFormat} as period,
          COUNT(*) as total_conversations,
          COUNT(CASE WHEN status = 'RESOLVED' THEN 1 END) as resolved,
          COUNT(CASE WHEN priority = 'HIGH' THEN 1 END) as high_priority
        FROM conversations 
        WHERE created_at >= ${startDate}
        GROUP BY ${dateFormat}
        ORDER BY period
      `;

      // Processar dados
      const trends = responseTrends.map(row => {
        const conversationData = conversationTrends.find(c => 
          new Date(c.period).getTime() === new Date(row.period).getTime()
        ) || { total_conversations: 0, resolved: 0, high_priority: 0 };

        const totalResponses = parseInt(row.total_responses);
        const promoters = parseInt(row.promoters);
        const detractors = parseInt(row.detractors);
        const nps = totalResponses > 0 ? Math.round(((promoters - detractors) / totalResponses) * 100) : 0;

        return {
          period: row.period,
          responses: {
            total: totalResponses,
            averageScore: parseFloat(row.avg_score) || 0,
            nps,
            promoters,
            detractors
          },
          conversations: {
            total: parseInt(conversationData.total_conversations),
            resolved: parseInt(conversationData.resolved),
            highPriority: parseInt(conversationData.high_priority),
            resolutionRate: conversationData.total_conversations > 0 
              ? Math.round((conversationData.resolved / conversationData.total_conversations) * 100)
              : 0
          }
        };
      });

      res.json({
        period: { start: startDate, end: now, days },
        granularity,
        trends,
        summary: {
          totalPeriods: trends.length,
          avgNPS: Math.round(trends.reduce((sum, t) => sum + t.responses.nps, 0) / trends.length),
          avgResolutionRate: Math.round(trends.reduce((sum, t) => sum + t.conversations.resolutionRate, 0) / trends.length),
          totalResponses: trends.reduce((sum, t) => sum + t.responses.total, 0),
          totalConversations: trends.reduce((sum, t) => sum + t.conversations.total, 0)
        }
      });
    } catch (error) {
      logger.error('Erro na análise de tendências:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

module.exports = router;
