import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { StatusBadge, ConfirmModal } from '../UI';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const SurveyCard = ({
  survey,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleStatus,
  onViewResponses,
  className = ''
}) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleDelete = () => {
    onDelete(survey.id);
    setShowDeleteModal(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'draft': return 'gray';
      case 'archived': return 'warning';
      default: return 'gray';
    }
  };

  return (
    <>
      <div className={`card hover:shadow-lg transition-shadow duration-200 ${className}`}>
        <div className="card-body">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {survey.title}
              </h3>
              <p className="text-sm text-gray-600 line-clamp-2">
                {survey.description}
              </p>
            </div>
            <StatusBadge status={survey.status} />
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {survey.responseCount || 0}
              </div>
              <div className="text-xs text-gray-500">Respostas</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {survey.npsScore || 0}
              </div>
              <div className="text-xs text-gray-500">NPS</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {survey.questionCount || 0}
              </div>
              <div className="text-xs text-gray-500">Perguntas</div>
            </div>
          </div>

          {/* Meta info */}
          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <span>
              Criado {formatDistanceToNow(new Date(survey.createdAt), { 
                addSuffix: true, 
                locale: ptBR 
              })}
            </span>
            {survey.lastResponseAt && (
              <span>
                Última resposta {formatDistanceToNow(new Date(survey.lastResponseAt), { 
                  addSuffix: true, 
                  locale: ptBR 
                })}
              </span>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <Link
                to={`/surveys/${survey.id}`}
                className="btn-outline btn-sm"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Ver
              </Link>
              
              <button
                onClick={() => onEdit(survey)}
                className="btn-outline btn-sm"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Editar
              </button>

              <button
                onClick={() => onViewResponses(survey.id)}
                className="btn-outline btn-sm"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Respostas
              </button>
            </div>

            <div className="flex space-x-1">
              <button
                onClick={() => onToggleStatus(survey.id)}
                className={`btn-sm ${survey.status === 'active' ? 'btn-warning' : 'btn-success'}`}
                title={survey.status === 'active' ? 'Desativar' : 'Ativar'}
              >
                {survey.status === 'active' ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9V6a4 4 0 118 0v3M5 9h14l1 12H4L5 9z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                  </svg>
                )}
              </button>

              <button
                onClick={() => onDuplicate(survey.id)}
                className="btn-outline btn-sm"
                title="Duplicar"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>

              <button
                onClick={() => setShowDeleteModal(true)}
                className="btn-danger btn-sm"
                title="Excluir"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        title="Excluir Pesquisa"
        message={`Tem certeza que deseja excluir a pesquisa "${survey.title}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        type="danger"
      />
    </>
  );
};

export default SurveyCard;
