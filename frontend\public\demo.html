<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNIFORMS2 - Sistema de Pesquisas SaaS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">
                        <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                        UNIFORMS2
                    </h1>
                    <span class="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                        SaaS
                    </span>
                </div>
                <nav class="flex space-x-8">
                    <a href="#dashboard" class="nav-link text-blue-600 border-b-2 border-blue-600">Dashboard</a>
                    <a href="#surveys" class="nav-link text-gray-500 hover:text-gray-700">Pesquisas</a>
                    <a href="#responses" class="nav-link text-gray-500 hover:text-gray-700">Respostas</a>
                    <a href="#gamification" class="nav-link text-gray-500 hover:text-gray-700">Gamificação</a>
                    <a href="#analytics" class="nav-link text-gray-500 hover:text-gray-700">Analytics</a>
                    <a href="#integration" class="nav-link text-gray-500 hover:text-gray-700">Integração</a>
                    <a href="#admin" class="nav-link text-gray-500 hover:text-gray-700" id="admin-nav" style="display:none">Admin</a>
                    <a href="#api-test" class="nav-link text-gray-500 hover:text-gray-700">API Test</a>
                </nav>
                <div class="flex items-center space-x-4">
                    <span id="user-name" class="text-sm text-gray-600"></span>
                    <button onclick="logout()" class="text-sm text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt mr-1"></i>Sair
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Section -->
        <div id="dashboard" class="section">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Dashboard</h2>
                <p class="text-gray-600">Visão geral do seu sistema de pesquisas de satisfação</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <i class="fas fa-poll text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total de Pesquisas</p>
                            <p class="text-2xl font-bold text-gray-900" id="total-surveys">12</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-comments text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Respostas Recebidas</p>
                            <p class="text-2xl font-bold text-gray-900" id="total-responses">1,247</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <i class="fas fa-star text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">NPS Médio</p>
                            <p class="text-2xl font-bold text-gray-900" id="average-nps">8.4</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <i class="fas fa-trophy text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Usuários Ativos</p>
                            <p class="text-2xl font-bold text-gray-900" id="active-users">342</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Respostas por Dia</h3>
                    <canvas id="responsesChart" width="400" height="200"></canvas>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Distribuição NPS</h3>
                    <canvas id="npsChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Atividade Recente</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4" id="recent-activity">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span class="text-sm text-gray-600">Nova resposta recebida - NPS: 9</span>
                            <span class="text-xs text-gray-400">há 2 minutos</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span class="text-sm text-gray-600">Pesquisa "Satisfação do Cliente" criada</span>
                            <span class="text-xs text-gray-400">há 1 hora</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                            <span class="text-sm text-gray-600">Usuário conquistou badge "Promotor da Marca"</span>
                            <span class="text-xs text-gray-400">há 3 horas</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Surveys Section -->
        <div id="surveys" class="section hidden">
            <div class="mb-8">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">Pesquisas</h2>
                        <p class="text-gray-600">Gerencie suas pesquisas de satisfação</p>
                    </div>
                    <button onclick="showCreateSurveyModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                        <i class="fas fa-plus mr-2"></i>Nova Pesquisa
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Respostas</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Criada em</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody id="surveys-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Surveys will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Responses Section -->
        <div id="responses" class="section hidden">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Respostas</h2>
                <p class="text-gray-600">Visualize e analise as respostas recebidas</p>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Filtros</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Pesquisa</label>
                        <select id="filter-survey" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Todas as pesquisas</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nota</label>
                        <select id="filter-score" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Todas as notas</option>
                            <option value="promoter">Promotores (9-10)</option>
                            <option value="neutral">Neutros (7-8)</option>
                            <option value="detractor">Detratores (0-6)</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Data</label>
                        <input type="date" id="filter-date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex items-end">
                        <button onclick="filterResponses()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Filtrar
                        </button>
                    </div>
                </div>
            </div>

            <!-- Responses Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Telefone</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nota</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feedback</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody id="responses-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Responses will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Gamification Section -->
        <div id="gamification" class="section hidden">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Sistema de Gamificação</h2>
                <p class="text-gray-600">Engaje seus usuários com pontos, badges e recompensas</p>
            </div>

            <!-- Gamification Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-100">Total de Pontos Distribuídos</p>
                            <p class="text-3xl font-bold">45,230</p>
                        </div>
                        <i class="fas fa-coins text-4xl text-blue-200"></i>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-500 to-teal-600 rounded-lg p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-100">Badges Conquistados</p>
                            <p class="text-3xl font-bold">127</p>
                        </div>
                        <i class="fas fa-trophy text-4xl text-green-200"></i>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-orange-500 to-red-600 rounded-lg p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-orange-100">Recompensas Resgatadas</p>
                            <p class="text-3xl font-bold">34</p>
                        </div>
                        <i class="fas fa-gift text-4xl text-orange-200"></i>
                    </div>
                </div>
            </div>

            <!-- Leaderboard -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🏆 Ranking dos Usuários</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border-2 border-yellow-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center font-bold text-yellow-900">1</div>
                            <div>
                                <p class="font-medium text-gray-900">Usuário ****1234</p>
                                <p class="text-sm text-gray-600">Nível 12 • 45 respostas</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">2,340 pts</p>
                            <p class="text-xs text-gray-500">🏆 Promotor da Marca</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center font-bold text-gray-700">2</div>
                            <div>
                                <p class="font-medium text-gray-900">Usuário ****5678</p>
                                <p class="text-sm text-gray-600">Nível 10 • 38 respostas</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">1,890 pts</p>
                            <p class="text-xs text-gray-500">🔥 Guerreiro da Sequência</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-300 rounded-full flex items-center justify-center font-bold text-orange-700">3</div>
                            <div>
                                <p class="font-medium text-gray-900">Usuário ****9012</p>
                                <p class="text-sm text-gray-600">Nível 8 • 29 respostas</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">1,560 pts</p>
                            <p class="text-xs text-gray-500">💬 Mestre do Feedback</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Section -->
        <div id="analytics" class="section hidden">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Analytics</h2>
                <p class="text-gray-600">Análise detalhada dos dados de satisfação</p>
            </div>

            <!-- KPIs -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">NPS Score</p>
                            <p class="text-3xl font-bold text-blue-600" id="analytics-nps">--</p>
                        </div>
                        <div class="p-3 bg-blue-100 rounded-full">
                            <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Taxa de Resposta</p>
                            <p class="text-3xl font-bold text-green-600" id="analytics-response-rate">--</p>
                        </div>
                        <div class="p-3 bg-green-100 rounded-full">
                            <i class="fas fa-percentage text-green-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Promotores</p>
                            <p class="text-3xl font-bold text-emerald-600" id="analytics-promoters">--</p>
                        </div>
                        <div class="p-3 bg-emerald-100 rounded-full">
                            <i class="fas fa-thumbs-up text-emerald-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Detratores</p>
                            <p class="text-3xl font-bold text-red-600" id="analytics-detractors">--</p>
                        </div>
                        <div class="p-3 bg-red-100 rounded-full">
                            <i class="fas fa-thumbs-down text-red-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Evolução do NPS</h3>
                    <canvas id="npsEvolutionChart" width="400" height="200"></canvas>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Distribuição de Notas</h3>
                    <canvas id="scoreDistributionChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Detailed Analysis -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise Detalhada</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-3">Principais Feedbacks Positivos</h4>
                        <div id="positive-feedback" class="space-y-2">
                            <!-- Positive feedback will be loaded here -->
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 mb-3">Principais Feedbacks Negativos</h4>
                        <div id="negative-feedback" class="space-y-2">
                            <!-- Negative feedback will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Section -->
        <div id="integration" class="section hidden">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Integração WhatsApp</h2>
                <p class="text-gray-600">Configure e gerencie a integração com WhatsApp</p>
            </div>

            <!-- WhatsApp Setup -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Configuração do WhatsApp</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nome da Instância</label>
                        <input type="text" id="instance-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="minha-empresa">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
                        <input type="url" id="webhook-url" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://meusite.com/webhook">
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="createWhatsAppInstance()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        <i class="fab fa-whatsapp mr-2"></i>Criar Instância
                    </button>
                </div>
            </div>

            <!-- Send Survey -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Enviar Pesquisa</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Telefone</label>
                        <input type="tel" id="send-phone" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="+5511999999999">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Pesquisa</label>
                        <select id="send-survey" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Selecione uma pesquisa</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="sendSurveyWhatsApp()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-paper-plane mr-2"></i>Enviar
                        </button>
                    </div>
                </div>
            </div>

            <!-- Integration Guide -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Guia de Integração</h3>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        <div>
                            <h4 class="font-medium text-gray-900">Configure sua instância WhatsApp</h4>
                            <p class="text-sm text-gray-600">Crie uma instância e configure o webhook para receber respostas</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        <div>
                            <h4 class="font-medium text-gray-900">Escaneie o QR Code</h4>
                            <p class="text-sm text-gray-600">Use o WhatsApp Web para conectar sua conta</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        <div>
                            <h4 class="font-medium text-gray-900">Envie pesquisas</h4>
                            <p class="text-sm text-gray-600">Use a API ou interface para enviar pesquisas aos seus clientes</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                        <div>
                            <h4 class="font-medium text-gray-900">Receba respostas automaticamente</h4>
                            <p class="text-sm text-gray-600">As respostas serão processadas e a gamificação aplicada automaticamente</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Section -->
        <div id="admin" class="section hidden">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Administração</h2>
                <p class="text-gray-600">Gerencie usuários, configurações e sistema</p>
            </div>

            <!-- Admin Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-indigo-100 rounded-lg">
                            <i class="fas fa-users text-indigo-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total de Usuários</p>
                            <p class="text-2xl font-bold text-gray-900" id="admin-total-users">--</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-user-check text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Usuários Ativos</p>
                            <p class="text-2xl font-bold text-gray-900" id="admin-active-users">--</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <i class="fas fa-database text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total de Dados</p>
                            <p class="text-2xl font-bold text-gray-900" id="admin-total-data">--</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <i class="fas fa-server text-red-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Status do Sistema</p>
                            <p class="text-2xl font-bold text-green-600">Online</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Management -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Gerenciar Usuários</h3>
                    <button onclick="showCreateUserModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        <i class="fas fa-user-plus mr-2"></i>Novo Usuário
                    </button>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Função</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
                            <!-- Users will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- System Settings -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Configurações do Sistema</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nome da Empresa</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" value="UNIFORMS2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email de Suporte</label>
                        <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" value="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="America/Sao_Paulo">América/São Paulo</option>
                            <option value="America/New_York">América/Nova York</option>
                            <option value="Europe/London">Europa/Londres</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Idioma Padrão</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="pt-BR">Português (Brasil)</option>
                            <option value="en-US">English (US)</option>
                            <option value="es-ES">Español</option>
                        </select>
                    </div>
                </div>
                <div class="mt-6">
                    <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>Salvar Configurações
                    </button>
                </div>
            </div>
        </div>

        <!-- API Test Section -->
        <div id="api-test" class="section hidden">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Teste de APIs</h2>
                <p class="text-gray-600">Teste as APIs do sistema em tempo real</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- API Endpoints -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Endpoints Disponíveis</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-mono text-sm">GET /health</span>
                                <p class="text-xs text-gray-600">Status da API</p>
                            </div>
                            <button onclick="testAPI('/health')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Testar</button>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-mono text-sm">GET /api/surveys</span>
                                <p class="text-xs text-gray-600">Listar pesquisas</p>
                            </div>
                            <button onclick="testAPI('/api/surveys')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Testar</button>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-mono text-sm">GET /api/responses</span>
                                <p class="text-xs text-gray-600">Listar respostas</p>
                            </div>
                            <button onclick="testAPI('/api/responses')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Testar</button>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-mono text-sm">GET /api/gamification/leaderboard</span>
                                <p class="text-xs text-gray-600">Ranking de usuários</p>
                            </div>
                            <button onclick="testAPI('/api/gamification/leaderboard')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Testar</button>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-mono text-sm">GET /api/gamification/badges</span>
                                <p class="text-xs text-gray-600">Badges disponíveis</p>
                            </div>
                            <button onclick="testAPI('/api/gamification/badges')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Testar</button>
                        </div>
                    </div>
                </div>

                <!-- API Response -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Resposta da API</h3>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm h-96 overflow-auto" id="api-response">
                        <div class="text-gray-500">Clique em "Testar" em qualquer endpoint para ver a resposta aqui...</div>
                    </div>
                </div>
            </div>

            <!-- Create Test Data -->
            <div class="mt-8 bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Criar Dados de Teste</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="createTestSurvey()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        <i class="fas fa-plus mr-2"></i>Criar Pesquisa de Teste
                    </button>
                    <button onclick="createTestResponse()" class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700">
                        <i class="fas fa-comment mr-2"></i>Criar Resposta de Teste
                    </button>
                    <button onclick="testGamification()" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                        <i class="fas fa-trophy mr-2"></i>Testar Gamificação
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.target.getAttribute('href').substring(1);

                // Update active nav
                document.querySelectorAll('.nav-link').forEach(l => {
                    l.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                    l.classList.add('text-gray-500');
                });

                // Handle the clicked element
                const clickedElement = e.target;
                if (clickedElement) {
                    clickedElement.classList.remove('text-gray-500');
                    clickedElement.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
                }

                // Show/hide sections
                document.querySelectorAll('.section').forEach(section => {
                    section.classList.add('hidden');
                });

                const targetSection = document.getElementById(target);
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                }
            });
        });

        // Charts
        const ctx1 = document.getElementById('responsesChart').getContext('2d');
        new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
                datasets: [{
                    label: 'Respostas',
                    data: [12, 19, 15, 25, 22, 18, 24],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        const ctx2 = document.getElementById('npsChart').getContext('2d');
        new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: ['Promotores', 'Neutros', 'Detratores'],
                datasets: [{
                    data: [65, 25, 10],
                    backgroundColor: [
                        'rgb(34, 197, 94)',
                        'rgb(234, 179, 8)',
                        'rgb(239, 68, 68)'
                    ]
                }]
            },
            options: {
                responsive: true
            }
        });

        // API Status Check
        async function checkAPIStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                console.log('✅ API Status:', data);

                // Update status indicator
                const statusIndicator = document.createElement('div');
                statusIndicator.className = 'fixed top-4 right-4 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium';
                statusIndicator.innerHTML = '🟢 API Online';
                document.body.appendChild(statusIndicator);

                // Load real data
                loadDashboardData();
            } catch (error) {
                console.log('❌ API não disponível:', error);

                // Update status indicator
                const statusIndicator = document.createElement('div');
                statusIndicator.className = 'fixed top-4 right-4 bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium';
                statusIndicator.innerHTML = '🔴 API Offline';
                document.body.appendChild(statusIndicator);
            }
        }

        // Load real dashboard data
        async function loadDashboardData() {
            try {
                // Load surveys
                const surveysResponse = await fetch('/api/surveys');
                if (surveysResponse.ok) {
                    const surveysData = await surveysResponse.json();
                    document.getElementById('total-surveys').textContent = surveysData.surveys?.length || 0;
                    loadSurveysTable(surveysData.surveys || []);
                }

                // Load responses
                const responsesResponse = await fetch('/api/responses');
                if (responsesResponse.ok) {
                    const responsesData = await responsesResponse.json();
                    document.getElementById('total-responses').textContent = responsesData.responses?.length || 0;
                    loadResponsesTable(responsesData.responses || []);
                }

                // Load gamification stats
                const gamificationResponse = await fetch('/api/gamification/leaderboard');
                if (gamificationResponse.ok) {
                    const gamificationData = await gamificationResponse.json();
                    document.getElementById('active-users').textContent = gamificationData.leaderboard?.length || 0;
                }

            } catch (error) {
                console.log('Erro ao carregar dados:', error);
            }
        }

        // Load surveys table
        function loadSurveysTable(surveys) {
            const tbody = document.getElementById('surveys-table-body');
            if (!tbody) return;

            if (surveys.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                            <div class="text-4xl mb-2">📋</div>
                            <p>Nenhuma pesquisa encontrada</p>
                            <p class="text-sm mt-1">Clique em "Nova Pesquisa" para criar sua primeira pesquisa</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = surveys.map(survey => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${survey.name || 'Sem nome'}</div>
                        <div class="text-sm text-gray-500">${survey.description || 'Sem descrição'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            survey.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }">
                            ${survey.isActive ? 'Ativa' : 'Inativa'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${survey._count?.responses || 0}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${survey.createdAt ? new Date(survey.createdAt).toLocaleDateString('pt-BR') : 'N/A'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="editSurvey('${survey.id}')" class="text-blue-600 hover:text-blue-900 mr-3">Editar</button>
                        <button onclick="viewSurvey('${survey.id}')" class="text-green-600 hover:text-green-900 mr-3">Ver</button>
                        <button onclick="deleteSurvey('${survey.id}')" class="text-red-600 hover:text-red-900">Excluir</button>
                    </td>
                </tr>
            `).join('');
        }

        // Load responses table
        function loadResponsesTable(responses) {
            const tbody = document.getElementById('responses-table-body');
            if (!tbody) return;

            if (responses.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                            <div class="text-4xl mb-2">💬</div>
                            <p>Nenhuma resposta encontrada</p>
                            <p class="text-sm mt-1">As respostas aparecerão aqui quando forem recebidas</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = responses.map(response => {
                const score = response.score || 0;
                const type = score >= 9 ? 'Promotor' : score >= 7 ? 'Neutro' : 'Detrator';
                const typeColor = score >= 9 ? 'text-green-600' : score >= 7 ? 'text-yellow-600' : 'text-red-600';

                return `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${response.userPhone ? response.userPhone.replace(/(\d{2})(\d{5})(\d{4})/, '+55 ($1) $2-$3') : 'N/A'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-lg font-bold ${typeColor}">${score}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                score >= 9 ? 'bg-green-100 text-green-800' :
                                score >= 7 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                            }">
                                ${type}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                            ${response.feedback || 'Sem feedback'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${response.createdAt ? new Date(response.createdAt).toLocaleDateString('pt-BR') : 'N/A'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="viewResponse('${response.id}')" class="text-blue-600 hover:text-blue-900">Ver Detalhes</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Create Survey Modal
        function showCreateSurveyModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 w-full max-w-md">
                    <h3 class="text-lg font-semibold mb-4">Nova Pesquisa</h3>
                    <form id="survey-form">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Nome da Pesquisa</label>
                            <input type="text" id="survey-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                            <textarea id="survey-description" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3"></textarea>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancelar</button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">Criar Pesquisa</button>
                        </div>
                    </form>
                </div>
            `;
            document.body.appendChild(modal);

            // Handle form submission
            document.getElementById('survey-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                const name = document.getElementById('survey-name').value;
                const description = document.getElementById('survey-description').value;

                try {
                    const response = await fetch('/api/surveys', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name,
                            description,
                            isActive: true
                        })
                    });

                    if (response.ok) {
                        alert('Pesquisa criada com sucesso!');
                        closeModal();
                        loadDashboardData();
                    } else {
                        alert('Erro ao criar pesquisa');
                    }
                } catch (error) {
                    alert('Erro de conexão');
                }
            });
        }

        function closeModal() {
            const modal = document.querySelector('.fixed.inset-0');
            if (modal) modal.remove();
        }

        // API Testing Functions
        async function testAPI(endpoint) {
            const responseDiv = document.getElementById('api-response');
            responseDiv.innerHTML = '<div class="text-yellow-400">Carregando...</div>';

            try {
                const response = await fetch(endpoint);
                const data = await response.json();

                responseDiv.innerHTML = `
                    <div class="text-green-400 mb-2">✅ Status: ${response.status} ${response.statusText}</div>
                    <div class="text-blue-400 mb-2">📡 Endpoint: ${endpoint}</div>
                    <div class="text-white">${JSON.stringify(data, null, 2)}</div>
                `;
            } catch (error) {
                responseDiv.innerHTML = `
                    <div class="text-red-400 mb-2">❌ Erro de conexão</div>
                    <div class="text-white">${error.message}</div>
                `;
            }
        }

        async function createTestSurvey() {
            try {
                const response = await fetch('/api/surveys', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: `Pesquisa de Teste ${Date.now()}`,
                        description: 'Pesquisa criada automaticamente para teste',
                        isActive: true
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    alert('✅ Pesquisa de teste criada com sucesso!');
                    testAPI('/api/surveys');
                } else {
                    alert('❌ Erro ao criar pesquisa de teste');
                }
            } catch (error) {
                alert('❌ Erro de conexão');
            }
        }

        async function createTestResponse() {
            try {
                const response = await fetch('/api/responses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userPhone: `+5511${Math.floor(Math.random() * 100000000)}`,
                        score: Math.floor(Math.random() * 11),
                        feedback: 'Feedback de teste gerado automaticamente',
                        surveyId: null
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    alert('✅ Resposta de teste criada com sucesso!');
                    testAPI('/api/responses');
                } else {
                    alert('❌ Erro ao criar resposta de teste');
                }
            } catch (error) {
                alert('❌ Erro de conexão');
            }
        }

        async function testGamification() {
            const testPhone = `+5511${Math.floor(Math.random() * 100000000)}`;

            try {
                const response = await fetch('/api/gamification/process-response', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userPhone: testPhone,
                        score: 9,
                        feedback: 'Teste de gamificação',
                        isFirstResponse: true
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    alert('✅ Gamificação testada com sucesso!');
                    document.getElementById('api-response').innerHTML = `
                        <div class="text-green-400 mb-2">✅ Gamificação Processada</div>
                        <div class="text-blue-400 mb-2">📱 Telefone: ${testPhone}</div>
                        <div class="text-white">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    alert('❌ Erro ao testar gamificação');
                }
            } catch (error) {
                alert('❌ Erro de conexão');
            }
        }

        // Initialize
        checkAPIStatus();
    </script>
</body>
</html>
