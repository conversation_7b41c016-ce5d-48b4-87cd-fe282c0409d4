const express = require('express');
const { authenticate } = require('../middleware/auth');
const { validate } = require('../utils/validation');
const {
  getNotifications,
  markAsRead,
  markAllAsRead,
  getUnreadCount,
  createPushSubscription,
  removePushSubscription,
  getVapidPublicKey,
  testPushNotification,
  getNotificationSettings,
  updateNotificationSettings,
  getNotificationStats
} = require('../controllers/notificationController');

const router = express.Router();

// Todas as rotas requerem autenticação
router.use(authenticate);

// Obter chaves VAPID (rota pública para PWA)
router.get('/vapid-public-key', getVapidPublicKey);

// Obter notificações do usuário
router.get('/', 
  validate(require('joi').object({
    page: require('joi').number().integer().min(1).default(1),
    limit: require('joi').number().integer().min(1).max(100).default(20),
    unreadOnly: require('joi').boolean().default(false),
    type: require('joi').string().valid(
      'new_response', 
      'detractor_alert', 
      'new_message', 
      'survey_completed',
      'system_alert',
      'test'
    ).optional()
  }).options({ allowUnknown: true })),
  getNotifications
);

// Obter contagem de não lidas
router.get('/unread-count', getUnreadCount);

// Obter estatísticas
router.get('/stats', 
  validate(require('joi').object({
    period: require('joi').string().valid('1d', '7d', '30d', '90d').default('7d')
  }).options({ allowUnknown: true })),
  getNotificationStats
);

// Marcar notificação como lida
router.patch('/:id/read', 
  validate(require('joi').object({
    id: require('joi').string().uuid().required()
  })),
  markAsRead
);

// Marcar todas como lidas
router.patch('/read-all', markAllAsRead);

// Push Notifications
router.post('/push-subscription', 
  validate(require('joi').object({
    endpoint: require('joi').string().uri().required(),
    keys: require('joi').object({
      p256dh: require('joi').string().required(),
      auth: require('joi').string().required()
    }).required()
  })),
  createPushSubscription
);

router.delete('/push-subscription', 
  validate(require('joi').object({
    endpoint: require('joi').string().uri().required()
  })),
  removePushSubscription
);

// Testar notificação push
router.post('/test-push', 
  validate(require('joi').object({
    title: require('joi').string().max(100).default('Teste'),
    message: require('joi').string().max(500).default('Esta é uma notificação de teste')
  })),
  testPushNotification
);

// Configurações de notificação
router.get('/settings', getNotificationSettings);

router.put('/settings', 
  validate(require('joi').object({
    emailNotifications: require('joi').boolean().optional(),
    pushNotifications: require('joi').boolean().optional(),
    websocketNotifications: require('joi').boolean().optional(),
    detractorAlerts: require('joi').boolean().optional(),
    newResponseAlerts: require('joi').boolean().optional(),
    newMessageAlerts: require('joi').boolean().optional(),
    surveyCompletedAlerts: require('joi').boolean().optional(),
    quietHoursEnabled: require('joi').boolean().optional(),
    quietHoursStart: require('joi').string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    quietHoursEnd: require('joi').string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional()
  })),
  updateNotificationSettings
);

// Webhook para receber confirmações de entrega de push notifications
router.post('/push-delivery-receipt', async (req, res) => {
  try {
    const { notificationId, status, timestamp } = req.body;
    const { logger } = require('../utils/logger');
    
    logger.info('Confirmação de entrega de push notification:', {
      notificationId,
      status,
      timestamp
    });

    // Aqui você pode atualizar estatísticas de entrega
    // ou tomar ações baseadas no status de entrega

    res.json({ message: 'Confirmação recebida' });
  } catch (error) {
    logger.error('Erro ao processar confirmação de entrega:', error);
    res.status(500).json({ error: 'Erro interno' });
  }
});

// Rota para service worker registrar eventos de notificação
router.post('/notification-clicked', async (req, res) => {
  try {
    const { notificationId, action, timestamp } = req.body;
    const { logger } = require('../utils/logger');
    
    logger.info('Notificação clicada:', {
      notificationId,
      action,
      timestamp,
      userId: req.user.id
    });

    // Marcar notificação como lida se foi clicada
    if (notificationId) {
      const notificationService = require('../services/notificationService');
      await notificationService.markAsRead(notificationId, req.user.id);
    }

    res.json({ message: 'Evento registrado' });
  } catch (error) {
    logger.error('Erro ao processar clique em notificação:', error);
    res.status(500).json({ error: 'Erro interno' });
  }
});

// Limpar notificações antigas (apenas admins)
router.delete('/cleanup', 
  require('../middleware/auth').authorize('ADMIN'),
  validate(require('joi').object({
    daysToKeep: require('joi').number().integer().min(1).max(365).default(30)
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const { daysToKeep = 30 } = req.query;
      const notificationService = require('../services/notificationService');
      
      const deletedCount = await notificationService.cleanupOldNotifications(parseInt(daysToKeep));
      
      res.json({
        message: 'Limpeza de notificações concluída',
        deletedCount,
        daysToKeep: parseInt(daysToKeep)
      });
    } catch (error) {
      logger.error('Erro na limpeza de notificações:', error);
      res.status(500).json({ error: 'Erro interno' });
    }
  }
);

// Obter estatísticas globais de notificações (apenas admins)
router.get('/admin/stats', 
  require('../middleware/auth').authorize('ADMIN'),
  async (req, res) => {
    try {
      const { period = '7d' } = req.query;
      const database = require('../config/database');
      
      // Calcular data de início
      const now = new Date();
      const periodDays = { '1d': 1, '7d': 7, '30d': 30, '90d': 90 };
      const days = periodDays[period] || 7;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      const [
        totalNotifications,
        notificationsByType,
        notificationsByUser,
        deliveryStats
      ] = await Promise.all([
        database.getClient().notification.count({
          where: { createdAt: { gte: startDate } }
        }),
        database.getClient().notification.groupBy({
          by: ['type'],
          where: { createdAt: { gte: startDate } },
          _count: { id: true },
          orderBy: { _count: { id: 'desc' } }
        }),
        database.getClient().notification.groupBy({
          by: ['userId'],
          where: { createdAt: { gte: startDate } },
          _count: { id: true },
          orderBy: { _count: { id: 'desc' } },
          take: 10
        }),
        database.getClient().pushSubscription.count({
          where: { isActive: true }
        })
      ]);

      res.json({
        period: { start: startDate, end: now, days },
        summary: {
          totalNotifications,
          activeSubscriptions: deliveryStats
        },
        byType: notificationsByType.map(item => ({
          type: item.type,
          count: item._count.id
        })),
        topUsers: notificationsByUser.map(item => ({
          userId: item.userId,
          count: item._count.id
        }))
      });
    } catch (error) {
      logger.error('Erro ao obter estatísticas de notificações:', error);
      res.status(500).json({ error: 'Erro interno' });
    }
  }
);

module.exports = router;
