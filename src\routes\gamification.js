const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');
const { validate } = require('../utils/validation');
const { apiRateLimit, customRateLimit } = require('../middleware/security');
const Joi = require('joi');
const {
  getUserProfile,
  getLeaderboard,
  getAvailableBadges,
  getAvailableAchievements,
  processResponse,
  getRewards,
  redeemReward,
  getUserRedemptions,
  getActiveChallenges,
  joinChallenge,
  getUserChallenges,
  getSystemStats,
  simulateBadge
} = require('../controllers/gamificationController');

const router = express.Router();

// Aplicar rate limiting
router.use(apiRateLimit);

// Rotas públicas (para integração com WhatsApp)

// Obter perfil de gamificação por telefone
router.get('/profile/:userPhone',
  validate(Joi.object({
    userPhone: Joi.string().required()
  })),
  getUserProfile
);

// Obter ranking público
router.get('/leaderboard',
  validate(Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(10),
    period: Joi.string().valid('all', 'week', 'month').default('all')
  }).options({ allowUnknown: true })),
  getLeaderboard
);

// Obter badges disponíveis
router.get('/badges', getAvailableBadges);

// Obter conquistas disponíveis
router.get('/achievements', getAvailableAchievements);

// Processar resposta (webhook)
router.post('/process-response',
  customRateLimit('gamification_process', 100, 60000), // 100 por minuto
  validate(Joi.object({
    userPhone: Joi.string().required(),
    score: Joi.number().integer().min(0).max(10).required(),
    feedback: Joi.string().optional(),
    surveyId: Joi.string().uuid().optional(),
    isFirstResponse: Joi.boolean().default(false)
  })),
  processResponse
);

// Rotas autenticadas
router.use(authenticate);

// Recompensas

// Obter recompensas disponíveis
router.get('/rewards',
  validate(Joi.object({
    isActive: Joi.boolean().default(true),
    validUntil: Joi.boolean().optional()
  }).options({ allowUnknown: true })),
  getRewards
);

// Resgatar recompensa
router.post('/rewards/redeem',
  customRateLimit('reward_redeem', 5, 60000), // 5 resgates por minuto
  validate(Joi.object({
    userPhone: Joi.string().required(),
    rewardId: Joi.string().uuid().required()
  })),
  redeemReward
);

// Obter histórico de resgates
router.get('/redemptions/:userPhone',
  validate(Joi.object({
    userPhone: Joi.string().required(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }).options({ allowUnknown: true })),
  getUserRedemptions
);

// Desafios

// Obter desafios ativos
router.get('/challenges', getActiveChallenges);

// Participar de desafio
router.post('/challenges/join',
  validate(Joi.object({
    userPhone: Joi.string().required(),
    challengeId: Joi.string().uuid().required()
  })),
  joinChallenge
);

// Obter progresso em desafios
router.get('/challenges/:userPhone',
  validate(Joi.object({
    userPhone: Joi.string().required()
  })),
  getUserChallenges
);

// Rotas administrativas

// Obter estatísticas do sistema
router.get('/admin/stats',
  authorize('ADMIN'),
  getSystemStats
);

// Criar recompensa
router.post('/admin/rewards',
  authorize('ADMIN'),
  validate(Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    type: Joi.string().valid('discount', 'gift', 'access', 'service').required(),
    value: Joi.string().required(),
    pointsCost: Joi.number().integer().min(1).required(),
    validUntil: Joi.date().optional(),
    maxRedemptions: Joi.number().integer().min(1).optional()
  })),
  async (req, res) => {
    try {
      const database = require('../config/database');
      
      const reward = await database.getClient().reward.create({
        data: req.body
      });

      res.status(201).json({
        message: 'Recompensa criada com sucesso',
        reward
      });
    } catch (error) {
      logger.error('Erro ao criar recompensa:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Atualizar recompensa
router.put('/admin/rewards/:rewardId',
  authorize('ADMIN'),
  validate(Joi.object({
    rewardId: Joi.string().uuid().required(),
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    type: Joi.string().valid('discount', 'gift', 'access', 'service').optional(),
    value: Joi.string().optional(),
    pointsCost: Joi.number().integer().min(1).optional(),
    isActive: Joi.boolean().optional(),
    validUntil: Joi.date().optional(),
    maxRedemptions: Joi.number().integer().min(1).optional()
  })),
  async (req, res) => {
    try {
      const { rewardId } = req.params;
      const database = require('../config/database');
      
      const reward = await database.getClient().reward.update({
        where: { id: rewardId },
        data: req.body
      });

      res.json({
        message: 'Recompensa atualizada com sucesso',
        reward
      });
    } catch (error) {
      if (error.code === 'P2025') {
        return res.status(404).json({
          error: 'Recompensa não encontrada',
          message: 'A recompensa especificada não existe'
        });
      }
      
      logger.error('Erro ao atualizar recompensa:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Criar desafio
router.post('/admin/challenges',
  authorize('ADMIN'),
  validate(Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    type: Joi.string().valid('daily', 'weekly', 'monthly', 'special').required(),
    target: Joi.number().integer().min(1).required(),
    reward: Joi.number().integer().min(1).required(),
    startDate: Joi.date().required(),
    endDate: Joi.date().required()
  })),
  async (req, res) => {
    try {
      const database = require('../config/database');
      
      // Validar datas
      if (new Date(req.body.startDate) >= new Date(req.body.endDate)) {
        return res.status(400).json({
          error: 'Datas inválidas',
          message: 'Data de início deve ser anterior à data de fim'
        });
      }

      const challenge = await database.getClient().challenge.create({
        data: req.body
      });

      res.status(201).json({
        message: 'Desafio criado com sucesso',
        challenge
      });
    } catch (error) {
      logger.error('Erro ao criar desafio:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Obter todos os resgates (admin)
router.get('/admin/redemptions',
  authorize('ADMIN'),
  validate(Joi.object({
    status: Joi.string().valid('pending', 'approved', 'delivered', 'cancelled').optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(200).default(50)
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const { status, page, limit } = req.query;
      const database = require('../config/database');

      const whereClause = status ? { status } : {};

      const [redemptions, total] = await Promise.all([
        database.getClient().rewardRedemption.findMany({
          where: whereClause,
          include: {
            reward: {
              select: {
                name: true,
                description: true,
                type: true,
                value: true
              }
            }
          },
          orderBy: { redeemedAt: 'desc' },
          skip: (parseInt(page) - 1) * parseInt(limit),
          take: parseInt(limit)
        }),
        database.getClient().rewardRedemption.count({ where: whereClause })
      ]);

      res.json({
        redemptions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      logger.error('Erro ao obter resgates:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Atualizar status de resgate
router.patch('/admin/redemptions/:redemptionId',
  authorize('ADMIN'),
  validate(Joi.object({
    redemptionId: Joi.string().uuid().required(),
    status: Joi.string().valid('pending', 'approved', 'delivered', 'cancelled').required()
  })),
  async (req, res) => {
    try {
      const { redemptionId } = req.params;
      const { status } = req.body;
      const database = require('../config/database');

      const updateData = { status };
      if (status === 'delivered') {
        updateData.deliveredAt = new Date();
      }

      const redemption = await database.getClient().rewardRedemption.update({
        where: { id: redemptionId },
        data: updateData,
        include: {
          reward: {
            select: {
              name: true,
              description: true
            }
          }
        }
      });

      res.json({
        message: 'Status do resgate atualizado com sucesso',
        redemption
      });
    } catch (error) {
      if (error.code === 'P2025') {
        return res.status(404).json({
          error: 'Resgate não encontrado',
          message: 'O resgate especificado não existe'
        });
      }
      
      logger.error('Erro ao atualizar resgate:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Simular conquista de badge (desenvolvimento)
router.post('/admin/simulate-badge',
  authorize('ADMIN'),
  validate(Joi.object({
    userPhone: Joi.string().required(),
    badgeId: Joi.string().required()
  })),
  simulateBadge
);

// Conceder pontos manualmente
router.post('/admin/grant-points',
  authorize('ADMIN'),
  validate(Joi.object({
    userPhone: Joi.string().required(),
    points: Joi.number().integer().min(1).required(),
    reason: Joi.string().required()
  })),
  async (req, res) => {
    try {
      const { userPhone, points, reason } = req.body;
      const gamificationService = require('../services/gamificationService');

      await gamificationService.updateUserPoints(userPhone, points);

      res.json({
        message: 'Pontos concedidos com sucesso',
        userPhone,
        pointsGranted: points,
        reason
      });
    } catch (error) {
      logger.error('Erro ao conceder pontos:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

// Relatório de gamificação
router.get('/admin/report',
  authorize('ADMIN'),
  validate(Joi.object({
    period: Joi.string().valid('7d', '30d', '90d').default('30d')
  }).options({ allowUnknown: true })),
  async (req, res) => {
    try {
      const { period } = req.query;
      const database = require('../config/database');

      // Calcular período
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90 }[period] || 30;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      const [
        totalUsers,
        activeUsers,
        totalPoints,
        totalBadges,
        totalRedemptions,
        topUsers
      ] = await Promise.all([
        database.getClient().gamificationProfile.count(),
        database.getClient().gamificationProfile.count({
          where: {
            lastResponseAt: { gte: startDate }
          }
        }),
        database.getClient().gamificationProfile.aggregate({
          _sum: { totalPoints: true }
        }),
        database.getClient().userBadge.count({
          where: {
            earnedAt: { gte: startDate }
          }
        }),
        database.getClient().rewardRedemption.count({
          where: {
            redeemedAt: { gte: startDate }
          }
        }),
        database.getClient().gamificationProfile.findMany({
          orderBy: { totalPoints: 'desc' },
          take: 5,
          select: {
            userPhone: true,
            totalPoints: true,
            currentLevel: true,
            totalResponses: true
          }
        })
      ]);

      res.json({
        period: { start: startDate, end: now, days },
        summary: {
          totalUsers,
          activeUsers,
          totalPoints: totalPoints._sum.totalPoints || 0,
          averagePointsPerUser: totalUsers > 0 ? Math.round((totalPoints._sum.totalPoints || 0) / totalUsers) : 0,
          badgesEarned: totalBadges,
          rewardsRedeemed: totalRedemptions
        },
        topUsers,
        engagement: {
          activeUserRate: totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0
        }
      });
    } catch (error) {
      logger.error('Erro ao gerar relatório:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
);

module.exports = router;
