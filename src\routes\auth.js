const express = require('express');
const rateLimit = require('express-rate-limit');
const { validate, authSchemas } = require('../utils/validation');
const { authenticate } = require('../middleware/auth');
const {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  refreshToken
} = require('../controllers/authController');

const router = express.Router();

// Rate limiting específico para autenticação
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 tentativas por IP
  message: {
    error: 'Muitas tentativas de login',
    message: 'Tente novamente em 15 minutos'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hora
  max: 3, // máximo 3 registros por IP por hora
  message: {
    error: 'Muitas tentativas de registro',
    message: 'Tente novamente em 1 hora'
  }
});

// Rotas públicas
router.post('/register', 
  registerLimiter,
  validate(authSchemas.register),
  register
);

router.post('/login', 
  authLimiter,
  validate(authSchemas.login),
  login
);

// Rotas protegidas
router.get('/profile', authenticate, getProfile);

router.put('/profile', 
  authenticate,
  validate(authSchemas.register.fork(['password'], (schema) => schema.optional())),
  updateProfile
);

router.post('/change-password',
  authenticate,
  validate(require('joi').object({
    currentPassword: require('joi').string().required().messages({
      'any.required': 'Senha atual é obrigatória'
    }),
    newPassword: require('joi').string().min(6).max(100).required().messages({
      'string.min': 'Nova senha deve ter pelo menos 6 caracteres',
      'string.max': 'Nova senha deve ter no máximo 100 caracteres',
      'any.required': 'Nova senha é obrigatória'
    })
  })),
  changePassword
);

router.post('/refresh-token', authenticate, refreshToken);

// Rota para logout (opcional - pode ser feito apenas no frontend)
router.post('/logout', authenticate, (req, res) => {
  // Em uma implementação mais robusta, você poderia:
  // 1. Adicionar o token a uma blacklist
  // 2. Invalidar refresh tokens
  // 3. Limpar cookies de sessão
  
  res.json({
    message: 'Logout realizado com sucesso'
  });
});

module.exports = router;
