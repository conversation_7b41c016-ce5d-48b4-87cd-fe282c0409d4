const { asyncHandler } = require('../middleware/errorHandler');
const analyticsService = require('../services/analyticsService');
const mlService = require('../services/mlService');
const database = require('../config/database');
const { logger } = require('../utils/logger');
const path = require('path');

// Gerar relatório avançado
const generateAdvancedReport = asyncHandler(async (req, res) => {
  const {
    surveyId,
    startDate,
    endDate,
    department,
    includeML = true,
    format = 'json'
  } = req.query;

  // Validar datas
  if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
    return res.status(400).json({
      error: 'Data inválida',
      message: 'Data de início deve ser anterior à data de fim'
    });
  }

  const report = await analyticsService.generateAdvancedReport({
    surveyId,
    startDate,
    endDate,
    department,
    includeML: includeML === 'true',
    format,
    filters: req.query.filters ? JSON.parse(req.query.filters) : {}
  });

  // Se for exportação, retornar informações do arquivo
  if (format !== 'json') {
    return res.json({
      message: 'Relatório gerado com sucesso',
      file: report,
      downloadUrl: `/api/analytics/download/${path.basename(report.filename)}`
    });
  }

  res.json(report);
});

// Obter predições de satisfação
const getSatisfactionPredictions = asyncHandler(async (req, res) => {
  if (!mlService.isAvailable()) {
    return res.status(503).json({
      error: 'ML não disponível',
      message: 'Serviço de Machine Learning não está disponível'
    });
  }

  const { userPhone, limit = 10 } = req.query;

  if (userPhone) {
    // Predição para cliente específico
    const prediction = await mlService.predictSatisfaction({ userPhone });
    return res.json({ prediction });
  }

  // Predições para múltiplos clientes
  const responses = await database.getClient().response.findMany({
    select: { userPhone: true },
    distinct: ['userPhone'],
    take: parseInt(limit),
    orderBy: { createdAt: 'desc' }
  });

  const predictions = [];
  for (const { userPhone } of responses) {
    try {
      const prediction = await mlService.predictSatisfaction({ userPhone });
      predictions.push({
        userPhone,
        ...prediction
      });
    } catch (error) {
      logger.warn(`Erro na predição para ${userPhone}:`, error);
    }
  }

  res.json({
    predictions,
    total: predictions.length,
    mlStatus: mlService.getModelsStatus()
  });
});

// Obter análise de churn
const getChurnAnalysis = asyncHandler(async (req, res) => {
  if (!mlService.isAvailable()) {
    return res.status(503).json({
      error: 'ML não disponível',
      message: 'Serviço de Machine Learning não está disponível'
    });
  }

  const { userPhone, riskLevel, limit = 20 } = req.query;

  if (userPhone) {
    // Análise para cliente específico
    const analysis = await mlService.predictChurn({ userPhone });
    return res.json({ analysis });
  }

  // Análise para múltiplos clientes
  const responses = await database.getClient().response.findMany({
    select: { userPhone: true },
    distinct: ['userPhone'],
    take: parseInt(limit),
    orderBy: { createdAt: 'desc' }
  });

  const analyses = [];
  for (const { userPhone } of responses) {
    try {
      const analysis = await mlService.predictChurn({ userPhone });
      
      // Filtrar por nível de risco se especificado
      if (!riskLevel || analysis.riskLevel === riskLevel.toUpperCase()) {
        analyses.push({
          userPhone,
          ...analysis
        });
      }
    } catch (error) {
      logger.warn(`Erro na análise de churn para ${userPhone}:`, error);
    }
  }

  // Ordenar por probabilidade de churn (maior primeiro)
  analyses.sort((a, b) => b.churnProbability - a.churnProbability);

  res.json({
    analyses,
    total: analyses.length,
    summary: {
      high: analyses.filter(a => a.riskLevel === 'HIGH').length,
      medium: analyses.filter(a => a.riskLevel === 'MEDIUM').length,
      low: analyses.filter(a => a.riskLevel === 'LOW').length
    }
  });
});

// Obter análise de sentimento
const getSentimentAnalysis = asyncHandler(async (req, res) => {
  const { text, surveyId, startDate, endDate } = req.query;

  if (text) {
    // Análise de texto específico
    const sentiment = mlService.analyzeSentiment(text);
    return res.json({ sentiment });
  }

  // Análise de sentimento de feedbacks
  const whereClause = {
    feedback: { not: null },
    ...(surveyId && { surveyId }),
    ...(startDate && endDate && {
      createdAt: {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    })
  };

  const responses = await database.getClient().response.findMany({
    where: whereClause,
    select: {
      id: true,
      feedback: true,
      score: true,
      userPhone: true,
      createdAt: true
    },
    orderBy: { createdAt: 'desc' },
    take: 100
  });

  const sentimentAnalysis = responses.map(response => ({
    responseId: response.id,
    userPhone: response.userPhone,
    score: response.score,
    feedback: response.feedback,
    sentiment: mlService.analyzeSentiment(response.feedback),
    createdAt: response.createdAt
  }));

  // Calcular estatísticas
  const totalAnalyzed = sentimentAnalysis.length;
  const positive = sentimentAnalysis.filter(s => s.sentiment.sentiment === 'positive').length;
  const negative = sentimentAnalysis.filter(s => s.sentiment.sentiment === 'negative').length;
  const neutral = sentimentAnalysis.filter(s => s.sentiment.sentiment === 'neutral').length;

  res.json({
    analysis: sentimentAnalysis,
    summary: {
      total: totalAnalyzed,
      positive,
      negative,
      neutral,
      positiveRate: Math.round((positive / totalAnalyzed) * 100),
      negativeRate: Math.round((negative / totalAnalyzed) * 100)
    }
  });
});

// Obter análise de padrões por segmento
const getSegmentPatterns = asyncHandler(async (req, res) => {
  if (!mlService.isAvailable()) {
    return res.status(503).json({
      error: 'ML não disponível',
      message: 'Serviço de Machine Learning não está disponível'
    });
  }

  const {
    department,
    timeRange = '30d',
    minResponses = 5
  } = req.query;

  const patterns = await mlService.analyzeSegmentPatterns({
    department,
    timeRange,
    minResponses: parseInt(minResponses)
  });

  res.json(patterns);
});

// Obter métricas em tempo real
const getRealTimeMetrics = asyncHandler(async (req, res) => {
  const { period = '24h' } = req.query;

  // Calcular período
  const now = new Date();
  const hours = { '1h': 1, '6h': 6, '24h': 24, '7d': 168 }[period] || 24;
  const startDate = new Date(now.getTime() - hours * 60 * 60 * 1000);

  // Buscar dados recentes
  const [recentResponses, recentConversations] = await Promise.all([
    database.getClient().response.findMany({
      where: {
        createdAt: { gte: startDate }
      },
      orderBy: { createdAt: 'desc' }
    }),
    database.getClient().conversation.findMany({
      where: {
        createdAt: { gte: startDate }
      },
      orderBy: { createdAt: 'desc' }
    })
  ]);

  // Calcular métricas
  const totalResponses = recentResponses.length;
  const promoters = recentResponses.filter(r => r.score >= 9).length;
  const detractors = recentResponses.filter(r => r.score <= 6).length;
  const npsScore = totalResponses > 0 ? Math.round(((promoters - detractors) / totalResponses) * 100) : 0;
  const averageScore = totalResponses > 0 ? recentResponses.reduce((sum, r) => sum + r.score, 0) / totalResponses : 0;

  // Conversas por status
  const conversationsByStatus = {
    open: recentConversations.filter(c => c.status === 'OPEN').length,
    inProgress: recentConversations.filter(c => c.status === 'IN_PROGRESS').length,
    resolved: recentConversations.filter(c => c.status === 'RESOLVED').length,
    closed: recentConversations.filter(c => c.status === 'CLOSED').length
  };

  // Tendência (comparar com período anterior)
  const previousStartDate = new Date(startDate.getTime() - hours * 60 * 60 * 1000);
  const previousResponses = await database.getClient().response.findMany({
    where: {
      createdAt: {
        gte: previousStartDate,
        lt: startDate
      }
    }
  });

  const previousNPS = previousResponses.length > 0 
    ? Math.round(((previousResponses.filter(r => r.score >= 9).length - previousResponses.filter(r => r.score <= 6).length) / previousResponses.length) * 100)
    : 0;

  const npsTrend = npsScore - previousNPS;

  res.json({
    period: {
      start: startDate,
      end: now,
      hours
    },
    metrics: {
      totalResponses,
      npsScore,
      npsTrend,
      averageScore: Math.round(averageScore * 100) / 100,
      promoters,
      detractors,
      promoterRate: Math.round((promoters / Math.max(totalResponses, 1)) * 100),
      detractorRate: Math.round((detractors / Math.max(totalResponses, 1)) * 100)
    },
    conversations: {
      total: recentConversations.length,
      byStatus: conversationsByStatus,
      highPriority: recentConversations.filter(c => c.priority === 'HIGH').length
    },
    lastUpdated: now.toISOString()
  });
});

// Obter comparação temporal
const getTemporalComparison = asyncHandler(async (req, res) => {
  const {
    currentPeriod = '30d',
    comparisonPeriod = '30d',
    surveyId
  } = req.query;

  // Calcular períodos
  const now = new Date();
  const periodDays = { '7d': 7, '30d': 30, '90d': 90, '365d': 365 };
  
  const currentDays = periodDays[currentPeriod] || 30;
  const comparisonDays = periodDays[comparisonPeriod] || 30;
  
  const currentStart = new Date(now.getTime() - currentDays * 24 * 60 * 60 * 1000);
  const comparisonStart = new Date(currentStart.getTime() - comparisonDays * 24 * 60 * 60 * 1000);

  // Buscar dados
  const whereClause = surveyId ? { surveyId } : {};
  
  const [currentData, comparisonData] = await Promise.all([
    database.getClient().response.findMany({
      where: {
        ...whereClause,
        createdAt: { gte: currentStart }
      }
    }),
    database.getClient().response.findMany({
      where: {
        ...whereClause,
        createdAt: {
          gte: comparisonStart,
          lt: currentStart
        }
      }
    })
  ]);

  // Calcular métricas para ambos os períodos
  const calculateMetrics = (responses) => {
    const total = responses.length;
    const promoters = responses.filter(r => r.score >= 9).length;
    const detractors = responses.filter(r => r.score <= 6).length;
    const nps = total > 0 ? Math.round(((promoters - detractors) / total) * 100) : 0;
    const avgScore = total > 0 ? responses.reduce((sum, r) => sum + r.score, 0) / total : 0;

    return {
      totalResponses: total,
      npsScore: nps,
      averageScore: Math.round(avgScore * 100) / 100,
      promoters,
      detractors,
      promoterRate: Math.round((promoters / Math.max(total, 1)) * 100),
      detractorRate: Math.round((detractors / Math.max(total, 1)) * 100)
    };
  };

  const current = calculateMetrics(currentData);
  const comparison = calculateMetrics(comparisonData);

  // Calcular diferenças
  const changes = {
    totalResponses: current.totalResponses - comparison.totalResponses,
    npsScore: current.npsScore - comparison.npsScore,
    averageScore: Math.round((current.averageScore - comparison.averageScore) * 100) / 100,
    promoters: current.promoters - comparison.promoters,
    detractors: current.detractors - comparison.detractors,
    promoterRate: current.promoterRate - comparison.promoterRate,
    detractorRate: current.detractorRate - comparison.detractorRate
  };

  res.json({
    periods: {
      current: {
        start: currentStart,
        end: now,
        days: currentDays
      },
      comparison: {
        start: comparisonStart,
        end: currentStart,
        days: comparisonDays
      }
    },
    metrics: {
      current,
      comparison,
      changes
    },
    insights: generateComparisonInsights(changes)
  });
});

// Gerar insights de comparação
function generateComparisonInsights(changes) {
  const insights = [];

  if (Math.abs(changes.npsScore) >= 5) {
    insights.push({
      type: changes.npsScore > 0 ? 'positive' : 'negative',
      message: `NPS ${changes.npsScore > 0 ? 'aumentou' : 'diminuiu'} ${Math.abs(changes.npsScore)} pontos`
    });
  }

  if (Math.abs(changes.totalResponses) >= 10) {
    insights.push({
      type: changes.totalResponses > 0 ? 'positive' : 'neutral',
      message: `${Math.abs(changes.totalResponses)} ${changes.totalResponses > 0 ? 'mais' : 'menos'} respostas no período`
    });
  }

  if (Math.abs(changes.averageScore) >= 0.5) {
    insights.push({
      type: changes.averageScore > 0 ? 'positive' : 'negative',
      message: `Satisfação média ${changes.averageScore > 0 ? 'aumentou' : 'diminuiu'} ${Math.abs(changes.averageScore)} pontos`
    });
  }

  return insights;
}

// Download de arquivo exportado
const downloadFile = asyncHandler(async (req, res) => {
  const { filename } = req.params;
  const filepath = path.join(process.cwd(), 'temp', filename);

  try {
    await require('fs').promises.access(filepath);
    res.download(filepath, filename, (err) => {
      if (err) {
        logger.error('Erro no download:', err);
        res.status(404).json({ error: 'Arquivo não encontrado' });
      }
    });
  } catch (error) {
    res.status(404).json({ error: 'Arquivo não encontrado' });
  }
});

// Obter status do ML
const getMLStatus = asyncHandler(async (req, res) => {
  const status = mlService.getModelsStatus();
  
  res.json({
    ...status,
    capabilities: {
      satisfactionPrediction: status.initialized,
      churnAnalysis: status.initialized,
      sentimentAnalysis: true, // Sempre disponível
      segmentAnalysis: status.initialized
    }
  });
});

module.exports = {
  generateAdvancedReport,
  getSatisfactionPredictions,
  getChurnAnalysis,
  getSentimentAnalysis,
  getSegmentPatterns,
  getRealTimeMetrics,
  getTemporalComparison,
  downloadFile,
  getMLStatus
};
