@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

/* Reset básico */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", system-ui, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  line-height: 1.6;
}

/* Utilitários básicos */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

/* Cores */
.bg-white {
  background-color: white;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.bg-green-600 {
  background-color: #16a34a;
}

.bg-red-600 {
  background-color: #dc2626;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.text-white {
  color: white;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-900 {
  color: #111827;
}

.text-blue-600 {
  color: #2563eb;
}

/* Tamanhos */
.w-full {
  width: 100%;
}

.h-64 {
  height: 16rem;
}

.h-12 {
  height: 3rem;
}

.w-12 {
  width: 3rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

/* Padding e Margin */
.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

/* Bordas */
.rounded {
  border-radius: 0.25rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.border {
  border-width: 1px;
  border-color: #e5e7eb;
}

.border-b {
  border-bottom-width: 1px;
  border-color: #e5e7eb;
}

/* Sombras */
.shadow {
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Texto */
.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-center {
  text-align: center;
}

/* Botões */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

/* Animações */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsivo */
@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@layer components {
  /* Botões */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }

  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-xl {
    @apply px-8 py-4 text-lg;
  }

  /* Cards */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl;
  }

  /* Formulários */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-textarea {
    @apply form-input resize-none;
  }

  .form-select {
    @apply form-input pr-10 bg-white;
  }

  .form-checkbox {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded;
  }

  .form-radio {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }

  .form-help {
    @apply text-sm text-gray-500 mt-1;
  }

  /* Estados */
  .loading {
    @apply opacity-50 pointer-events-none;
  }

  .disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }

  /* Alertas */
  .alert {
    @apply p-4 rounded-lg border;
  }

  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }

  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }

  .alert-danger {
    @apply alert bg-danger-50 border-danger-200 text-danger-800;
  }

  .alert-info {
    @apply alert bg-primary-50 border-primary-200 text-primary-800;
  }

  /* Tabelas */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Navegação */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }

  .nav-link-inactive {
    @apply nav-link text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }

  /* Modais */
  .modal-overlay {
    @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-40;
  }

  .modal-container {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .modal-content {
    @apply flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0;
  }

  .modal-panel {
    @apply relative transform overflow-hidden rounded-xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg;
  }

  /* Dropdown */
  .dropdown-menu {
    @apply absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150;
  }

  /* Skeleton Loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .skeleton-text {
    @apply skeleton h-4 w-full mb-2;
  }

  .skeleton-title {
    @apply skeleton h-6 w-3/4 mb-4;
  }

  .skeleton-avatar {
    @apply skeleton h-10 w-10 rounded-full;
  }

  /* Scrollbar personalizada */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}
