const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const rateLimit = require("express-rate-limit");
const http = require("http");
require("dotenv").config();

// Importar middlewares de segurança
const {
  advancedSecurity,
  apiRateLimit,
  speedLimiter,
  suspiciousActivityDetector,
} = require("./src/middleware/security");

const authRoutes = require("./src/routes/auth");
const surveyRoutes = require("./src/routes/surveys");
const responseRoutes = require("./src/routes/responses");
const whatsappRoutes = require("./src/routes/whatsapp");
const conversationRoutes = require("./src/routes/conversations");
const webhookRoutes = require("./src/routes/webhooks");
const departmentRoutes = require("./src/routes/departments");
const notificationRoutes = require("./src/routes/notifications");
const analyticsRoutes = require("./src/routes/analytics");
const automationRoutes = require("./src/routes/automation");
const securityRoutes = require("./src/routes/security");
const gamificationRoutes = require("./src/routes/gamification");

const { errorHandler } = require("./src/middleware/errorHandler");
const { logger } = require("./src/utils/logger");

// Importar serviços
const websocketService = require("./src/services/websocketService");
const notificationService = require("./src/services/notificationService");
const mlService = require("./src/services/mlService");
const automationService = require("./src/services/automationService");
const gamificationService = require("./src/services/gamificationService");

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3000;

// Middlewares de segurança avançada
app.use(advancedSecurity);
app.use(apiRateLimit);
app.use(speedLimiter);

app.use(
  cors({
    origin: [
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:3002",
      process.env.FRONTEND_URL,
    ].filter(Boolean),
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    optionsSuccessStatus: 200,
  })
);

// Middleware para parsing
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Middleware de detecção de atividade suspeita (após parsing)
app.use(suspiciousActivityDetector);

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Rotas
app.use("/api/auth", authRoutes);
app.use("/api/surveys", surveyRoutes);
app.use("/api/responses", responseRoutes);
app.use("/api/whatsapp", whatsappRoutes);
app.use("/api/conversations", conversationRoutes);
app.use("/api/departments", departmentRoutes);
app.use("/api/notifications", notificationRoutes);
app.use("/api/analytics", analyticsRoutes);
app.use("/api/automation", automationRoutes);
app.use("/api/security", securityRoutes);
app.use("/api/gamification", gamificationRoutes);
app.use("/webhooks", webhookRoutes);

// Rota de health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Rota para servir formulários HTML
app.get(
  "/survey/:id",
  require("./src/controllers/surveyController").renderSurveyForm
);
app.post(
  "/submit-survey",
  require("./src/controllers/surveyController").submitSurveyForm
);

// Middleware de tratamento de erros
app.use(errorHandler);

// Rota 404
app.use("*", (req, res) => {
  res.status(404).json({ error: "Rota não encontrada" });
});

// Inicializar serviços
websocketService.initialize(server);
mlService.initialize();
automationService.initializeAutomation();
gamificationService.initializeGamification();

// Inicialização do servidor
server.listen(PORT, () => {
  logger.info(`🚀 Servidor rodando na porta ${PORT}`);
  logger.info(`📊 Dashboard: http://localhost:${PORT}/health`);
  logger.info(
    `🌐 Frontend: ${process.env.FRONTEND_URL || "http://localhost:3001"}`
  );
  logger.info(`📡 WebSocket habilitado`);
});

// Tratamento de erros não capturados
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

module.exports = app;
