const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const database = require('../config/database');
const { logger } = require('../utils/logger');

class SecurityService {
  constructor() {
    this.encryptionKey = process.env.ENCRYPTION_KEY || this.generateEncryptionKey();
    this.algorithm = 'aes-256-gcm';
    this.keyDerivationIterations = 100000;
  }

  // Gerar chave de criptografia
  generateEncryptionKey() {
    const key = crypto.randomBytes(32).toString('hex');
    logger.warn('⚠️ Chave de criptografia gerada automaticamente. Configure ENCRYPTION_KEY no .env para produção');
    return key;
  }

  // Criptografia end-to-end para mensagens
  encryptMessage(message, userKey = null) {
    try {
      const key = userKey || Buffer.from(this.encryptionKey, 'hex');
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.algorithm, key);
      
      cipher.setAAD(Buffer.from('uniforms-message', 'utf8'));
      
      let encrypted = cipher.update(message, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        algorithm: this.algorithm
      };
    } catch (error) {
      logger.error('Erro na criptografia:', error);
      throw new Error('Falha na criptografia da mensagem');
    }
  }

  // Descriptografia de mensagens
  decryptMessage(encryptedData, userKey = null) {
    try {
      const { encrypted, iv, authTag, algorithm } = encryptedData;
      const key = userKey || Buffer.from(this.encryptionKey, 'hex');
      
      const decipher = crypto.createDecipher(algorithm, key);
      decipher.setAAD(Buffer.from('uniforms-message', 'utf8'));
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('Erro na descriptografia:', error);
      throw new Error('Falha na descriptografia da mensagem');
    }
  }

  // Gerar chave de usuário para E2E
  generateUserKey(userId, password) {
    const salt = crypto.randomBytes(32);
    const key = crypto.pbkdf2Sync(password, salt, this.keyDerivationIterations, 32, 'sha512');
    
    return {
      key: key.toString('hex'),
      salt: salt.toString('hex'),
      iterations: this.keyDerivationIterations
    };
  }

  // Derivar chave de usuário
  deriveUserKey(password, salt, iterations) {
    const saltBuffer = Buffer.from(salt, 'hex');
    const key = crypto.pbkdf2Sync(password, saltBuffer, iterations, 32, 'sha512');
    return key;
  }

  // Configurar 2FA para usuário
  async setup2FA(userId) {
    try {
      const user = await database.getClient().user.findUnique({
        where: { id: userId },
        select: { email: true, name: true }
      });

      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Gerar secret
      const secret = speakeasy.generateSecret({
        name: `UNIFORMS2 (${user.email})`,
        issuer: 'UNIFORMS2 Survey System',
        length: 32
      });

      // Gerar QR Code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

      // Salvar secret temporário (será confirmado após verificação)
      await database.getClient().user.update({
        where: { id: userId },
        data: {
          tempTwoFactorSecret: secret.base32,
          twoFactorEnabled: false
        }
      });

      return {
        secret: secret.base32,
        qrCode: qrCodeUrl,
        manualEntryKey: secret.base32,
        backupCodes: this.generateBackupCodes()
      };
    } catch (error) {
      logger.error('Erro ao configurar 2FA:', error);
      throw error;
    }
  }

  // Verificar e ativar 2FA
  async verify2FA(userId, token) {
    try {
      const user = await database.getClient().user.findUnique({
        where: { id: userId },
        select: { tempTwoFactorSecret: true, twoFactorSecret: true, twoFactorEnabled: true }
      });

      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      const secret = user.tempTwoFactorSecret || user.twoFactorSecret;
      if (!secret) {
        throw new Error('2FA não configurado');
      }

      // Verificar token
      const verified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token,
        window: 2 // Permite 2 intervalos de tempo (60s cada)
      });

      if (!verified) {
        // Log tentativa de 2FA inválida
        await this.logSecurityEvent(userId, 'invalid_2fa_attempt', {
          timestamp: new Date(),
          userAgent: 'system'
        });
        
        return { success: false, message: 'Token inválido' };
      }

      // Se estava configurando, ativar 2FA
      if (user.tempTwoFactorSecret && !user.twoFactorEnabled) {
        await database.getClient().user.update({
          where: { id: userId },
          data: {
            twoFactorSecret: user.tempTwoFactorSecret,
            tempTwoFactorSecret: null,
            twoFactorEnabled: true,
            twoFactorEnabledAt: new Date()
          }
        });

        await this.logSecurityEvent(userId, '2fa_enabled', {
          timestamp: new Date()
        });

        return { success: true, message: '2FA ativado com sucesso' };
      }

      return { success: true, message: 'Token válido' };
    } catch (error) {
      logger.error('Erro na verificação 2FA:', error);
      throw error;
    }
  }

  // Desativar 2FA
  async disable2FA(userId, password) {
    try {
      const user = await database.getClient().user.findUnique({
        where: { id: userId },
        select: { password: true, twoFactorEnabled: true }
      });

      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Verificar senha
      const validPassword = await bcrypt.compare(password, user.password);
      if (!validPassword) {
        await this.logSecurityEvent(userId, 'invalid_password_2fa_disable', {
          timestamp: new Date()
        });
        throw new Error('Senha inválida');
      }

      // Desativar 2FA
      await database.getClient().user.update({
        where: { id: userId },
        data: {
          twoFactorSecret: null,
          tempTwoFactorSecret: null,
          twoFactorEnabled: false,
          twoFactorEnabledAt: null
        }
      });

      await this.logSecurityEvent(userId, '2fa_disabled', {
        timestamp: new Date()
      });

      return { success: true, message: '2FA desativado com sucesso' };
    } catch (error) {
      logger.error('Erro ao desativar 2FA:', error);
      throw error;
    }
  }

  // Gerar códigos de backup
  generateBackupCodes() {
    const codes = [];
    for (let i = 0; i < 10; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return codes;
  }

  // Verificar permissões RBAC
  async checkPermission(userId, resource, action) {
    try {
      const user = await database.getClient().user.findUnique({
        where: { id: userId },
        include: {
          role: {
            include: {
              permissions: true
            }
          }
        }
      });

      if (!user) {
        return false;
      }

      // Admin tem todas as permissões
      if (user.role.name === 'ADMIN') {
        return true;
      }

      // Verificar permissões específicas
      const hasPermission = user.role.permissions.some(permission => 
        permission.resource === resource && 
        permission.action === action
      );

      // Log verificação de permissão
      await this.logSecurityEvent(userId, 'permission_check', {
        resource,
        action,
        granted: hasPermission,
        timestamp: new Date()
      });

      return hasPermission;
    } catch (error) {
      logger.error('Erro na verificação de permissão:', error);
      return false;
    }
  }

  // Detectar atividade suspeita
  async detectSuspiciousActivity(userId, activity) {
    try {
      const {
        action,
        ip,
        userAgent,
        timestamp = new Date(),
        metadata = {}
      } = activity;

      // Buscar atividades recentes do usuário
      const recentActivities = await database.getClient().securityLog.findMany({
        where: {
          userId,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24h
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 100
      });

      const suspiciousIndicators = [];

      // 1. Múltiplos IPs em pouco tempo
      const uniqueIPs = new Set(recentActivities.map(a => a.metadata?.ip).filter(Boolean));
      if (uniqueIPs.size > 5) {
        suspiciousIndicators.push('multiple_ips');
      }

      // 2. Múltiplas tentativas de login falhadas
      const failedLogins = recentActivities.filter(a => 
        a.event === 'login_failed' && 
        a.createdAt > new Date(Date.now() - 60 * 60 * 1000) // Última hora
      );
      if (failedLogins.length >= 5) {
        suspiciousIndicators.push('multiple_failed_logins');
      }

      // 3. Atividade fora do horário normal
      const hour = timestamp.getHours();
      if (hour < 6 || hour > 22) {
        const normalHourActivities = recentActivities.filter(a => {
          const activityHour = new Date(a.createdAt).getHours();
          return activityHour >= 6 && activityHour <= 22;
        });
        
        if (normalHourActivities.length > 10) { // Usuário normalmente ativo em horário comercial
          suspiciousIndicators.push('unusual_hours');
        }
      }

      // 4. Mudança súbita de localização (baseado em IP)
      if (ip && recentActivities.length > 0) {
        const lastIP = recentActivities[0]?.metadata?.ip;
        if (lastIP && lastIP !== ip) {
          // Em produção, usar serviço de geolocalização
          suspiciousIndicators.push('location_change');
        }
      }

      // 5. User-Agent suspeito
      if (userAgent) {
        const suspiciousPatterns = [
          /bot/i,
          /crawler/i,
          /spider/i,
          /curl/i,
          /wget/i,
          /python/i
        ];
        
        if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
          suspiciousIndicators.push('suspicious_user_agent');
        }
      }

      // Se atividade suspeita detectada
      if (suspiciousIndicators.length > 0) {
        await this.logSecurityEvent(userId, 'suspicious_activity_detected', {
          action,
          ip,
          userAgent,
          indicators: suspiciousIndicators,
          riskLevel: this.calculateRiskLevel(suspiciousIndicators),
          timestamp
        });

        // Notificar administradores se risco alto
        const riskLevel = this.calculateRiskLevel(suspiciousIndicators);
        if (riskLevel === 'HIGH') {
          await this.notifySecurityTeam(userId, suspiciousIndicators, activity);
        }

        return {
          suspicious: true,
          indicators: suspiciousIndicators,
          riskLevel,
          action: riskLevel === 'HIGH' ? 'block' : 'monitor'
        };
      }

      return {
        suspicious: false,
        riskLevel: 'LOW'
      };
    } catch (error) {
      logger.error('Erro na detecção de atividade suspeita:', error);
      return { suspicious: false, riskLevel: 'LOW' };
    }
  }

  // Calcular nível de risco
  calculateRiskLevel(indicators) {
    const highRiskIndicators = ['multiple_failed_logins', 'suspicious_user_agent'];
    const mediumRiskIndicators = ['multiple_ips', 'location_change'];

    if (indicators.some(i => highRiskIndicators.includes(i))) {
      return 'HIGH';
    }
    
    if (indicators.length >= 2 || indicators.some(i => mediumRiskIndicators.includes(i))) {
      return 'MEDIUM';
    }

    return 'LOW';
  }

  // Notificar equipe de segurança
  async notifySecurityTeam(userId, indicators, activity) {
    try {
      const user = await database.getClient().user.findUnique({
        where: { id: userId },
        select: { email: true, name: true }
      });

      const notificationService = require('./notificationService');
      
      await notificationService.createNotification({
        type: 'security_alert',
        title: '🚨 Atividade Suspeita Detectada',
        message: `Usuário ${user?.name} (${user?.email}) apresentou atividade suspeita: ${indicators.join(', ')}`,
        data: {
          userId,
          indicators,
          activity,
          timestamp: new Date()
        },
        priority: 'urgent',
        channels: ['websocket', 'database', 'email']
      });

      logger.warn('Atividade suspeita detectada:', {
        userId,
        userEmail: user?.email,
        indicators,
        activity
      });
    } catch (error) {
      logger.error('Erro ao notificar equipe de segurança:', error);
    }
  }

  // Log de eventos de segurança
  async logSecurityEvent(userId, event, metadata = {}) {
    try {
      await database.getClient().securityLog.create({
        data: {
          userId,
          event,
          metadata,
          createdAt: new Date()
        }
      });
    } catch (error) {
      logger.error('Erro ao registrar evento de segurança:', error);
    }
  }

  // Validar força da senha
  validatePasswordStrength(password) {
    const requirements = {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumbers: /\d/.test(password),
      hasSpecialChars: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      noCommonPatterns: !this.isCommonPassword(password)
    };

    const score = Object.values(requirements).filter(Boolean).length;
    
    let strength = 'weak';
    if (score >= 5) strength = 'strong';
    else if (score >= 3) strength = 'medium';

    return {
      score,
      strength,
      requirements,
      isValid: score >= 4 // Mínimo 4 requisitos
    };
  }

  // Verificar senhas comuns
  isCommonPassword(password) {
    const commonPasswords = [
      '123456', 'password', '123456789', '12345678', '12345',
      '1234567', '1234567890', 'qwerty', 'abc123', 'million2',
      '000000', '1234', 'iloveyou', 'aaron431', 'password1',
      'qqww1122', '123', 'omgpop', '123321', '654321'
    ];

    return commonPasswords.includes(password.toLowerCase());
  }

  // Gerar hash seguro da senha
  async hashPassword(password) {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }

  // Verificar senha
  async verifyPassword(password, hash) {
    return await bcrypt.compare(password, hash);
  }

  // Gerar token seguro
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  // Sanitizar entrada do usuário
  sanitizeInput(input) {
    if (typeof input !== 'string') return input;

    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/javascript:/gi, '') // Remove javascript:
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  // Validar e sanitizar dados
  validateAndSanitize(data, schema) {
    const sanitized = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (schema[key]) {
        const fieldSchema = schema[key];
        
        // Sanitizar
        let sanitizedValue = this.sanitizeInput(value);
        
        // Validar tipo
        if (fieldSchema.type === 'email') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(sanitizedValue)) {
            throw new Error(`Email inválido: ${key}`);
          }
        }
        
        // Validar comprimento
        if (fieldSchema.maxLength && sanitizedValue.length > fieldSchema.maxLength) {
          throw new Error(`Campo ${key} muito longo`);
        }
        
        if (fieldSchema.minLength && sanitizedValue.length < fieldSchema.minLength) {
          throw new Error(`Campo ${key} muito curto`);
        }
        
        sanitized[key] = sanitizedValue;
      }
    }
    
    return sanitized;
  }

  // Verificar rate limiting
  async checkRateLimit(identifier, action, limit = 10, windowMs = 60000) {
    try {
      const key = `rate_limit:${action}:${identifier}`;
      const now = Date.now();
      const windowStart = now - windowMs;

      // Buscar tentativas recentes
      const attempts = await database.getClient().rateLimitLog.findMany({
        where: {
          identifier,
          action,
          createdAt: {
            gte: new Date(windowStart)
          }
        }
      });

      if (attempts.length >= limit) {
        // Log tentativa bloqueada
        await this.logSecurityEvent(null, 'rate_limit_exceeded', {
          identifier,
          action,
          attempts: attempts.length,
          limit,
          windowMs
        });

        return {
          allowed: false,
          remaining: 0,
          resetTime: new Date(attempts[0].createdAt.getTime() + windowMs)
        };
      }

      // Registrar tentativa
      await database.getClient().rateLimitLog.create({
        data: {
          identifier,
          action,
          createdAt: new Date()
        }
      });

      return {
        allowed: true,
        remaining: limit - attempts.length - 1,
        resetTime: new Date(now + windowMs)
      };
    } catch (error) {
      logger.error('Erro no rate limiting:', error);
      return { allowed: true, remaining: limit - 1 }; // Falha aberta
    }
  }

  // Limpar logs antigos
  async cleanupOldLogs(daysToKeep = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const [securityResult, rateLimitResult] = await Promise.all([
        database.getClient().securityLog.deleteMany({
          where: {
            createdAt: { lt: cutoffDate }
          }
        }),
        database.getClient().rateLimitLog.deleteMany({
          where: {
            createdAt: { lt: cutoffDate }
          }
        })
      ]);

      logger.info('Logs de segurança limpos:', {
        securityLogs: securityResult.count,
        rateLimitLogs: rateLimitResult.count,
        cutoffDate
      });

      return {
        securityLogs: securityResult.count,
        rateLimitLogs: rateLimitResult.count
      };
    } catch (error) {
      logger.error('Erro na limpeza de logs:', error);
      throw error;
    }
  }

  // Obter relatório de segurança
  async getSecurityReport(period = '30d') {
    try {
      const now = new Date();
      const days = { '7d': 7, '30d': 30, '90d': 90 }[period] || 30;
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      const [
        securityEvents,
        suspiciousActivities,
        rateLimitViolations,
        twoFactorStats
      ] = await Promise.all([
        database.getClient().securityLog.groupBy({
          by: ['event'],
          where: { createdAt: { gte: startDate } },
          _count: { id: true }
        }),
        database.getClient().securityLog.count({
          where: {
            event: 'suspicious_activity_detected',
            createdAt: { gte: startDate }
          }
        }),
        database.getClient().rateLimitLog.groupBy({
          by: ['action'],
          where: { createdAt: { gte: startDate } },
          _count: { id: true }
        }),
        database.getClient().user.groupBy({
          by: ['twoFactorEnabled'],
          _count: { id: true }
        })
      ]);

      return {
        period: { start: startDate, end: now, days },
        events: securityEvents.map(e => ({
          event: e.event,
          count: e._count.id
        })),
        suspiciousActivities,
        rateLimitViolations: rateLimitViolations.map(r => ({
          action: r.action,
          count: r._count.id
        })),
        twoFactorAdoption: {
          enabled: twoFactorStats.find(s => s.twoFactorEnabled)?._count.id || 0,
          disabled: twoFactorStats.find(s => !s.twoFactorEnabled)?._count.id || 0
        }
      };
    } catch (error) {
      logger.error('Erro ao gerar relatório de segurança:', error);
      throw error;
    }
  }
}

module.exports = new SecurityService();
