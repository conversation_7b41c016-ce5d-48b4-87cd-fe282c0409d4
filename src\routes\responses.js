const express = require('express');
const { validate, responseSchemas } = require('../utils/validation');
const { authenticate, authorize } = require('../middleware/auth');
const {
  getResponses,
  getResponse,
  createResponse,
  getAnalytics
} = require('../controllers/responseController');

const router = express.Router();

// Todas as rotas de respostas requerem autenticação
router.use(authenticate);

// Obter analytics e métricas
router.get('/analytics', getAnalytics);

// Listar respostas
router.get('/', getResponses);

// Obter resposta específica
router.get('/:id', getResponse);

// Criar resposta via API
router.post('/', 
  validate(responseSchemas.create),
  createResponse
);

// Marcar resposta como lida/não lida
router.patch('/:id/read', async (req, res) => {
  try {
    const { id } = req.params;
    const { isRead = true } = req.body;
    const userId = req.user.id;
    const database = require('../config/database');
    const { logger } = require('../utils/logger');

    // Verificar se a resposta pertence ao usuário
    const response = await database.getClient().response.findFirst({
      where: { 
        id,
        survey: { userId }
      }
    });

    if (!response) {
      return res.status(404).json({
        error: 'Resposta não encontrada',
        message: 'A resposta solicitada não foi encontrada'
      });
    }

    // Atualizar status (assumindo que você adicione um campo isRead no schema)
    // Por enquanto, vamos apenas logar a ação
    logger.info('Resposta marcada como lida:', { responseId: id, isRead });

    res.json({
      message: `Resposta marcada como ${isRead ? 'lida' : 'não lida'}`,
      responseId: id
    });

  } catch (error) {
    logger.error('Erro ao marcar resposta como lida:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível atualizar o status da resposta'
    });
  }
});

// Adicionar nota à resposta
router.post('/:id/notes', async (req, res) => {
  try {
    const { id } = req.params;
    const { note } = req.body;
    const userId = req.user.id;
    const database = require('../config/database');
    const { logger } = require('../utils/logger');

    if (!note || note.trim().length === 0) {
      return res.status(400).json({
        error: 'Nota inválida',
        message: 'A nota não pode estar vazia'
      });
    }

    // Verificar se a resposta pertence ao usuário
    const response = await database.getClient().response.findFirst({
      where: { 
        id,
        survey: { userId }
      }
    });

    if (!response) {
      return res.status(404).json({
        error: 'Resposta não encontrada',
        message: 'A resposta solicitada não foi encontrada'
      });
    }

    // Por enquanto, vamos apenas logar a nota
    // Em uma implementação completa, você criaria uma tabela de notas
    logger.info('Nota adicionada à resposta:', { 
      responseId: id, 
      note: note.substring(0, 100),
      userId 
    });

    res.json({
      message: 'Nota adicionada com sucesso',
      responseId: id,
      note
    });

  } catch (error) {
    logger.error('Erro ao adicionar nota:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível adicionar a nota'
    });
  }
});

// Exportar respostas para CSV
router.get('/export/csv', async (req, res) => {
  try {
    const { surveyId, startDate, endDate } = req.query;
    const userId = req.user.id;
    const database = require('../config/database');

    // Construir filtros
    const where = {
      survey: { userId },
      ...(surveyId && { surveyId }),
      ...(startDate && endDate && {
        createdAt: {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      })
    };

    // Buscar respostas
    const responses = await database.getClient().response.findMany({
      where,
      include: {
        survey: {
          select: {
            title: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Gerar CSV
    const csvHeader = 'ID,Pesquisa,Telefone,Score,Feedback,Tipo,Data\n';
    const csvRows = responses.map(response => {
      const type = response.isPromoter ? 'Promotor' : 
                   response.isNeutral ? 'Neutro' : 
                   response.isDetractor ? 'Detrator' : 'N/A';
      
      return [
        response.id,
        `"${response.survey.title}"`,
        response.userPhone,
        response.score || '',
        `"${(response.feedback || '').replace(/"/g, '""')}"`,
        type,
        response.createdAt.toISOString().split('T')[0]
      ].join(',');
    }).join('\n');

    const csv = csvHeader + csvRows;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="respostas.csv"');
    res.send(csv);

  } catch (error) {
    logger.error('Erro ao exportar CSV:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível exportar os dados'
    });
  }
});

// Obter resumo de respostas por pesquisa
router.get('/summary/by-survey', async (req, res) => {
  try {
    const userId = req.user.id;
    const database = require('../config/database');

    const surveys = await database.getClient().survey.findMany({
      where: { userId },
      select: {
        id: true,
        title: true,
        isActive: true,
        createdAt: true,
        _count: {
          select: { responses: true }
        },
        responses: {
          select: {
            isPromoter: true,
            isNeutral: true,
            isDetractor: true,
            score: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const summary = surveys.map(survey => {
      const responses = survey.responses;
      const totalResponses = responses.length;
      const promoters = responses.filter(r => r.isPromoter).length;
      const neutrals = responses.filter(r => r.isNeutral).length;
      const detractors = responses.filter(r => r.isDetractor).length;
      
      const npsScore = totalResponses > 0 
        ? Math.round(((promoters - detractors) / totalResponses) * 100)
        : 0;

      const avgScore = responses.length > 0 
        ? responses.reduce((sum, r) => sum + (r.score || 0), 0) / responses.filter(r => r.score !== null).length
        : 0;

      return {
        id: survey.id,
        title: survey.title,
        isActive: survey.isActive,
        createdAt: survey.createdAt,
        stats: {
          totalResponses,
          npsScore,
          averageScore: Math.round(avgScore * 10) / 10,
          distribution: {
            promoters,
            neutrals,
            detractors
          }
        }
      };
    });

    res.json({ surveys: summary });

  } catch (error) {
    logger.error('Erro ao obter resumo por pesquisa:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter o resumo'
    });
  }
});

module.exports = router;
