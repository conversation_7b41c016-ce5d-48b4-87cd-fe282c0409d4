# Makefile para UNIFORMS2 Survey System
# Facilita comandos comuns de desenvolvimento e deploy

.PHONY: help install dev build start stop restart logs clean test backup restore

# Variáveis
COMPOSE_FILE = docker-compose.yml
COMPOSE_DEV = docker-compose.override.yml
COMPOSE_PROD = docker-compose.prod.yml
PROJECT_NAME = uniforms-survey-system

# Cores para output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

# Help
help: ## Mostrar esta ajuda
	@echo "$(BLUE)UNIFORMS2 Survey System - Comandos Disponíveis$(NC)"
	@echo "=================================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Instalação e Setup
install: ## Instalar dependências e configurar projeto
	@echo "$(BLUE)🚀 Instalando UNIFORMS2...$(NC)"
	@chmod +x install.sh
	@./install.sh

setup-env: ## Configurar arquivo .env
	@echo "$(BLUE)⚙️  Configurando ambiente...$(NC)"
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(GREEN)✅ Arquivo .env criado$(NC)"; \
	else \
		echo "$(YELLOW)⚠️  Arquivo .env já existe$(NC)"; \
	fi

# Desenvolvimento
dev: ## Iniciar em modo desenvolvimento
	@echo "$(BLUE)🔧 Iniciando modo desenvolvimento...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV) up -d
	@echo "$(GREEN)✅ Serviços iniciados:$(NC)"
	@echo "  📊 Backend: http://localhost:3000"
	@echo "  🌐 Frontend: http://localhost:3001"
	@echo "  📱 Evolution API: http://localhost:8080"
	@echo "  🔄 n8n: http://localhost:5678"
	@echo "  🗄️  Adminer: http://localhost:8081"
	@echo "  📮 Redis Commander: http://localhost:8082"

dev-logs: ## Ver logs do desenvolvimento
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV) logs -f

dev-stop: ## Parar desenvolvimento
	@echo "$(YELLOW)🛑 Parando desenvolvimento...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV) down

# Produção
build: ## Build das imagens para produção
	@echo "$(BLUE)🔨 Fazendo build para produção...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD) build --no-cache

start: ## Iniciar em modo produção
	@echo "$(BLUE)🚀 Iniciando modo produção...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD) up -d
	@echo "$(GREEN)✅ Sistema em produção iniciado$(NC)"

stop: ## Parar todos os serviços
	@echo "$(YELLOW)🛑 Parando todos os serviços...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD) down
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV) down

restart: stop start ## Reiniciar sistema

# Logs e Monitoramento
logs: ## Ver logs de todos os serviços
	@docker-compose -f $(COMPOSE_FILE) logs -f

logs-backend: ## Ver logs do backend
	@docker-compose -f $(COMPOSE_FILE) logs -f backend

logs-postgres: ## Ver logs do PostgreSQL
	@docker-compose -f $(COMPOSE_FILE) logs -f postgres

logs-redis: ## Ver logs do Redis
	@docker-compose -f $(COMPOSE_FILE) logs -f redis

logs-evolution: ## Ver logs da Evolution API
	@docker-compose -f $(COMPOSE_FILE) logs -f evolution-api

# Banco de Dados
db-migrate: ## Executar migrações do banco
	@echo "$(BLUE)🗄️  Executando migrações...$(NC)"
	@docker-compose exec backend npm run prisma:migrate

db-seed: ## Popular banco com dados iniciais
	@echo "$(BLUE)🌱 Populando banco com dados iniciais...$(NC)"
	@docker-compose exec backend npm run prisma:seed

db-reset: ## Resetar banco de dados
	@echo "$(RED)⚠️  ATENÇÃO: Isso irá apagar todos os dados!$(NC)"
	@read -p "Tem certeza? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker-compose exec backend npm run prisma:reset

db-studio: ## Abrir Prisma Studio
	@echo "$(BLUE)🎨 Abrindo Prisma Studio...$(NC)"
	@docker-compose exec backend npx prisma studio

# Backup e Restore
backup: ## Fazer backup do banco de dados
	@echo "$(BLUE)💾 Fazendo backup...$(NC)"
	@mkdir -p backups
	@docker-compose exec postgres pg_dump -U uniforms_user uniforms_survey > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✅ Backup criado em backups/$(NC)"

restore: ## Restaurar backup (especificar BACKUP_FILE=arquivo.sql)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "$(RED)❌ Especifique o arquivo: make restore BACKUP_FILE=backup.sql$(NC)"; \
		exit 1; \
	fi
	@echo "$(BLUE)🔄 Restaurando backup $(BACKUP_FILE)...$(NC)"
	@docker-compose exec -T postgres psql -U uniforms_user uniforms_survey < $(BACKUP_FILE)
	@echo "$(GREEN)✅ Backup restaurado$(NC)"

# Testes
test: ## Executar testes
	@echo "$(BLUE)🧪 Executando testes...$(NC)"
	@docker-compose exec backend npm test

test-watch: ## Executar testes em modo watch
	@docker-compose exec backend npm run test:watch

test-coverage: ## Executar testes com coverage
	@docker-compose exec backend npm run test:coverage

# Limpeza
clean: ## Limpar containers, volumes e imagens não utilizadas
	@echo "$(YELLOW)🧹 Limpando recursos Docker...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV) down -v --remove-orphans
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD) down -v --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)✅ Limpeza concluída$(NC)"

clean-all: ## Limpar tudo (CUIDADO: remove volumes com dados)
	@echo "$(RED)⚠️  ATENÇÃO: Isso irá remover TODOS os dados!$(NC)"
	@read -p "Tem certeza? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_DEV) down -v --remove-orphans
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD) down -v --remove-orphans
	@docker system prune -af --volumes
	@echo "$(GREEN)✅ Limpeza completa concluída$(NC)"

# Utilitários
shell-backend: ## Abrir shell no container do backend
	@docker-compose exec backend bash

shell-postgres: ## Abrir shell no PostgreSQL
	@docker-compose exec postgres psql -U uniforms_user uniforms_survey

shell-redis: ## Abrir shell no Redis
	@docker-compose exec redis redis-cli

status: ## Mostrar status dos containers
	@echo "$(BLUE)📊 Status dos containers:$(NC)"
	@docker-compose ps

health: ## Verificar saúde dos serviços
	@echo "$(BLUE)🏥 Verificando saúde dos serviços...$(NC)"
	@curl -f http://localhost:3000/health || echo "$(RED)❌ Backend não está saudável$(NC)"
	@curl -f http://localhost:3001 || echo "$(RED)❌ Frontend não está acessível$(NC)"
	@curl -f http://localhost:8080 || echo "$(RED)❌ Evolution API não está acessível$(NC)"

# Deploy
deploy-staging: ## Deploy para staging
	@echo "$(BLUE)🚀 Deploy para staging...$(NC)"
	@git push origin develop
	@echo "$(GREEN)✅ Deploy para staging iniciado$(NC)"

deploy-prod: ## Deploy para produção
	@echo "$(BLUE)🚀 Deploy para produção...$(NC)"
	@git push origin main
	@echo "$(GREEN)✅ Deploy para produção iniciado$(NC)"

# Monitoramento
monitor: ## Abrir dashboards de monitoramento
	@echo "$(BLUE)📊 Abrindo dashboards...$(NC)"
	@echo "  📈 Grafana: http://localhost:3001"
	@echo "  🔍 Prometheus: http://localhost:9090"
	@open http://localhost:3001 2>/dev/null || xdg-open http://localhost:3001 2>/dev/null || echo "Abra manualmente: http://localhost:3001"

# Informações
info: ## Mostrar informações do sistema
	@echo "$(BLUE)ℹ️  Informações do Sistema$(NC)"
	@echo "=========================="
	@echo "Projeto: $(PROJECT_NAME)"
	@echo "Docker Compose: $(shell docker-compose version --short)"
	@echo "Docker: $(shell docker version --format '{{.Server.Version}}')"
	@echo ""
	@echo "$(BLUE)📊 Uso de recursos:$(NC)"
	@docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Comandos rápidos
quick-start: setup-env dev db-migrate db-seed ## Setup completo e início rápido
	@echo "$(GREEN)🎉 UNIFORMS2 está pronto!$(NC)"
	@echo "  📊 Backend: http://localhost:3000"
	@echo "  🌐 Frontend: http://localhost:3001"
	@echo "  👤 Login: <EMAIL> / admin123"
