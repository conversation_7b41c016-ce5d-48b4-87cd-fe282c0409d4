import React, { createContext, useContext, useReducer, useEffect } from "react";
import { authAPI } from "../services/api";
import { toast } from "react-hot-toast";

// Estado inicial
const initialState = {
  user: null,
  token: localStorage.getItem("token"),
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Actions
const AUTH_ACTIONS = {
  LOGIN_START: "LOGIN_START",
  LOGIN_SUCCESS: "LOGIN_SUCCESS",
  LOGIN_FAILURE: "LOGIN_FAILURE",
  LOGOUT: "LOGOUT",
  LOAD_USER_START: "LOAD_USER_START",
  LOAD_USER_SUCCESS: "LOAD_USER_SUCCESS",
  LOAD_USER_FAILURE: "LOAD_USER_FAILURE",
  UPDATE_USER: "UPDATE_USER",
  CLEAR_ERROR: "CLEAR_ERROR",
};

// Reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
    case AUTH_ACTIONS.LOAD_USER_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.LOAD_USER_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
    case AUTH_ACTIONS.LOAD_USER_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
}

// Context
const AuthContext = createContext();

// Provider
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Carregar usuário ao inicializar
  useEffect(() => {
    if (state.token) {
      loadUser();
    } else {
      dispatch({ type: AUTH_ACTIONS.LOAD_USER_FAILURE, payload: null });
    }
  }, [state.token]);

  // Configurar token no axios quando mudar
  useEffect(() => {
    if (state.token) {
      authAPI.defaults.headers.common["Authorization"] =
        `Bearer ${state.token}`;
      localStorage.setItem("token", state.token);
    } else {
      delete authAPI.defaults.headers.common["Authorization"];
      localStorage.removeItem("token");
    }
  }, [state.token]);

  // Carregar dados do usuário
  const loadUser = async () => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOAD_USER_START });

      const response = await authAPI.get("/auth/profile");

      dispatch({
        type: AUTH_ACTIONS.LOAD_USER_SUCCESS,
        payload: { user: response.data.user },
      });
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
      dispatch({
        type: AUTH_ACTIONS.LOAD_USER_FAILURE,
        payload: error.response?.data?.message || "Erro ao carregar usuário",
      });
    }
  };

  // Login
  const login = async (email, password) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      const response = await authAPI.post("/auth/login", { email, password });

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: {
          user: response.data.user,
          token: response.data.token,
        },
      });

      toast.success("Login realizado com sucesso!");
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || "Erro ao fazer login";

      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: message,
      });

      toast.error(message);
      return { success: false, error: message };
    }
  };

  // Registro
  const register = async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      const response = await authAPI.post("/auth/register", userData);

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: {
          user: response.data.user,
          token: response.data.token,
        },
      });

      toast.success("Conta criada com sucesso!");
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || "Erro ao criar conta";

      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: message,
      });

      toast.error(message);
      return { success: false, error: message };
    }
  };

  // Logout
  const logout = () => {
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
    toast.success("Logout realizado com sucesso!");
  };

  // Atualizar perfil
  const updateProfile = async (userData) => {
    try {
      const response = await authAPI.put("/auth/profile", userData);

      dispatch({
        type: AUTH_ACTIONS.UPDATE_USER,
        payload: response.data.user,
      });

      toast.success("Perfil atualizado com sucesso!");
      return { success: true };
    } catch (error) {
      const message =
        error.response?.data?.message || "Erro ao atualizar perfil";
      toast.error(message);
      return { success: false, error: message };
    }
  };

  // Alterar senha
  const changePassword = async (currentPassword, newPassword) => {
    try {
      await authAPI.post("/auth/change-password", {
        currentPassword,
        newPassword,
      });

      toast.success("Senha alterada com sucesso!");
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || "Erro ao alterar senha";
      toast.error(message);
      return { success: false, error: message };
    }
  };

  // Limpar erro
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Verificar se usuário tem permissão
  const hasPermission = (requiredRole) => {
    if (!state.user) return false;

    const roleHierarchy = {
      USER: 1,
      AGENT: 2,
      ADMIN: 3,
    };

    const userLevel = roleHierarchy[state.user.role] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  };

  // Verificar se usuário é admin
  const isAdmin = () => hasPermission("ADMIN");

  // Verificar se usuário é agente
  const isAgent = () => hasPermission("AGENT");

  const value = {
    // Estado
    ...state,

    // Ações
    login,
    register,
    logout,
    loadUser,
    updateProfile,
    changePassword,
    clearError,

    // Utilitários
    hasPermission,
    isAdmin,
    isAgent,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook para usar o contexto
export function useAuth() {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth deve ser usado dentro de um AuthProvider");
  }

  return context;
}
