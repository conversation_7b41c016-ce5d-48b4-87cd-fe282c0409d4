const { PrismaClient } = require('@prisma/client');
const { logger } = require('../utils/logger');

class Database {
  constructor() {
    this.prisma = new PrismaClient({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });

    // Event listeners para logs
    this.prisma.$on('query', (e) => {
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Query:', {
          query: e.query,
          params: e.params,
          duration: `${e.duration}ms`
        });
      }
    });

    this.prisma.$on('error', (e) => {
      logger.error('Database Error:', e);
    });

    this.prisma.$on('info', (e) => {
      logger.info('Database Info:', e);
    });

    this.prisma.$on('warn', (e) => {
      logger.warn('Database Warning:', e);
    });
  }

  async connect() {
    try {
      await this.prisma.$connect();
      logger.success('✅ Conectado ao banco de dados PostgreSQL');
    } catch (error) {
      logger.error('❌ Erro ao conectar com o banco de dados:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      await this.prisma.$disconnect();
      logger.info('🔌 Desconectado do banco de dados');
    } catch (error) {
      logger.error('Erro ao desconectar do banco de dados:', error);
    }
  }

  async healthCheck() {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      logger.error('Database health check failed:', error);
      return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
    }
  }

  getClient() {
    return this.prisma;
  }
}

// Singleton instance
const database = new Database();

// Conectar automaticamente
database.connect().catch((error) => {
  logger.error('Falha na inicialização do banco de dados:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Recebido SIGINT, desconectando do banco de dados...');
  await database.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Recebido SIGTERM, desconectando do banco de dados...');
  await database.disconnect();
  process.exit(0);
});

module.exports = database;
