const Joi = require('joi');

// Esquemas de validação para autenticação
const authSchemas = {
  register: Joi.object({
    name: Joi.string()
      .min(2)
      .max(100)
      .required()
      .messages({
        'string.min': 'Nome deve ter pelo menos 2 caracteres',
        'string.max': 'Nome deve ter no máximo 100 caracteres',
        'any.required': 'Nome é obrigatório'
      }),
    
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Email deve ter um formato válido',
        'any.required': 'Email é obrigatório'
      }),
    
    password: Joi.string()
      .min(6)
      .max(100)
      .required()
      .messages({
        'string.min': 'Senha deve ter pelo menos 6 caracteres',
        'string.max': 'Senha deve ter no máximo 100 caracteres',
        'any.required': 'Senha é obrigatória'
      }),
    
    role: Joi.string()
      .valid('USER', 'ADMIN', 'AGENT')
      .default('USER')
  }),

  login: Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Email deve ter um formato válido',
        'any.required': 'Email é obrigatório'
      }),
    
    password: Joi.string()
      .required()
      .messages({
        'any.required': 'Senha é obrigatória'
      })
  })
};

// Esquemas de validação para pesquisas
const surveySchemas = {
  create: Joi.object({
    title: Joi.string()
      .min(3)
      .max(200)
      .required()
      .messages({
        'string.min': 'Título deve ter pelo menos 3 caracteres',
        'string.max': 'Título deve ter no máximo 200 caracteres',
        'any.required': 'Título é obrigatório'
      }),
    
    description: Joi.string()
      .max(1000)
      .allow('')
      .messages({
        'string.max': 'Descrição deve ter no máximo 1000 caracteres'
      }),
    
    questions: Joi.array()
      .items(
        Joi.object({
          id: Joi.number().required(),
          type: Joi.string()
            .valid('nps', 'text', 'multiple_choice', 'rating', 'yes_no')
            .required(),
          question: Joi.string().min(5).max(500).required(),
          required: Joi.boolean().default(false),
          options: Joi.array().items(Joi.string()).when('type', {
            is: 'multiple_choice',
            then: Joi.required(),
            otherwise: Joi.optional()
          })
        })
      )
      .min(1)
      .required()
      .messages({
        'array.min': 'Deve haver pelo menos uma pergunta',
        'any.required': 'Perguntas são obrigatórias'
      }),
    
    isActive: Joi.boolean().default(true)
  }),

  update: Joi.object({
    title: Joi.string().min(3).max(200),
    description: Joi.string().max(1000).allow(''),
    questions: Joi.array().items(
      Joi.object({
        id: Joi.number().required(),
        type: Joi.string().valid('nps', 'text', 'multiple_choice', 'rating', 'yes_no').required(),
        question: Joi.string().min(5).max(500).required(),
        required: Joi.boolean().default(false),
        options: Joi.array().items(Joi.string()).when('type', {
          is: 'multiple_choice',
          then: Joi.required(),
          otherwise: Joi.optional()
        })
      })
    ).min(1),
    isActive: Joi.boolean()
  }).min(1)
};

// Esquemas de validação para respostas
const responseSchemas = {
  create: Joi.object({
    surveyId: Joi.string().uuid().required(),
    userPhone: Joi.string()
      .pattern(/^[1-9]\d{1,14}$/)
      .required()
      .messages({
        'string.pattern.base': 'Número de telefone deve ter formato válido'
      }),
    score: Joi.number().min(0).max(10).when('feedback', {
      is: Joi.exist(),
      then: Joi.optional(),
      otherwise: Joi.required()
    }),
    feedback: Joi.string().max(2000).allow(''),
    answers: Joi.object().pattern(
      Joi.number(),
      Joi.alternatives().try(
        Joi.string(),
        Joi.number(),
        Joi.boolean(),
        Joi.array().items(Joi.string())
      )
    )
  })
};

// Esquemas de validação para departamentos
const departmentSchemas = {
  create: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    keywords: Joi.array().items(Joi.string().min(2).max(50)).min(1).required(),
    description: Joi.string().max(500).allow(''),
    isActive: Joi.boolean().default(true)
  }),

  update: Joi.object({
    name: Joi.string().min(2).max(100),
    keywords: Joi.array().items(Joi.string().min(2).max(50)).min(1),
    description: Joi.string().max(500).allow(''),
    isActive: Joi.boolean()
  }).min(1)
};

// Middleware de validação
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      return res.status(400).json({
        error: 'Dados inválidos',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }

    req.body = value;
    next();
  };
};

module.exports = {
  authSchemas,
  surveySchemas,
  responseSchemas,
  departmentSchemas,
  validate
};
