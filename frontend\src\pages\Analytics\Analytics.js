import React from 'react';
import { useQuery } from 'react-query';

const Analytics = () => {
  // Fetch analytics data
  const { data: analyticsData, isLoading } = useQuery(
    'analytics',
    async () => {
      const [responsesRes, surveysRes] = await Promise.all([
        fetch('/api/responses', {
          headers: { 'Authorization': `Bear<PERSON> ${localStorage.getItem('authToken')}` }
        }),
        fetch('/api/surveys', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
        })
      ]);

      const responses = responsesRes.ok ? (await responsesRes.json()).responses || [] : [];
      const surveys = surveysRes.ok ? (await surveysRes.json()).surveys || [] : [];

      // Calculate NPS
      const scores = responses.map(r => r.score || 0);
      const promoters = scores.filter(s => s >= 9).length;
      const detractors = scores.filter(s => s <= 6).length;
      const nps = scores.length > 0 ? Math.round(((promoters - detractors) / scores.length) * 100) : 0;

      return {
        responses,
        surveys,
        nps,
        promoters,
        detractors,
        neutrals: scores.filter(s => s >= 7 && s <= 8).length,
        totalResponses: responses.length,
        responseRate: 85 // Mock data
      };
    },
    {
      retry: 1,
      refetchOnWindowFocus: false
    }
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const data = analyticsData || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
        <p className="text-gray-600">Análise detalhada dos dados de satisfação</p>
      </div>

      {/* KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">NPS Score</p>
              <p className="text-3xl font-bold text-blue-600">{data.nps || 0}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Taxa de Resposta</p>
              <p className="text-3xl font-bold text-green-600">{data.responseRate || 0}%</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Promotores</p>
              <p className="text-3xl font-bold text-emerald-600">{data.promoters || 0}</p>
            </div>
            <div className="p-3 bg-emerald-100 rounded-full">
              <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Detratores</p>
              <p className="text-3xl font-bold text-red-600">{data.detractors || 0}</p>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 13l3 3 7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Placeholder */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Evolução do NPS</h3>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p className="text-gray-500">Gráfico de evolução do NPS</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribuição de Notas</h3>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
              </svg>
              <p className="text-gray-500">Gráfico de distribuição de notas</p>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Analysis */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Análise Detalhada</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Principais Feedbacks Positivos</h4>
            <div className="space-y-2">
              {data.responses?.filter(r => (r.score || 0) >= 9 && r.feedback)
                .slice(0, 3)
                .map((response, index) => (
                  <div key={index} className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm text-gray-700">"{response.feedback}"</p>
                    <p className="text-xs text-gray-500 mt-1">Nota: {response.score}</p>
                  </div>
                )) || (
                <p className="text-gray-500 text-sm">Nenhum feedback positivo encontrado</p>
              )}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Principais Feedbacks Negativos</h4>
            <div className="space-y-2">
              {data.responses?.filter(r => (r.score || 0) <= 6 && r.feedback)
                .slice(0, 3)
                .map((response, index) => (
                  <div key={index} className="p-3 bg-red-50 rounded-lg">
                    <p className="text-sm text-gray-700">"{response.feedback}"</p>
                    <p className="text-xs text-gray-500 mt-1">Nota: {response.score}</p>
                  </div>
                )) || (
                <p className="text-gray-500 text-sm">Nenhum feedback negativo encontrado</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo Executivo</h3>
        <div className="prose max-w-none">
          <p className="text-gray-700">
            Com base em <strong>{data.totalResponses || 0} respostas</strong> coletadas, 
            seu NPS atual é de <strong>{data.nps || 0}</strong>. 
            {data.nps >= 70 && " Excelente resultado! Seus clientes são verdadeiros promotores da marca."}
            {data.nps >= 50 && data.nps < 70 && " Bom resultado! Há oportunidades de melhoria para aumentar a satisfação."}
            {data.nps >= 0 && data.nps < 50 && " Resultado que precisa de atenção. Foque em melhorar a experiência do cliente."}
            {data.nps < 0 && " Resultado crítico. É urgente revisar os processos e melhorar a satisfação."}
          </p>
          
          <div className="mt-4 grid grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{data.promoters || 0}</p>
              <p className="text-sm text-gray-600">Promotores</p>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <p className="text-2xl font-bold text-yellow-600">{data.neutrals || 0}</p>
              <p className="text-sm text-gray-600">Neutros</p>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <p className="text-2xl font-bold text-red-600">{data.detractors || 0}</p>
              <p className="text-sm text-gray-600">Detratores</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
